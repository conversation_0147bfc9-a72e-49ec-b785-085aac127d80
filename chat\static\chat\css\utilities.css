/* utilities.css - Utility classes for the application */

/* Display utilities */
.d-flex {
    display: flex;
}

.d-block {
    display: block;
}

.d-none {
    display: none;
}

.d-inline-block {
    display: inline-block;
}

/* Flex utilities */
.flex-row {
    flex-direction: row;
}

.flex-column {
    flex-direction: column;
}

.justify-content-center {
    justify-content: center;
}

.justify-content-between {
    justify-content: space-between;
}

.justify-content-around {
    justify-content: space-around;
}

.align-items-center {
    align-items: center;
}

.align-items-start {
    align-items: flex-start;
}

.align-items-end {
    align-items: flex-end;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-nowrap {
    flex-wrap: nowrap;
}

.flex-1 {
    flex: 1;
}

/* Spacing utilities */
.m-0 {
    margin: 0;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 1rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.ml-1 {
    margin-left: 0.25rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.ml-3 {
    margin-left: 1rem;
}

.mr-1 {
    margin-right: 0.25rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mr-3 {
    margin-right: 1rem;
}

.p-0 {
    padding: 0;
}

.p-1 {
    padding: 0.25rem;
}

.p-2 {
    padding: 0.5rem;
}

.p-3 {
    padding: 1rem;
}

.p-4 {
    padding: 1.5rem;
}

/* Text utilities */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-primary {
    color: var(--text-primary);
}

.text-secondary {
    color: var(--text-secondary);
}

.text-dimmed {
    color: var(--text-dimmed);
}

.text-accent {
    color: var(--accent-primary);
}

.text-danger {
    color: var(--danger-color);
}

.text-success {
    color: var(--success-color);
}

.text-warning {
    color: var(--warning-color);
}

.fw-bold {
    font-weight: bold;
}

.fw-normal {
    font-weight: normal;
}

.fw-light {
    font-weight: 300;
}

.fs-small {
    font-size: 0.8rem;
}

.fs-medium {
    font-size: 1rem;
}

.fs-large {
    font-size: 1.2rem;
}

.fs-xlarge {
    font-size: 1.5rem;
}

/* Border utilities */
.border {
    border: 1px solid var(--border-color);
}

.border-top {
    border-top: 1px solid var(--border-color);
}

.border-bottom {
    border-bottom: 1px solid var(--border-color);
}

.border-left {
    border-left: 1px solid var(--border-color);
}

.border-right {
    border-right: 1px solid var(--border-color);
}

.rounded {
    border-radius: var(--border-radius);
}

.rounded-sm {
    border-radius: var(--border-radius-sm);
}

/* Background utilities */
.bg-primary {
    background-color: var(--bg-primary);
}

.bg-secondary {
    background-color: var(--bg-secondary);
}

.bg-tertiary {
    background-color: var(--bg-tertiary);
}

.bg-transparent {
    background-color: transparent;
}

/* Position utilities */
.position-relative {
    position: relative;
}

.position-absolute {
    position: absolute;
}

.position-fixed {
    position: fixed;
}

/* Width and height utilities */
.w-100 {
    width: 100%;
}

.h-100 {
    height: 100%;
}

.w-50 {
    width: 50%;
}

.h-50 {
    height: 50%;
}

/* Visibility utilities */
.visible {
    visibility: visible;
}

.invisible {
    visibility: hidden;
}

/* Overflow utilities */
.overflow-hidden {
    overflow: hidden;
}

.overflow-auto {
    overflow: auto;
}

.overflow-scroll {
    overflow: scroll;
}

/* Animation utilities - moved to animations.css */

/* Shadow utilities */
.shadow {
    box-shadow: var(--card-shadow);
}

.shadow-sm {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Tooltip styles removed */

/* Animations moved to animations.css */
