/* notebook.css - Styles for the notebook feature */

/* Notebook modal */
.notebook-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--modal-backdrop);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    backdrop-filter: blur(5px);
}

.light-theme .notebook-modal {
    background-color: var(--modal-backdrop-light);
}



.notebook-modal.active {
    opacity: 1;
    visibility: visible;
}

.notebook-content {
    background-color: var(--bg-secondary);
    border-radius: 12px;
    width: 90%;
    max-width: 1100px;
    height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transform: translateY(20px);
    transition: transform 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.notebook-modal.active .notebook-content {
    transform: translateY(0);
}

.notebook-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notebook-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.notebook-header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Delete All Notes Button */
.delete-all-notes-btn {
    gap: 8px;
}

.delete-all-notes-btn i {
    font-size: 0.9rem;
}



/* Generate PDF Button */
.generate-pdf-btn {
    gap: 8px;
    color: var(--text-on-accent);
}

.generate-pdf-btn i {
    font-size: 0.9rem;
}



.close-notebook {
    font-size: 1.5rem;
    line-height: 1;
}

.notebook-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.notebook-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-content: flex-start;
    justify-content: flex-start;
    background-color: var(--bg-primary);
}

/* Notebook body background already defined above */

.notebook-sidebar {
    width: 250px;
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    background-color: var(--bg-tertiary);
}

.notebook-sidebar-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notebook-sidebar-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

/* Add Notebook Button */
.add-notebook-btn {
    width: var(--icon-button-size-lg);
    height: var(--icon-button-size-lg);
    border-radius: 6px;
}



.notebook-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

/* Empty state */
.notebook-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-dimmed);
    text-align: center;
    padding: 20px;
    width: 100%;
}

.notebook-empty i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.notebook-empty p {
    font-size: 1.1rem;
    max-width: 400px;
    line-height: 1.5;
}

/* Message preview cards */
.message-preview {
    width: 300px;
    height: 400px;
    background-color: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    cursor: grab;
}

.message-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.preview-date {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    color: var(--text-dimmed);
}

.preview-date i {
    font-size: 0.9rem;
    opacity: 0.7;
}

.preview-drag-handle {
    color: var(--text-dimmed);
    opacity: 0.5;
    transition: opacity 0.2s ease;
}

.message-preview:hover .preview-drag-handle {
    opacity: 0.8;
}

.preview-content {
    flex: 1;
    padding: 20px;
    overflow: hidden;
    position: relative;
    background-color: var(--bg-primary);
}

.preview-content .message-content {
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-primary);
    height: 100%;
    overflow: hidden;
    mask-image: linear-gradient(to bottom, black 60%, transparent 100%);
    -webkit-mask-image: linear-gradient(to bottom, black 60%, transparent 100%);
}

.preview-actions {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    gap: 10px;
}

.preview-actions-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.preview-button {
    display: flex;
    align-items: center;
    gap: 6px;
    background: none;
    border: none;
    color: var(--text-dimmed);
    cursor: pointer;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.preview-button.icon-only {
    padding: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-button:hover {
    color: var(--text-primary);
    background-color: var(--bg-quaternary);
}

.preview-button i {
    font-size: 0.9rem;
}

.preview-button:not(.icon-only) i {
    font-size: 0.9rem;
}

.preview-button span {
    font-weight: 500;
}

.preview-button.icon-only.delete-btn:hover {
    color: var(--danger-color);
    background-color: rgba(var(--danger-color-rgb), 0.1);
}

.preview-button.move-btn:hover {
    color: var(--accent-primary);
    background-color: rgba(var(--accent-primary-rgb), 0.1);
}

/* Light theme adjustments */
.light-theme .message-preview {
    background-color: var(--bg-secondary);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.light-theme .message-preview:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.light-theme .preview-header,
.light-theme .preview-actions {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
}

.light-theme .preview-content {
    background-color: var(--bg-primary);
}

/* Notification container */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 8px;
    pointer-events: none;
}

#notification-container .notification {
    pointer-events: auto;
}

.notification {
    background-color: rgba(var(--bg-secondary-rgb), 0.7);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transform: translateY(20px);
    animation: notification-slide-in 0.3s forwards;
    border: 1px solid rgba(var(--border-color-rgb), 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    max-width: 300px;
    transition: var(--transition-theme);
}

.light-theme .notification {
    background-color: rgba(var(--bg-secondary-rgb), 0.85);
}



.notification.closing {
    animation: notification-slide-out 0.3s forwards;
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
}

.notification-message {
    color: var(--text-primary);
    font-size: 0.85rem;
    font-weight: 500;
}

.notification-close {
    font-size: 1rem;
    line-height: 1;
    margin-left: 8px;
}

.notification.success {
    border-left: 3px solid var(--accent-primary);
}

.notification.error {
    border-left: 3px solid var(--danger-color);
}

@keyframes notification-slide-in {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes notification-slide-out {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(20px); }
}

/* Notebook items */
.notebook-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 5px;
    cursor: pointer;
    position: relative;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.notebook-item:hover {
    background-color: var(--bg-quaternary);
}

.notebook-item.active {
    background-color: rgba(var(--accent-primary-rgb), 0.15);
    font-weight: 500;
}



.notebook-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
    flex-shrink: 0;
}

.notebook-name {
    flex: 1;
    font-size: 0.9rem;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.notebook-count {
    background-color: var(--bg-quaternary);
    color: var(--text-dimmed);
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
}

.notebook-delete-btn {
    font-size: 0.8rem;
    padding: 5px;
    opacity: 0;
    transition: opacity 0.2s ease, color 0.2s ease, background-color 0.2s ease;
}

.notebook-item:hover .notebook-delete-btn {
    opacity: 1;
}

/* Drop target styling */
.notebook-item.drop-target {
    background-color: rgba(var(--accent-primary-rgb), 0.3);
    box-shadow: 0 0 0 1px var(--accent-primary);
}

/* Drop target styling already defined above for both themes */

/* Message dragging */
.message-preview.dragging {
    opacity: 0.9;
    transform: scale(0.95);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.light-theme .message-preview.dragging {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Move to menu */
.move-to-menu {
    position: absolute;
    background-color: var(--bg-secondary);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color);
    overflow: hidden;
    z-index: 1100;
    min-width: 200px;
    max-width: 250px;
}

.light-theme .move-to-menu {
    background-color: var(--bg-secondary);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
}

.move-to-menu-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.move-to-menu-item:hover {
    background-color: var(--bg-tertiary);
}

.move-to-menu-item .notebook-color {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 10px;
}

.move-to-menu-item.create-notebook {
    border-top: 1px solid var(--border-color);
    color: var(--accent-primary);
}

.move-to-menu-item.create-notebook i {
    margin-right: 8px;
}

.move-to-menu-item.no-notebooks {
    color: var(--text-dimmed);
    font-style: italic;
    cursor: default;
}

.move-to-menu-item.no-notebooks:hover {
    background-color: transparent;
}

.light-theme .move-to-menu-item.create-notebook {
    color: var(--accent-primary);
}

.light-theme .move-to-menu-item.no-notebooks {
    color: var(--text-dimmed);
}

/* Add notebook dialog */
.notebook-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--bg-primary-rgb), 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1100;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Notebook color options - Theme-aware colors */
.notebook-color-0 {
    background-color: var(--accent-primary);
}

.notebook-color-1 {
    background-color: var(--success-color);
}

.notebook-color-2 {
    background-color: var(--warning-color);
}

.notebook-color-3 {
    background-color: var(--danger-color);
}

.notebook-color-4 {
    background-color: var(--accent-secondary);
}

.notebook-color-5 {
    background-color: var(--accent-tertiary);
}

/* Ensure color options inherit theme transitions */
.color-option {
    transition: var(--transition-theme);
}

.light-theme .notebook-dialog {
    background-color: rgba(var(--bg-primary-rgb), 0.3);
}



.notebook-dialog.active {
    opacity: 1;
    visibility: visible;
}

.notebook-dialog-content {
    background-color: var(--bg-secondary);
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transform: translateY(20px);
    transition: transform 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.light-theme .notebook-dialog-content {
    background-color: var(--bg-secondary);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.notebook-dialog.active .notebook-dialog-content {
    transform: translateY(0);
}

.notebook-dialog-content h4 {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.notebook-dialog-form {
    padding: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: border-color 0.2s ease, background-color 0.3s ease, color 0.3s ease;
}

.form-group input:focus {
    border-color: var(--accent-primary);
    outline: none;
}

.light-theme .form-group input {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.color-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.color-option {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
    border: 2px solid transparent;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.selected {
    border-color: var(--text-primary);
    box-shadow: 0 0 0 2px var(--bg-secondary), 0 0 0 4px var(--border-color);
}

.light-theme .color-option.selected {
    border-color: var(--text-primary);
    box-shadow: 0 0 0 2px var(--bg-secondary), 0 0 0 4px var(--border-color);
}

.notebook-dialog-actions {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.notebook-dialog-actions button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.notebook-dialog-actions .cancel-btn {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-dimmed);
}

.notebook-dialog-actions .cancel-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.notebook-dialog-actions .save-btn {
    background-color: var(--accent-primary);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.notebook-dialog-actions .save-btn:hover:not(:disabled) {
    background-color: var(--accent-secondary);
}

.notebook-dialog-actions .save-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Delete dialog specific styles */
.delete-dialog .notebook-dialog-content {
    max-width: 450px;
}

.delete-dialog .notebook-dialog-form p {
    margin: 0 0 10px 0;
    color: var(--text-primary);
    font-size: 0.95rem;
    line-height: 1.5;
}

.delete-dialog .delete-warning {
    color: var(--text-dimmed);
    font-size: 0.85rem;
    font-style: italic;
}

.light-theme .delete-dialog .delete-warning {
    color: var(--text-dimmed);
}

.delete-dialog .delete-current-btn {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.delete-dialog .delete-current-btn:hover {
    background-color: var(--bg-quaternary);
}

.delete-dialog .delete-btn,
.delete-dialog .delete-all-btn {
    background-color: var(--danger-color);
    color: var(--text-white);
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.delete-dialog .delete-btn:hover,
.delete-dialog .delete-all-btn:hover {
    background-color: rgba(var(--danger-color-rgb), 0.8);
}

/* Custom scrollbar for notebook elements */
.notebook-body::-webkit-scrollbar,
.notebook-list::-webkit-scrollbar {
    width: 8px;
}

.notebook-body::-webkit-scrollbar-track,
.notebook-list::-webkit-scrollbar-track {
    background: transparent;
}

.notebook-body::-webkit-scrollbar-thumb,
.notebook-list::-webkit-scrollbar-thumb {
    background-color: var(--bg-quaternary);
    border-radius: 4px;
}



.notebook-body::-webkit-scrollbar-thumb:hover,
.notebook-list::-webkit-scrollbar-thumb:hover {
    background-color: var(--button-dimmed);
}

.light-theme .notebook-body::-webkit-scrollbar-thumb,
.light-theme .notebook-list::-webkit-scrollbar-thumb {
    background-color: var(--bg-quaternary);
}

.light-theme .notebook-body::-webkit-scrollbar-thumb:hover,
.light-theme .notebook-list::-webkit-scrollbar-thumb:hover {
    background-color: var(--button-dimmed);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .notebook-content {
        width: 95%;
        height: 90vh;
    }

    .notebook-container {
        flex-direction: column-reverse;
    }

    .notebook-sidebar {
        width: 100%;
        height: 200px;
        border-left: none;
        border-top: 1px solid var(--border-color);
    }

    .message-preview {
        width: 260px;
        height: 350px;
    }

    .preview-button:not(.icon-only) span {
        display: none;
    }

    .preview-button:not(.icon-only) {
        padding: 6px;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .preview-button i {
        font-size: 1rem;
    }

    .generate-pdf-btn {
        padding: 6px 10px;
    }

    .generate-pdf-btn span {
        display: none;
    }

    .generate-pdf-btn i {
        margin: 0;
    }

    .delete-all-notes-btn {
        padding: 6px 10px;
    }

    .delete-all-notes-btn span {
        display: none;
    }

    .delete-all-notes-btn i {
        margin: 0;
    }
}

.notebook-modal .preview-content .message-content {
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.light-theme .notebook-modal .preview-content .message-content {
    color: var(--text-primary);
}

.notebook-modal .message-content,
.notebook-modal .message-content *,
.preview-content .message-content,
.preview-content .message-content * {
    color: var(--text-primary) !important;
}

.light-theme .notebook-modal .message-content,
.light-theme .notebook-modal .message-content *,
.light-theme .preview-content .message-content,
.light-theme .preview-content .message-content * {
    color: var(--text-primary) !important;
}


