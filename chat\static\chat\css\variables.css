/* variables.css - Central location for all CSS variables */

/* Base theme variables - Dark theme by default */
:root, .dark-theme {
    /* Background colors */
    --bg-primary: #1a1a1a;
    --bg-primary-rgb: 26, 26, 26;
    --bg-secondary: #252525;
    --bg-secondary-rgb: 37, 37, 37;
    --bg-tertiary: #2e2e2e;
    --bg-tertiary-rgb: 46, 46, 46;
    --bg-quaternary: #383838;
    --bg-quaternary-rgb: 56, 56, 56;

    /* Chat-specific darker backgrounds */
    --chat-bg-primary: #0f0f0f; /* Deeper black for main chat area */
    --chat-bg-primary-rgb: 15, 15, 15;
    --chat-bg-secondary: #1a1a1a; /* Darker gray for gradient */
    --chat-bg-secondary-rgb: 26, 26, 26;

    /* Transitions */
    --transition-theme: all 0.3s ease;
    --transition-fast: all 0.2s ease;
    --transition-slow: all 0.5s ease;

    /* Text colors */
    --text-primary: #e8e8e8;
    --text-primary-rgb: 232, 232, 232;
    --text-secondary: #c8c8c8;
    --text-secondary-rgb: 200, 200, 200;
    --text-white: #f5f5f5;
    --text-white-rgb: 245, 245, 245;
    --text-dimmed: #a0a0a0;
    --text-dimmed-rgb: 160, 160, 160;
    --text-response: #d8d8d8;
    --text-response-rgb: 216, 216, 216;

    /* Accent colors */
    --accent-primary: #808080;
    --accent-primary-rgb: 128, 128, 128;
    --accent-secondary: #9e9e9e;
    --accent-secondary-rgb: 158, 158, 158;
    --accent-tertiary: #b8b8b8;
    --accent-tertiary-rgb: 184, 184, 184;
    --button-dimmed: #606060;
    --button-dimmed-rgb: 96, 96, 96;

    /* Status colors */
    --danger-color: #ff4d4d;
    --danger-color-rgb: 255, 77, 77;
    --success-color: #4CAF50;
    --success-color-rgb: 76, 175, 80;
    --warning-color: #FFC107;
    --warning-color-rgb: 255, 193, 7;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #252525, #1a1a1a);
    --gradient-secondary: linear-gradient(135deg, #2e2e2e, #252525);
    --gradient-accent: linear-gradient(135deg, #707070, #909090);

    /* Terminal-specific colors */
    --terminal-bg-primary: #1a1a1a;
    --terminal-bg-secondary: #252525;
    --terminal-header-bg: #2a2a2a;
    --terminal-text: #e8e8e8;
    --terminal-text-dimmed: #a0a0a0;
    --terminal-border: rgba(255, 255, 255, 0.1);

    /* Syntax highlighting colors */
    --syntax-keyword: #569cd6;
    --syntax-string: #4ec9b0;
    --syntax-comment: #6a9955;
    --syntax-number: #ff8c42;
    --syntax-function: #dcdcaa;
    --syntax-variable: #9cdcfe;
    --syntax-background: #1e1e1e;
    --syntax-text: #d4d4d4;
    --syntax-regex: #d16969;
    --syntax-constant: #4fc1ff;
    --syntax-type: #4ec9b0;
    --syntax-decorator: #dcdcaa;
    --syntax-operator: #d4d4d4;

    /* Inline code colors */
    --inline-code-bg: rgba(86, 156, 214, 0.15);
    --inline-code-text: #569cd6;
    --inline-code-border: rgba(86, 156, 214, 0.3);

    /* UI elements */
    --border-color: rgba(255, 255, 255, 0.1);
    --border-radius: 12px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

    /* Typography */
    --font-size: 1rem;
    --font-size-sm: 0.875rem;
    --font-size-xs: 0.75rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-family-primary: 'Inter', Arial, Helvetica, sans-serif;
    --font-family-secondary: 'Nunito', Arial, Helvetica, sans-serif;
    --font-family-code: 'Fira Code', monospace;
    --line-height: 1.6;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;

    /* Common component spacing patterns */
    --button-padding: 8px 14px;
    --button-padding-sm: 6px 10px;
    --button-padding-lg: 10px 20px;
    --icon-button-size: 28px;
    --icon-button-size-lg: 32px;

    /* Particle and effect colors */
    --particle-color-1: rgba(var(--accent-primary-rgb), 0.3);
    --particle-color-2: rgba(var(--accent-secondary-rgb), 0.3);
    --particle-color-3: rgba(var(--accent-tertiary-rgb), 0.3);
    --particle-color-4: rgba(var(--text-dimmed-rgb), 0.2);

    /* Drag and drop colors */
    --drag-image-bg: var(--bg-secondary);
    --drag-image-border: var(--border-color);
    --drag-image-text: var(--text-primary);
}

/* Light theme variables - Professional Minimalist Theme */
.light-theme {
    /* Background colors - Clean white and light grays */
    --bg-primary: #ffffff; /* Clean white */
    --bg-primary-rgb: 255, 255, 255;
    --bg-secondary: #f7f7f7; /* Very light gray */
    --bg-secondary-rgb: 247, 247, 247;
    --bg-tertiary: #f0f0f0; /* Light gray */
    --bg-tertiary-rgb: 240, 240, 240;
    --bg-quaternary: #e8e8e8; /* Slightly darker gray */
    --bg-quaternary-rgb: 232, 232, 232;

    /* Chat-specific darker backgrounds */
    --chat-bg-primary: #e0e0e0; /* Darker gray for main chat area */
    --chat-bg-primary-rgb: 224, 224, 224;
    --chat-bg-secondary: #ebebeb; /* Medium gray for gradient */
    --chat-bg-secondary-rgb: 235, 235, 235;

    /* Text colors - Grayscale for readability */
    --text-primary: #333333; /* Dark gray for main text */
    --text-primary-rgb: 51, 51, 51;
    --text-secondary: #555555; /* Medium gray */
    --text-secondary-rgb: 85, 85, 85;
    --text-white: #444444; /* Dark gray (not black) */
    --text-white-rgb: 68, 68, 68;
    --text-dimmed: #777777; /* Muted gray */
    --text-dimmed-rgb: 119, 119, 119;
    --text-response: #505050; /* Response text */
    --text-response-rgb: 80, 80, 80;

    /* Accent colors - Grayscale */
    --accent-primary: #666666; /* Medium gray */
    --accent-primary-rgb: 102, 102, 102;
    --accent-secondary: #888888; /* Lighter gray */
    --accent-secondary-rgb: 136, 136, 136;
    --accent-tertiary: #aaaaaa; /* Very light gray */
    --accent-tertiary-rgb: 170, 170, 170;
    --button-dimmed: #999999; /* Muted gray */
    --button-dimmed-rgb: 153, 153, 153;

    /* Status colors - Modern tones */
    --danger-color: #dc3545;
    --danger-color-rgb: 220, 53, 69;
    --success-color: #28a745;
    --success-color-rgb: 40, 167, 69;
    --warning-color: #ffc107;
    --warning-color-rgb: 255, 193, 7;

    /* UI elements */
    --border-color: rgba(0, 0, 0, 0.1); /* Subtle gray borders */
    --card-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); /* Soft shadows */

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #f7f7f7, #ffffff); /* White gradient */
    --gradient-secondary: linear-gradient(135deg, #f0f0f0, #f7f7f7); /* Light gray gradient */
    --gradient-accent: linear-gradient(135deg, #666666, #888888); /* Gray gradient */

    /* Terminal-specific colors */
    --terminal-bg-primary: #ffffff;
    --terminal-bg-secondary: #f7f7f7;
    --terminal-header-bg: #f0f0f0;
    --terminal-text: #333333;
    --terminal-text-dimmed: #555555;
    --terminal-border: rgba(0, 0, 0, 0.1);

    /* Syntax highlighting colors */
    --syntax-keyword: #0066cc;
    --syntax-string: #22a522;
    --syntax-comment: #6a737d;
    --syntax-number: #e36209;
    --syntax-function: #6f42c1;
    --syntax-variable: #005cc5;
    --syntax-background: #f5f5f5;
    --syntax-text: #333333;
    --syntax-regex: #d73a49;
    --syntax-constant: #005cc5;
    --syntax-type: #22a522;
    --syntax-decorator: #6f42c1;
    --syntax-operator: #333333;

    /* Inline code colors */
    --inline-code-bg: rgba(0, 0, 0, 0.08);
    --inline-code-text: #333333;
    --inline-code-border: rgba(0, 0, 0, 0.15);

    /* Typography - Professional sans-serif font */
    --font-family-primary: 'Inter', 'Helvetica Neue', Arial, sans-serif;
    --font-family-secondary: 'Roboto', 'Segoe UI', Arial, sans-serif;
    --font-family-code: 'Fira Code', monospace;
    --font-size: 1rem;
    --font-size-sm: 0.875rem;
    --font-size-xs: 0.75rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --line-height: 1.6;

    /* Particle and effect colors */
    --particle-color-1: rgba(var(--accent-primary-rgb), 0.2);
    --particle-color-2: rgba(var(--accent-secondary-rgb), 0.2);
    --particle-color-3: rgba(var(--accent-tertiary-rgb), 0.2);
    --particle-color-4: rgba(var(--text-dimmed-rgb), 0.15);

    /* Drag and drop colors */
    --drag-image-bg: var(--bg-secondary);
    --drag-image-border: var(--border-color);
    --drag-image-text: var(--text-primary);

    /* Common modal/dialog patterns */
    --modal-backdrop: rgba(var(--bg-primary-rgb), 0.5);
    --modal-backdrop-light: rgba(var(--bg-primary-rgb), 0.3);
    --modal-border-radius: 12px;
    --modal-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}