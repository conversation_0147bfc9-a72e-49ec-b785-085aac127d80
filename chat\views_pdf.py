"""
Vistas para la generación de PDFs a partir de notebooks.
"""
import os
import json
import logging
from django.http import JsonResponse, HttpResponse, FileResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings

from .services.pdf_service import (
    generate_pdf_for_notebook,
    get_pdf_status,
    PDF_STORAGE_DIR,
    pdf_generation_status
)

logger = logging.getLogger(__name__)

@login_required
@require_http_methods(["POST"])
def generate_notebook_pdf(request):
    """
    Genera un PDF a partir de un notebook.
    """
    try:
        # Obtener datos del notebook desde el cuerpo de la solicitud
        data = json.loads(request.body)
        notebook_data = data.get('notebook')

        if not notebook_data:
            return JsonResponse({
                'status': 'error',
                'message': 'No se proporcionaron datos del notebook'
            }, status=400)

        # Generar PDF
        result = generate_pdf_for_notebook(notebook_data)

        return JsonResponse({
            'status': result.get('status', 'error'),
            'message': result.get('message', 'Error desconocido'),
            'pdf_url': f"/pdf/{os.path.basename(result.get('pdf_path', ''))}" if result.get('pdf_path') else None
        })

    except Exception as e:
        logger.error(f"Error al generar PDF: {e}")
        return JsonResponse({
            'status': 'error',
            'message': f'Error: {str(e)}'
        }, status=500)

@login_required
@require_http_methods(["GET"])
def get_notebook_pdf_status(request, notebook_id):
    """
    Obtiene el estado de generación de un PDF para un notebook.
    """
    try:
        # Obtener estado
        status = get_pdf_status(notebook_id)

        # Añadir URL del PDF si está completo
        if status.get('status') == 'completed' and status.get('pdf_path'):
            status['pdf_url'] = f"/pdf/{os.path.basename(status.get('pdf_path'))}"

        return JsonResponse(status)

    except Exception as e:
        logger.error(f"Error al obtener estado de PDF: {e}")
        return JsonResponse({
            'status': 'error',
            'message': f'Error: {str(e)}'
        }, status=500)

@login_required
@require_http_methods(["GET"])
def download_notebook_pdf(request, notebook_id):
    """
    Descarga un PDF generado para un notebook.
    """
    try:
        # Construir ruta al archivo PDF
        pdf_filename = f"notebook_{notebook_id}.pdf"
        pdf_path = os.path.join(PDF_STORAGE_DIR, pdf_filename)

        # Verificar si el archivo existe
        if not os.path.exists(pdf_path):
            return JsonResponse({
                'status': 'error',
                'message': 'El PDF solicitado no existe'
            }, status=404)

        # Devolver el archivo como respuesta
        return FileResponse(
            open(pdf_path, 'rb'),
            as_attachment=True,
            filename=pdf_filename
        )

    except Exception as e:
        logger.error(f"Error al descargar PDF: {e}")
        return JsonResponse({
            'status': 'error',
            'message': f'Error: {str(e)}'
        }, status=500)

@login_required
@require_http_methods(["GET"])
def preview_notebook_pdf(request, notebook_id):
    """
    Previsualiza un PDF generado para un notebook.
    """
    try:
        # Construir ruta al archivo PDF
        pdf_filename = f"notebook_{notebook_id}.pdf"
        pdf_path = os.path.join(PDF_STORAGE_DIR, pdf_filename)

        # Verificar si el archivo existe
        if not os.path.exists(pdf_path):
            return JsonResponse({
                'status': 'error',
                'message': 'El PDF solicitado no existe'
            }, status=404)

        # Devolver el archivo como respuesta para visualización
        response = FileResponse(
            open(pdf_path, 'rb'),
            content_type='application/pdf'
        )
        response['Content-Disposition'] = f'inline; filename="{pdf_filename}"'
        return response

    except Exception as e:
        logger.error(f"Error al previsualizar PDF: {e}")
        return JsonResponse({
            'status': 'error',
            'message': f'Error: {str(e)}'
        }, status=500)

@login_required
@require_http_methods(["DELETE"])
def delete_notebook_pdf(request, notebook_id):
    """
    Elimina un PDF generado para un notebook.
    """
    try:
        # Construir ruta al archivo PDF
        pdf_filename = f"notebook_{notebook_id}.pdf"
        pdf_path = os.path.join(PDF_STORAGE_DIR, pdf_filename)

        # Verificar si el archivo existe
        if not os.path.exists(pdf_path):
            return JsonResponse({
                'status': 'error',
                'message': 'El PDF solicitado no existe'
            }, status=404)

        # Eliminar el archivo
        os.remove(pdf_path)

        # Limpiar el estado de generación
        if notebook_id in pdf_generation_status:
            del pdf_generation_status[notebook_id]

        return JsonResponse({
            'status': 'success',
            'message': 'PDF eliminado correctamente'
        })

    except Exception as e:
        logger.error(f"Error al eliminar PDF: {e}")
        return JsonResponse({
            'status': 'error',
            'message': f'Error: {str(e)}'
        }, status=500)
