/* animations.css - All animations and transitions */

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in animation */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(5px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide up animation */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Float animation for particles */
@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-30px) translateX(15px);
  }
  50% {
    transform: translateY(-15px) translateX(-15px);
  }
  75% {
    transform: translateY(30px) translateX(5px);
  }
}

/* Typing indicator animation */
@keyframes typing {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* <PERSON>unce animation */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Pulse animation */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Blink animation */
@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Fade out animation */
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Animation utility classes */
.animate-fade-in {
  animation: fadeIn var(--animation-speed) ease-out;
}

.animate-slide-in {
  animation: slideIn var(--animation-speed) ease-out;
}

.animate-slide-up {
  animation: slideUp var(--animation-speed) ease-out;
}

.animate-float {
  animation: float 15s infinite ease-in-out;
}

.animate-pulse {
  animation: pulse 2s infinite ease-in-out;
}

.animate-blink {
  animation: blink 1s infinite;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

/* Transition utility classes */
.transition-default {
  transition: var(--transition);
}

.transition-fast {
  transition: var(--transition-fast);
}

.transition-slow {
  transition: var(--transition-slow);
}

/* Theme transitions are now handled in theme-transitions.css */

/* Entrance animations for elements */
.animate-entrance {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity var(--animation-speed) ease, transform var(--animation-speed) ease;
}

.animate-entrance.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Hover effect animations */
.hover-scale {
  transition: transform var(--animation-speed-fast) ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-lift {
  transition: transform var(--animation-speed-fast) ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-glow {
  transition: box-shadow var(--animation-speed-fast) ease;
}

.hover-glow:hover {
  box-shadow: 0 0 8px rgba(56, 189, 248, 0.5);
}

/* Typing indicator animation */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  width: fit-content;
}

.typing-indicator span {
  height: 6px;
  width: 6px;
  background: var(--accent-primary);
  border-radius: 50%;
  display: inline-block;
  margin-right: 4px;
  animation: typing 1.2s infinite ease-in-out;
  opacity: 0.7;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
  margin-right: 0;
}
