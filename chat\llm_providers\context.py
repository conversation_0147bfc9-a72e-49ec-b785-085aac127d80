"""
Módulo para gestionar el contexto de solicitudes LLM.
"""
from django.http import HttpRequest
import logging

logger = logging.getLogger(__name__)

class LLMRequestContext:
    """
    Encapsula el contexto de una solicitud para los proveedores de LLM.
    Mantiene la información del proveedor y modelo activos para una solicitud específica.
    """
    
    def __init__(self, request=None, provider_name=None, model_name=None):
        """
        Inicializa el contexto con información de la solicitud o valores explícitos.
        
        Args:
            request: Objeto HttpRequest de Django (opcional)
            provider_name: Nombre del proveedor a usar (opcional, anula el de la solicitud)
            model_name: Nombre del modelo a usar (opcional, anula el de la solicitud)
        """
        from django.conf import settings
        
        self.provider_name = provider_name
        self.model_name = model_name
        
        # Si se proporciona una solicitud, extraer información de la sesión
        if request and isinstance(request, HttpRequest):
            # Obtener proveedor de la sesión o usar el proporcionado explícitamente
            self.provider_name = self.provider_name or request.session.get('active_llm_provider')
            # Obtener modelo de la sesión o usar el proporcionado explícitamente
            self.model_name = self.model_name or request.session.get('active_model')
            
            # Si es Gemini, obtener el modelo específico
            if self.provider_name == 'gemini' and not self.model_name:
                self.model_name = request.session.get('active_gemini_model')
        
        # Usar valores predeterminados si no se especifican
        self.provider_name = self.provider_name or settings.ACTIVE_LLM_PROVIDER
        
        # Normalizar el nombre del proveedor
        if self.provider_name:
            self.provider_name = self.provider_name.lower()
            
        logger.debug(f"LLMRequestContext inicializado: provider={self.provider_name}, model={self.model_name}")
    
    def save_to_session(self, request):
        """
        Guarda el contexto en la sesión del usuario.
        
        Args:
            request: Objeto HttpRequest de Django
        """
        if not isinstance(request, HttpRequest):
            logger.warning("No se puede guardar el contexto: el objeto request no es válido")
            return
            
        request.session['active_llm_provider'] = self.provider_name
        request.session['active_model'] = self.model_name
        
        # Si es Gemini, guardar el modelo específico
        if self.provider_name == 'gemini':
            request.session['active_gemini_model'] = self.model_name
            
        logger.debug(f"Contexto guardado en sesión: provider={self.provider_name}, model={self.model_name}")
