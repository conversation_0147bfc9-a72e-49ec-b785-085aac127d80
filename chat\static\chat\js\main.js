/**
 * Archivo principal de JavaScript
 *
 * Este archivo importa y configura todos los módulos de la aplicación.
 */

import ChatBot from './modules/core.js';
import { initializeSpeechRecognition } from './modules/speech.js';
import { initializeTerminals, setupTerminalObserver } from './modules/terminal-code.js';
import { initializeScrollHelpers } from './modules/scroll-helper.js';
import { initializeChatNavigation } from './modules/chat-nav.js';
import { initializeGlobalScroll } from './modules/global-scroll.js';
import { testFeature } from './modules/test-feature.js';
import { initializeThemeSwitcher } from './modules/theme-switcher.js';
import { initializeModelSelector } from './modules/model-selector.js';
import { initializeRightSidebar } from './modules/right-sidebar.js';
import { initializeNotebook } from './modules/notebook.js';
import { initializeNotes } from './modules/notes.js';
import { initializeCustomDragDrop } from './modules/custom-drag-drop.js';
import { initializePDFGenerator } from './modules/pdf-generator.js';
import { initializeSegmentChat } from './modules/segment-chat.js';


// Inicializar el chat
ChatBot.initialize();

// Inicializar módulos cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
  // Inicializar scroll global primero para asegurar que funcione correctamente
  initializeGlobalScroll();

  // Inicializar navegación del chat
  initializeChatNavigation();

  // Inicializar helpers de scroll
  initializeScrollHelpers();

  // Inicializar reconocimiento de voz
  initializeSpeechRecognition();

  // Inicializar terminales de código
  initializeTerminals();
  setupTerminalObserver();

  // Inicializar la funcionalidad de test
  testFeature.initialize();

  // Inicializar el switcher de temas sin transiciones
  initializeThemeSwitcher();

  // Inicializar el selector de modelos
  initializeModelSelector();

  // Inicializar la barra lateral derecha
  initializeRightSidebar();

  // Inicializar el notebook
  initializeNotebook();

  // Inicializar las notas
  initializeNotes();

  // Inicializar el generador de PDF
  initializePDFGenerator();

  // Inicializar la funcionalidad de arrastrar y soltar personalizada
  window.customDragDrop = initializeCustomDragDrop();

  // Inicializar el chat de segmentos
  initializeSegmentChat();
});

// Exportar ChatBot al ámbito global para compatibilidad con código existente
window.ChatBot = ChatBot;
