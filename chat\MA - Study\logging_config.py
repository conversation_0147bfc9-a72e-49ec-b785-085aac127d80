# logging_config.py
import logging
import sys
import os
from colorama import Fore, Style, Back, init

# init(autoreset=True) # Let main.py handle initialization

class ColorFormatter(logging.Formatter):
    """Custom formatter to add colors and prefixes based on log level and extra flags."""
    LOG_LEVEL_COLORS = {
        logging.DEBUG: Fore.CYAN,
        logging.INFO: Fore.BLUE,
        logging.WARNING: Fore.YELLOW,
        logging.ERROR: Fore.RED,
        logging.CRITICAL: Fore.RED + Style.BRIGHT,
    }
    RESET_ALL = Style.RESET_ALL

    # Custom prefixes/styles
    STEP_PREFIX = f"{Fore.MAGENTA}>>> {RESET_ALL}"
    TIMING_PREFIX = f"{Fore.BLUE}[TIMING]{RESET_ALL} "
    HEADER_PREFIX = f"\n{Back.BLUE}{Fore.WHITE}{Style.BRIGHT} "
    HEADER_SUFFIX = f" {RESET_ALL}\n"
    SUCCESS_PREFIX = f"{Fore.GREEN}[SUCCESS]{RESET_ALL} "

    def __init__(self, fmt="%(message)s", datefmt=None, style='%', validate=True):
        # Initialize with a basic format string (e.g., just the message)
        # The base class will use this *before* our custom logic adds prefixes/colors.
        super().__init__(fmt=fmt, datefmt=datefmt, style=style, validate=validate)

    def format(self, record):
        # 1. Let the base class format the message using the fmt provided during __init__
        #    This also handles embedding exception information correctly.
        log_msg = super().format(record)

        # 2. Determine the correct prefix based on level and extra flags
        log_prefix = ""
        is_header = getattr(record, 'is_header', False)

        if is_header:
            # Special handling for headers: Apply prefix and suffix directly
            return f"{self.HEADER_PREFIX}{log_msg}{self.HEADER_SUFFIX}"
        elif getattr(record, 'is_step', False):
            log_prefix = self.STEP_PREFIX
        elif getattr(record, 'is_timing', False):
            log_prefix = self.TIMING_PREFIX
        elif getattr(record, 'is_success', False):
            log_prefix = self.SUCCESS_PREFIX
        else:
            # Default: Use levelname prefix with color
            log_color = self.LOG_LEVEL_COLORS.get(record.levelno, Fore.WHITE)
            # Include levelname in the default prefix
            log_prefix = f"{log_color}[{record.levelname}]{self.RESET_ALL} "

        # 3. Combine the determined prefix with the formatted message from the base class
        #    The log_msg already contains the core message and any exception text.
        return f"{log_prefix}{log_msg}"


def setup_logging(debug_mode: bool = False):
    """Configures the root logger."""
    logger = logging.getLogger()
    level = logging.DEBUG if debug_mode else logging.INFO
    logger.setLevel(level)

    if logger.hasHandlers():
        logger.handlers.clear()

    # Console Handler with Color
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    # Initialize ColorFormatter with the base format string '%(message)s'
    # Our custom format method will add the necessary prefixes later.
    console_handler.setFormatter(ColorFormatter('%(message)s'))
    logger.addHandler(console_handler)

    # Optional: Add File Handler (remains the same)
    # ... (file handler code if you use it) ...

    # Silence overly verbose libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)

    # Use logger.log for the initial message to avoid recursion issues if logging fails early
    # logger.info(f"Logging initialized. Level: {logging.getLevelName(level)}")
    # Logging this message after handler is fully set up should be fine now.
    # logger.info(f"Logging initialized. Level: {logging.getLevelName(level)}")
    # Let's remove this initial log message from setup_logging itself to be safer,
    # main.py can log the debug status after setup is complete.