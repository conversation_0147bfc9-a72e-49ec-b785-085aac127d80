/* notes.css - Styles for the notes feature */

/* Notes modal */
.notes-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    backdrop-filter: blur(5px);
}

.dark-theme .notes-modal {
    background-color: var(--modal-backdrop);
}

.light-theme .notes-modal {
    background-color: var(--modal-backdrop-light);
}

.notes-modal.active {
    opacity: 1;
    visibility: visible;
}

.notes-content {
    background-color: var(--bg-secondary);
    border-radius: 16px;
    width: 95%;
    max-width: 1600px;
    height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: var(--card-shadow);
    transform: translateY(20px);
    transition: transform 0.3s ease;
    border: none;
}

.notes-modal.active .notes-content {
    transform: translateY(0);
}

.notes-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: center; /* Center content horizontally */
    align-items: center;
    /* No background color here - theme-specific styles will handle it */
}

.notes-header h3 {
    margin: 0 auto;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    flex: 1;
    text-align: center;
}

/* Notes header text color - same for both themes */
.notes-header h3 {
    color: var(--text-primary);
}

.notes-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sidebar-footer {
    padding: 10px;
    position: sticky;
    bottom: 0;
    background-color: inherit;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
}

.light-theme .sidebar-footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.dark-theme .sidebar-footer {
    border-top: 1px solid var(--border-color);
}

.new-note-btn {
    background: rgba(var(--accent-primary-rgb), 0.1);
    border: 1px solid rgba(var(--accent-primary-rgb), 0.2);
    color: var(--accent-primary);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 6px;
    width: 100%;
    justify-content: center;
    height: 36px;
    font-weight: 500;
}

.new-note-btn i {
    font-size: 0.9rem;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.new-note-btn::after {
    content: "New Note";
    font-weight: 500;
}

.new-note-btn:hover {
    background-color: rgba(var(--accent-primary-rgb), 0.2);
    box-shadow: 0 2px 8px rgba(var(--accent-primary-rgb), 0.15);
    transform: translateY(-1px);
}

.new-note-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(var(--accent-primary-rgb), 0.2);
}

.close-notes {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--text-dimmed);
    font-size: 1.2rem;
    font-weight: 300;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.close-notes::before {
    content: "×";
    line-height: 1;
}

.close-notes:hover {
    background-color: rgba(var(--text-dimmed-rgb), 0.08);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.close-notes:active {
    transform: translateY(0);
}

.notes-container {
    display: flex;
    flex: 1;
    overflow: hidden;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notes-sidebar {
    width: 220px;
    overflow-y: auto;
    background-color: var(--bg-tertiary);
    transition: width 0.3s ease, transform 0.3s ease, opacity 0.3s ease;
    display: flex;
    flex-direction: column;
}

.notes-sidebar.collapsed {
    width: 0;
    transform: translateX(-100%);
    opacity: 0;
}

.notes-list {
    padding: 8px;
    flex: 1;
    overflow-y: auto;
}

.note-item {
    padding: 8px 10px;
    border-radius: 6px;
    margin-bottom: 6px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 2px solid transparent;
    background-color: rgba(var(--bg-quaternary-rgb), 0.1);
    position: relative;
    min-height: 32px;
}

.note-item:hover {
    background-color: rgba(var(--bg-quaternary-rgb), 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.note-item.active {
    background-color: rgba(var(--accent-primary-rgb), 0.1);
    border-left: 2px solid var(--accent-primary);
    transform: none;
    box-shadow: 0 2px 8px rgba(var(--accent-primary-rgb), 0.15);
}

/* Pinned style removed */

/* Drag and drop styles removed */

.note-title {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
    font-size: 0.8rem;
    font-weight: 400;
    padding-left: 6px;
}

/* Pin button removed as requested */

.note-actions {
    display: flex;
    gap: 12px; /* Significantly increase gap between buttons */
    opacity: 0;
    transition: opacity 0.2s ease;
}

.note-item:hover .note-actions {
    opacity: 1;
}

.note-rename-btn,
.note-delete-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--text-dimmed);
    font-size: 0.9rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.note-rename-btn:hover,
.note-delete-btn:hover {
    background-color: rgba(var(--text-dimmed-rgb), 0.08);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.note-delete-btn:hover {
    background-color: rgba(var(--danger-color-rgb), 0.1);
    color: var(--danger-color);
}

.note-rename-btn:active,
.note-delete-btn:active {
    transform: translateY(0);
}

/* Empty state for notes list */
.notes-list:empty::after {
    content: "No notes yet";
    display: block;
    text-align: center;
    color: var(--text-secondary);
    padding: 12px 0;
    font-size: 0.8rem;
}

.note-rename-input {
    width: 100%;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 0.8rem;
    outline: none;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.note-rename-input:focus {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(var(--accent-primary-rgb), 0.2);
}

.notes-editor-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin: 0;
    padding: 0;
    transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notes-toolbar {
    position: relative;
    z-index: 10;
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 0;
    margin: 0;
    background-color: transparent;
}

/* Custom styling for Quill toolbar - Floating style */
.ql-toolbar.ql-snow {
    border: none !important;
    padding: 8px 16px !important;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    background-color: var(--bg-tertiary);
    margin: 20px auto 0;
    height: auto;
    width: fit-content;
    max-width: 85%;
    border-radius: 12px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
    z-index: 20;
    min-width: 320px;
    transition: opacity 0.2s ease, background-color 0.2s ease;
}

.light-theme .ql-toolbar.ql-snow {
    background-color: var(--bg-tertiary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.dark-theme .ql-toolbar.ql-snow {
    background-color: var(--bg-tertiary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* Group toolbar buttons */
.ql-toolbar.ql-snow .ql-formats {
    margin-right: 12px !important;
    border-right: 1px solid var(--border-color);
    padding-right: 12px;
    display: flex;
    gap: 4px;
}

.ql-toolbar.ql-snow .ql-formats:last-child {
    border-right: none;
    margin-right: 0 !important;
    padding-right: 0;
}

/* Style toolbar buttons */
.ql-toolbar.ql-snow button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    color: var(--text-dimmed);
    border: none;
    background: none;
    font-size: 0.9rem;
    position: relative;
}

/* Copy button styling */
.ql-toolbar.ql-snow .ql-copy {
    color: var(--text-dimmed);
    width: 64px;
    height: 32px;
    font-size: 0.8em;
    font-family: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(var(--accent-primary-rgb), 0.08);
    border-radius: 6px;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.ql-toolbar.ql-snow button:hover {
    background-color: rgba(var(--text-dimmed-rgb), 0.08);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.ql-toolbar.ql-snow button.ql-active {
    background-color: rgba(var(--accent-primary-rgb), 0.12);
    color: var(--accent-primary);
}

.ql-toolbar.ql-snow button:active {
    transform: translateY(0);
}

/* Copy button specific hover */
.ql-toolbar.ql-snow .ql-copy:hover {
    background: rgba(var(--accent-primary-rgb), 0.12);
    color: var(--accent-primary);
}

/* Ensure all toolbar icons have consistent color */
.ql-toolbar.ql-snow .ql-stroke {
    stroke: var(--text-dimmed);
    transition: stroke 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.ql-toolbar.ql-snow .ql-fill {
    fill: var(--text-dimmed);
    transition: fill 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.ql-toolbar.ql-snow button:hover .ql-stroke {
    stroke: var(--text-primary);
}

.ql-toolbar.ql-snow button:hover .ql-fill {
    fill: var(--text-primary);
}

.ql-toolbar.ql-snow button.ql-active .ql-stroke {
    stroke: var(--accent-primary);
}

.ql-toolbar.ql-snow button.ql-active .ql-fill {
    fill: var(--accent-primary);
}

/* Add spacing to the editor to accommodate the floating toolbar */
.notes-editor .ql-editor {
    padding-top: 24px !important;
}

/* Floating toolbar animations and responsive adjustments */
.ql-toolbar.ql-snow {
    transition: opacity 0.2s ease, background-color 0.2s ease;
}

/* Toolbar hover effect */
.ql-toolbar.ql-snow:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.light-theme .ql-toolbar.ql-snow:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.dark-theme .ql-toolbar.ql-snow:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

/* Ensure toolbar is visible on small screens */
@media (max-width: 576px) {
    .ql-toolbar.ql-snow {
        min-width: 280px;
        max-width: 95%;
        padding: 6px 12px !important;
        gap: 6px;
    }

    .ql-toolbar.ql-snow .ql-formats {
        margin-right: 8px !important;
        padding-right: 8px;
    }

    .ql-toolbar.ql-snow button {
        width: 28px;
        height: 28px;
    }

    .ql-toolbar.ql-snow .ql-copy {
        width: 56px;
        height: 28px;
    }
}

/* Ensure toolbar is properly positioned in the editor container */
.notes-toolbar {
    position: relative;
    z-index: 10;
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 0;
    margin: 0;
    background-color: transparent;
}

/* Ensure toolbar is properly positioned when editor is disabled */
.notes-editor-container.disabled .ql-toolbar.ql-snow {
    opacity: 0.5;
    pointer-events: none;
    transition: opacity 0.2s ease;
}

/* Ensure toolbar is properly positioned when editor is enabled */
.notes-editor-container:not(.disabled) .ql-toolbar.ql-snow {
    opacity: 1;
    pointer-events: auto;
    transition: opacity 0.2s ease;
}

/* Remove notes-preview-header styles */
.notes-preview-header,
.notes-preview-header h4,
.light-theme .notes-preview-header h4,
.dark-theme .notes-preview-header h4 {
    display: none !important;
}

.notes-preview-content {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background-color: var(--bg-primary);
    margin: 10px;
    border-radius: 8px;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.notes-preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-dimmed);
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    background-color: rgba(var(--bg-secondary-rgb), 0.3);
    border: 1px dashed var(--border-color);
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.notes-preview-placeholder i {
    font-size: 2rem;
    margin-bottom: 10px;
    opacity: 0.6;
}

/* Quill editor theme overrides */
.light-theme .ql-toolbar {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color) !important;
}

.light-theme .ql-container {
    border: none !important;
}

.light-theme .ql-editor {
    color: var(--text-primary);
}

.light-theme .ql-snow .ql-stroke {
    stroke: var(--text-dimmed);
}

.light-theme .ql-snow .ql-fill {
    fill: var(--text-dimmed);
}

.light-theme .ql-snow .ql-picker {
    color: var(--text-dimmed);
}

.light-theme .ql-snow .ql-picker-options {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

.light-theme .ql-snow .ql-picker-item {
    color: var(--text-primary);
}

.light-theme .ql-snow .ql-picker-label {
    color: var(--text-primary);
}

.dark-theme .ql-toolbar {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color) !important;
}

.dark-theme .ql-container {
    border: none !important;
}

.dark-theme .ql-editor {
    color: var(--text-primary);
}

.dark-theme .ql-snow .ql-stroke {
    stroke: var(--text-dimmed);
}

.dark-theme .ql-snow .ql-fill {
    fill: var(--text-dimmed);
}

.dark-theme .ql-snow .ql-picker {
    color: var(--text-dimmed);
}

.dark-theme .ql-snow .ql-picker-options {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

.dark-theme .ql-snow .ql-picker-item {
    color: var(--text-primary);
}

.dark-theme .ql-snow .ql-picker-label {
    color: var(--text-primary);
}

/* Font size styles for pt units */
.ql-snow .ql-size .ql-picker-label::before { content: 'Size: 14pt'; }
.ql-snow .ql-size .ql-picker-label[data-value="small"]::before { content: 'Size: 12pt'; }
.ql-snow .ql-size .ql-picker-label[data-value="normal"]::before { content: 'Size: 14pt'; }
.ql-snow .ql-size .ql-picker-label[data-value="large"]::before { content: 'Size: 16pt'; }

.ql-snow .ql-size .ql-picker-item[data-value="small"]::before { content: '12pt'; }
.ql-snow .ql-size .ql-picker-item[data-value="normal"]::before { content: '14pt'; }
.ql-snow .ql-size .ql-picker-item[data-value="large"]::before { content: '16pt'; }

/* Apply font sizes */
.ql-snow .ql-size-small { font-size: 12pt !important; }
.ql-snow .ql-size-normal { font-size: 14pt !important; }
.ql-snow .ql-size-large { font-size: 16pt !important; }

/* Default font size for editor */
.ql-editor {
    font-size: 14pt !important;
}

/* Confirmation dialog */
.notes-confirmation-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1100;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    backdrop-filter: blur(5px);
}

.dark-theme .notes-confirmation-dialog {
    background-color: rgba(var(--bg-primary-rgb), 0.5);
}

.light-theme .notes-confirmation-dialog {
    background-color: rgba(var(--bg-primary-rgb), 0.3);
}

/* Ensure delete button has proper styling in light theme */
.light-theme .notes-confirmation-actions .delete-btn {
    background-color: var(--danger-color);
    border: 1px solid var(--danger-color);
    color: white;
}

.notes-confirmation-dialog.active {
    opacity: 1;
    visibility: visible;
}

.notes-confirmation-content {
    background-color: var(--bg-secondary);
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    padding: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notes-confirmation-dialog.active .notes-confirmation-content {
    transform: translateY(0);
}

.notes-confirmation-content h4 {
    margin: 0 0 15px 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.notes-confirmation-content p {
    margin: 0 0 20px 0;
    color: var(--text-primary);
    font-size: 1rem;
    line-height: 1.5;
}

.notes-confirmation-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.cancel-btn {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

.cancel-btn:hover {
    background-color: var(--bg-quaternary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.cancel-btn:active {
    transform: translateY(0);
}

.delete-btn,
.notes-confirmation-actions .delete-btn {
    background-color: var(--danger-color);
    border: 1px solid var(--danger-color);
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

.delete-btn:hover,
.notes-confirmation-actions .delete-btn:hover {
    background-color: rgba(var(--danger-color-rgb), 0.8);
    box-shadow: 0 2px 4px rgba(var(--danger-color-rgb), 0.3);
    transform: translateY(-1px);
}

.delete-btn:active,
.notes-confirmation-actions .delete-btn:active {
    transform: translateY(0);
}

/* Preview delete button styling */
.preview-button.delete-btn {
    background: none;
    border: none;
    color: var(--text-dimmed);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.dark-theme .preview-button.delete-btn {
    color: var(--text-dimmed);
}

.light-theme .preview-button.delete-btn {
    color: var(--text-dimmed);
}

.preview-button.delete-btn:hover {
    color: var(--danger-color);
    background-color: rgba(var(--danger-color-rgb), 0.1);
}

.dark-theme .preview-button.delete-btn:hover {
    color: var(--danger-color);
    background-color: rgba(var(--danger-color-rgb), 0.1);
}

.light-theme .preview-button.delete-btn:hover {
    color: var(--danger-color);
    background-color: rgba(var(--danger-color-rgb), 0.1);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translate(-50%, 20px);
    }
    to {
        opacity: 1;
        transform: translate(-50%, 0);
    }
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .notes-content {
        width: 98%;
        max-width: none;
    }

    .notes-sidebar {
        width: 200px;
    }

    .notes-preview-container {
        width: 400px;
    }
}

@media (max-width: 992px) {
    .notes-sidebar {
        width: 180px;
    }

    .notes-preview-container {
        width: 350px;
    }
}

@media (max-width: 768px) {
    .notes-container {
        flex-wrap: wrap;
    }

    .notes-sidebar {
        width: 30%;
        min-width: 180px;
    }

    .notes-editor-container {
        width: 70%;
        flex: none;
    }

    .notes-preview-container {
        width: 100%;
        height: 300px;
        border-left: none;
        border-top: 1px solid var(--border-color);
    }
}

@media (max-width: 576px) {
    .notes-container {
        flex-direction: column;
    }

    .notes-sidebar {
        width: 100%;
        height: 150px;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        flex-direction: row;
    }

    .notes-list {
        flex: 1;
        overflow-y: auto;
    }

    .sidebar-footer {
        width: auto;
        border-top: none;
        border-left: 1px solid var(--border-color);
        padding: 0 10px;
    }

    .new-note-btn {
        height: 100%;
    }

    .notes-editor-container {
        width: 100%;
        height: calc(100% - 450px);
    }

    .notes-preview-container {
        height: 300px;
    }
}

/* Remove duplicate styles for collapsible sidebar */

.toggle-sidebar-btn {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-left: none;
    color: var(--text-dimmed);
    width: 24px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    border-radius: 0 6px 6px 0;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.8;
    font-size: 0.8rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.toggle-sidebar-btn:hover {
    opacity: 1;
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
    transform: translateY(-50%) translateY(-1px);
}

.toggle-sidebar-btn:active {
    transform: translateY(-50%) translateY(0);
}

.toggle-sidebar-btn i {
    transition: transform 0.2s ease;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notes-sidebar.collapsed + .toggle-sidebar-btn i {
    transform: rotate(180deg);
}

.notes-sidebar:not(.collapsed) + .toggle-sidebar-btn i {
    transform: rotate(0deg);
}

.notes-editor {
    flex: 1;
    overflow-y: auto;
    background-color: var(--bg-primary);
    font-size: 1.1rem;
    margin: 0;
    padding: 0;
}

/* Style for disabled editor */
.notes-editor-container.disabled {
    position: relative;
    /* Remove opacity to maintain consistent background color */
}

.notes-editor-container.disabled .ql-toolbar {
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.notes-editor-container.disabled .ql-editor {
    background-color: var(--bg-primary); /* Maintain consistent background color */
}

.notes-editor-container.disabled::after {
    content: "Click the + button in the top right to create your first note.";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-secondary);
    font-size: 1.1rem;
    text-align: center;
    width: 100%;
    padding: 20px;
    pointer-events: none;
    background-color: transparent; /* Make background transparent */
    border-radius: 8px;
    max-width: 400px;
    z-index: 5; /* Ensure it's above the editor but below the toolbar */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

/* Theme-specific styling for the disabled message */
.light-theme .notes-editor-container.disabled::after {
    color: #666;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
}

.dark-theme .notes-editor-container.disabled::after {
    color: #aaa;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.8);
}

/* Remove the old welcome state styles */
.notes-editor .ql-editor.welcome-state {
    display: none;
}

.notes-editor .ql-editor.welcome-state p {
    display: none;
}

.notes-preview-container {
    width: 500px;
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    background-color: var(--bg-tertiary);
    overflow: hidden;
    transition: width 0.3s ease, transform 0.3s ease, opacity 0.3s ease;
}

.notes-preview-container.collapsed {
    width: 0;
    transform: translateX(100%);
    opacity: 0;
}

.toggle-preview-btn {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-right: none;
    color: var(--text-dimmed);
    width: 24px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    border-radius: 6px 0 0 6px;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.8;
    font-size: 0.8rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.toggle-preview-btn:hover {
    background: var(--bg-tertiary);
    opacity: 1;
    color: var(--text-primary);
    transform: translateY(-50%) translateY(-1px);
}

.toggle-preview-btn:active {
    transform: translateY(-50%) translateY(0);
}

.toggle-preview-btn i {
    transition: transform 0.2s ease;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notes-preview-container.collapsed + .toggle-preview-btn i {
    transform: rotate(180deg);
}

.notes-preview-container:not(.collapsed) + .toggle-preview-btn i {
    transform: rotate(0deg);
}

/* Adjust the editor container when preview is collapsed */
.notes-preview-container.collapsed ~ .notes-editor-container {
    margin-right: 0;
}

/* Main modal container */
.notes-modal .notes-content {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Notes header */
.light-theme .notes-header {
  border-bottom: 1px solid #e2e6ea;
  background-color: #f0f2f5; /* Soft gray that complements the sidebar (#f5f5f5) */
  padding-bottom: 12px;
}

.dark-theme .notes-header {
  border-bottom: 1px solid #383838;
  background-color: #252525; /* Dark gray that complements var(--bg-tertiary) */
  padding-bottom: 12px;
}

/* Notes container */

/* Sidebar */
.light-theme .notes-sidebar {
  border-right: none;
  background-color: #f5f5f5;
}

.dark-theme .notes-sidebar {
  border-right: none;
  background-color: var(--bg-tertiary);
}

/* Note items */
.note-item {
  margin-bottom: 2px;
  border-radius: 4px;
}

.light-theme .note-item.active {
  background-color: #e9ecef;
}

.dark-theme .note-item.active {
  background-color: rgba(var(--accent-primary-rgb), 0.1);
}

/* Editor container */
.notes-editor-container {
  background-color: var(--bg-primary);
}

/* Toolbar */

/* Editor */
.notes-editor {
  background-color: var(--bg-primary);
}

.notes-editor .ql-editor {
  background-color: var(--bg-primary);
}

/* Preview container */
.light-theme .notes-preview-container {
  border-left: none;
  background-color: #f8f9fa;
}

.dark-theme .notes-preview-container {
  border-left: none;
  background-color: var(--bg-tertiary);
}

/* Toggle buttons theme support */
.light-theme .toggle-sidebar-btn,
.light-theme .toggle-preview-btn {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-dimmed);
}

.light-theme .toggle-sidebar-btn:hover,
.light-theme .toggle-preview-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.dark-theme .toggle-sidebar-btn,
.dark-theme .toggle-preview-btn {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-dimmed);
}

.dark-theme .toggle-sidebar-btn:hover,
.dark-theme .toggle-preview-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Confirmation dialog */
.notes-confirmation_dialog .notes-confirmation-content {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

