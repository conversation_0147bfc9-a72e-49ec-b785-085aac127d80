# Generated by Django 4.2.7 on 2025-05-30 00:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat', '0006_chat_title'),
    ]

    operations = [
        migrations.CreateModel(
            name='SegmentChat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('segment_chat_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('selected_text', models.TextField()),
                ('title', models.CharField(blank=True, default='Segment Discussion', max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('parent_message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='segment_chats', to='chat.message')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='segment_chats', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SegmentMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sender', models.CharField(choices=[('user', 'User'), ('bot', 'Bot')], max_length=10)),
                ('content', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('segment_chat', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='chat.segmentchat')),
            ],
        ),
        migrations.CreateModel(
            name='SegmentMemory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('memory_data', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('segment_chat', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='memories', to='chat.segmentchat')),
            ],
        ),
    ]
