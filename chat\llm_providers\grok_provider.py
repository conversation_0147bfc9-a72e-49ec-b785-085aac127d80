"""
Proveedor de LLM para Grok (xAI).
"""
from django.conf import settings
import logging
from typing import Dict, Any
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langchain_core.messages import HumanMessage, AIMessage
from openai import OpenAI

from .base import BaseLLMProvider, format_exam_query

logger = logging.getLogger(__name__)

class GrokProvider(BaseLLMProvider):
    """Implementación del proveedor de LLM para Grok."""

    # Modelos disponibles de Grok
    AVAILABLE_MODELS = {
        'grok-3-beta': {
            'id': 'grok-3-beta',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 131072,
            'description': 'Modelo principal de Grok 3, ideal para tareas complejas'
        },
        'grok-3-fast': {
            'id': 'grok-3-fast',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 131072,
            'description': 'Versión rápida de Grok 3 para aplicaciones sensibles a la latencia'
        },
        'grok-3-mini': {
            'id': 'grok-3-mini',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 131072,
            'description': 'Versión ligera de Grok 3, optimizada para tareas lógicas'
        },
        'grok-3-mini-fast': {
            'id': 'grok-3-mini-fast',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 131072,
            'description': 'Versión rápida y ligera de Grok 3'
        },
        'grok-2-vision-1212': {
            'id': 'grok-2-vision-1212',
            'capabilities': ['text', 'vision'],
            'max_tokens': 8192,
            'description': 'Modelo multimodal para procesar texto e imágenes'
        },
        'grok-2-image-1212': {
            'id': 'grok-2-image-1212',
            'capabilities': ['text', 'image_generation'],
            'max_tokens': 131072,
            'description': 'Modelo para generar imágenes a partir de texto'
        }
    }

    def __init__(self):
        """Inicializa el proveedor de Grok."""
        # Intentar usar GROK_API_KEY primero, si no está disponible, usar XAI_API_KEY como fallback
        self.api_key = settings.GROK_API_KEY or settings.XAI_API_KEY
        if not self.api_key:
            logger.warning("Ni GROK_API_KEY ni XAI_API_KEY están configuradas. Asegúrate de configurar al menos una de estas variables de entorno.")

        self._model_name = "grok-3-beta"
        self.max_tokens = 4096
        self.temperature = 0.7

        # Verificar que el modelo exista en la lista de modelos disponibles
        if self._model_name not in self.AVAILABLE_MODELS:
            logger.warning(f"Modelo Grok '{self._model_name}' no reconocido. Usando grok-3-beta como fallback.")
            self._model_name = 'grok-3-beta'

    def set_model(self, model_name: str) -> None:
        """
        Configura el modelo Grok a utilizar.

        Args:
            model_name: Nombre del modelo Grok a utilizar.
        """
        if model_name in self.AVAILABLE_MODELS:
            self._model_name = model_name
            self.max_tokens = self.AVAILABLE_MODELS[model_name].get('max_tokens', 4096)
            logger.info(f"Modelo Grok cambiado a: {model_name}")
        else:
            logger.warning(f"Modelo Grok '{model_name}' no reconocido. Manteniendo {self._model_name}.")

    @property
    def model_name(self) -> str:
        """Nombre del modelo utilizado."""
        return self._model_name

    def query(self, user_input: str, memory_instance: ConversationBufferWindowMemory) -> str:
        """
        Consulta el modelo Grok con la entrada del usuario y la memoria de conversación.

        Args:
            user_input: Texto de entrada del usuario.
            memory_instance: Instancia de memoria de conversación.

        Returns:
            Respuesta del modelo como texto o URL de imagen generada.
        """
        try:
            # Verificar que la clave API esté configurada
            if not self.api_key:
                logger.error("Ni GROK_API_KEY ni XAI_API_KEY están configuradas. Configura al menos una de estas variables de entorno.")
                return "Error de configuración: La clave API de Grok no está configurada. Por favor, contacta al administrador del sistema."

            # Crear cliente OpenAI para Grok
            client = OpenAI(
                api_key=self.api_key,
                base_url="https://api.x.ai/v1"
            )

            # Manejo especial para modelos de generación de imágenes
            if self._model_name == "grok-2-image-1212":
                logger.info(f"Usando modelo de generación de imágenes: {self._model_name}")
                try:
                    # Llamar a la API de generación de imágenes
                    response = client.images.generate(
                        model=self._model_name,
                        prompt=user_input,
                        n=1,  # Número de imágenes a generar
                        response_format="url"  # Obtener URL de la imagen
                    )

                    # Extraer la URL de la imagen
                    image_url = response.data[0].url

                    # Actualizar la memoria con la consulta y respuesta
                    memory_instance.chat_memory.add_user_message(user_input)
                    memory_instance.chat_memory.add_ai_message(f"Imagen generada: {image_url}")

                    # Devolver un diccionario con el tipo de respuesta y la URL de la imagen
                    return {
                        "response_type": "image",
                        "data": image_url
                    }
                except Exception as img_error:
                    logger.error(f"Error al generar imagen con Grok: {img_error}")
                    error_message = str(img_error).lower()

                    if "endpoint" in error_message or "not found" in error_message:
                        return "Error: El modelo de generación de imágenes debe usarse con el endpoint correcto. Detalles: " + str(img_error)
                    else:
                        return f"Error al generar imagen: {str(img_error)}"

            # Obtener mensajes de la memoria
            chat_history = memory_instance.chat_memory.messages
            messages = []

            # Convertir mensajes de la memoria al formato de OpenAI
            for message in chat_history:
                if hasattr(message, 'type'):
                    if message.type == 'human':
                        messages.append({"role": "user", "content": message.content})
                    elif message.type == 'ai':
                        messages.append({"role": "assistant", "content": message.content})
                # Compatibilidad con LangChain 0.1.0+
                elif hasattr(message, 'content'):
                    if isinstance(message, HumanMessage):
                        messages.append({"role": "user", "content": message.content})
                    elif isinstance(message, AIMessage):
                        messages.append({"role": "assistant", "content": message.content})

            # Agregar el mensaje actual del usuario
            messages.append({"role": "user", "content": user_input})

            # Llamar a la API de Grok
            completion = client.chat.completions.create(
                model=self._model_name,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            # Extraer la respuesta
            response = completion.choices[0].message.content

            # Actualizar la memoria con la respuesta
            memory_instance.chat_memory.add_user_message(user_input)
            memory_instance.chat_memory.add_ai_message(response)

            return response

        except Exception as e:
            # Capturar todos los errores de la API
            logger.error(f"Error al consultar Grok: {e}")

            error_message = str(e).lower()

            if "unauthorized" in error_message or "authentication" in error_message:
                return "Error: No autorizado. La clave API de Grok no es válida o ha expirado."
            elif "rate limit" in error_message or "too many requests" in error_message:
                return "Error: Se ha excedido el límite de solicitudes a la API. Por favor, intenta de nuevo más tarde."
            elif "bad request" in error_message or "invalid request" in error_message:
                return "Error: La solicitud al modelo de lenguaje no es válida. Esto puede deberse a un problema con el formato de la solicitud."
            elif "connection" in error_message or "timeout" in error_message:
                return "Error: No se pudo establecer conexión con el servicio de Grok. Por favor, verifica tu conexión a Internet."
            else:
                return f"Error: No se pudo completar la solicitud al modelo de lenguaje. Detalles: {str(e)}"

    def query_with_image(self, user_input: str, image_path: str, memory_instance: ConversationBufferWindowMemory) -> str:
        """
        Consulta el modelo Grok con texto e imagen.

        Args:
            user_input: Texto de entrada del usuario.
            image_path: Ruta a la imagen.
            memory_instance: Instancia de memoria de conversación.

        Returns:
            Respuesta del modelo como texto.
        """
        try:
            # Verificar que la clave API esté configurada
            if not self.api_key:
                logger.error("XAI_API_KEY no está configurada.")
                return "Error: La clave API de Grok no está configurada."

            # Verificar que el modelo soporte visión
            if 'vision' not in self.AVAILABLE_MODELS.get(self._model_name, {}).get('capabilities', []):
                logger.error(f"El modelo {self._model_name} no soporta procesamiento de imágenes.")
                return f"Error: El modelo {self._model_name} no soporta procesamiento de imágenes. Usa grok-2-vision-1212 u otro modelo con capacidad de visión."

            # Crear cliente OpenAI para Grok
            client = OpenAI(
                api_key=self.api_key,
                base_url="https://api.x.ai/v1"
            )

            # Leer la imagen como base64
            import base64
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            # Obtener mensajes de la memoria
            chat_history = memory_instance.chat_memory.messages
            messages = []

            # Convertir mensajes de la memoria al formato de OpenAI
            for message in chat_history:
                if hasattr(message, 'type'):
                    if message.type == 'human':
                        messages.append({"role": "user", "content": message.content})
                    elif message.type == 'ai':
                        messages.append({"role": "assistant", "content": message.content})
                elif hasattr(message, 'content'):
                    if isinstance(message, HumanMessage):
                        messages.append({"role": "user", "content": message.content})
                    elif isinstance(message, AIMessage):
                        messages.append({"role": "assistant", "content": message.content})

            # Crear el mensaje con la imagen
            content = [
                {"type": "text", "text": user_input},
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                    }
                }
            ]

            # Agregar el mensaje actual del usuario con la imagen
            messages.append({"role": "user", "content": content})

            # Llamar a la API de Grok
            completion = client.chat.completions.create(
                model=self._model_name,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            # Extraer la respuesta
            response = completion.choices[0].message.content

            # Actualizar la memoria con la consulta y respuesta
            memory_instance.chat_memory.add_user_message(f"{user_input} [Imagen adjunta]")
            memory_instance.chat_memory.add_ai_message(response)

            return response

        except Exception as e:
            logger.error(f"Error al consultar Grok con imagen: {e}")
            return f"Error: No se pudo completar la solicitud al modelo de lenguaje con la imagen. Detalles: {str(e)}"

    def format_exam_query(self, user_input: str) -> str:
        """
        Formatea la entrada del usuario para la generación de exámenes.

        Args:
            user_input: Texto de entrada del usuario.

        Returns:
            Prompt formateado para la generación de exámenes.
        """
        return format_exam_query(user_input)

    def get_model_info(self) -> Dict[str, Any]:
        """
        Obtiene información sobre el modelo.

        Returns:
            Diccionario con información del modelo.
        """
        model_info = self.AVAILABLE_MODELS.get(self._model_name, {})
        return {
            "name": self._model_name,
            "provider": "xAI",
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "capabilities": model_info.get('capabilities', ['text', 'code']),
            "description": model_info.get('description', '')
        }

    @classmethod
    def get_available_models(cls) -> Dict[str, Dict[str, Any]]:
        """
        Obtiene la lista de modelos disponibles.

        Returns:
            Diccionario con información de los modelos disponibles.
        """
        return cls.AVAILABLE_MODELS
