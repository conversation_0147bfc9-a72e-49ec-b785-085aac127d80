/**
 * Módulo utils.js - Funciones de utilidad
 * 
 * Este módulo contiene funciones de utilidad general para la aplicación.
 */

/**
 * Obtiene el valor de una cookie por su nombre.
 * @param {string} name - Nombre de la cookie.
 * @returns {string} - Valor de la cookie o cadena vacía si no existe.
 */
export function getCookie(name) {
  let cookieValue = '';
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

/**
 * Escapa caracteres HTML especiales.
 * @param {string} text - Texto a escapar.
 * @returns {string} - Texto escapado.
 */
export function escapeHtml(text) {
  if (!text) return '';
  
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * Desescapa caracteres HTML especiales.
 * @param {string} html - HTML a desescapar.
 * @returns {string} - Texto desescapado.
 */
export function unescapeHtml(html) {
  if (!html) return '';
  
  return html
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#039;/g, "'");
}

/**
 * Genera un ID único.
 * @returns {string} - ID único.
 */
export function generateUniqueId() {
  return 'id_' + Math.random().toString(36).substr(2, 9);
}

/**
 * Limita la ejecución de una función.
 * @param {Function} func - Función a limitar.
 * @param {number} wait - Tiempo de espera en milisegundos.
 * @returns {Function} - Función limitada.
 */
export function debounce(func, wait) {
  let timeout;
  return function(...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

/**
 * Formatea una fecha en formato legible.
 * @param {Date|string} date - Fecha a formatear.
 * @returns {string} - Fecha formateada.
 */
export function formatDate(date) {
  if (!date) return '';
  
  const d = new Date(date);
  return d.toLocaleDateString('es-ES', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Detecta si el dispositivo es móvil.
 * @returns {boolean} - true si es móvil, false en caso contrario.
 */
export function isMobileDevice() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * Detecta si el navegador es compatible con una característica.
 * @param {string} feature - Característica a detectar.
 * @returns {boolean} - true si es compatible, false en caso contrario.
 */
export function isFeatureSupported(feature) {
  switch (feature) {
    case 'speechRecognition':
      return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
    case 'clipboard':
      return navigator.clipboard !== undefined;
    case 'webShare':
      return navigator.share !== undefined;
    default:
      return false;
  }
}
