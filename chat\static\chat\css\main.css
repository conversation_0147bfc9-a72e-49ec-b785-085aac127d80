/* main.css - Main CSS file that imports all other CSS files */

/* Import variables first - foundation for all other styles */
@import url('variables.css');

/* Import animations */
@import url('animations.css');

/* Import base styles */
@import url('base.css');

/* Import common patterns */
@import url('button-patterns.css');

/* Import layout - core layout structure */
@import url('layout.css');

/* Import scrollbar styles */
@import url('scrollbar.css');

/* Import component styles - organized by functionality */
@import url('sidebar.css');
@import url('header.css');
@import url('message.css');
@import url('input.css');
@import url('code.css');
@import url('chat.css');
@import url('formatted.css');
@import url('chat-nav.css');
@import url('scroll-nav.css');
@import url('exam.css');
@import url('test-feature.css');
@import url('think-container.css');
@import url('notebook.css');
@import url('notes.css');
@import url('model-selector.css');
@import url('notifications.css');
@import url('segment-chat.css');

/* Import utilities last - highest specificity */
@import url('utilities.css');
