/**
 * Module notebook.js - Notebook functionality
 *
 * This module handles the notebook feature that allows users to save
 * and organize bot messages for later reference.
 */

// Notebook class to handle all notebook functionality
class Notebook {
  constructor() {
    this.notebooks = [
      { id: 'all', name: 'All', color: 'var(--accent-primary)', messages: [] }
    ];
    this.activeNotebook = 'all';
    this.isInitialized = false;
    this.storageKey = 'chat_notebook_data';
    // Use theme-aware color references instead of hardcoded hex values
    this.notebookColors = [
      'var(--accent-primary)',    // Primary accent (default)
      'var(--success-color)',     // Success green
      'var(--warning-color)',     // Warning amber
      'var(--danger-color)',      // Danger red
      'var(--accent-secondary)',  // Secondary accent
      'var(--accent-tertiary)',   // Tertiary accent
    ];
  }

  /**
   * Initialize the notebook functionality
   */
  initialize() {
    if (this.isInitialized) return;

    // Load saved messages from localStorage
    this.loadSavedMessages();

    // Create the notebook modal
    this.createNotebookModal();

    // Set up notebook button click handler
    const notebookBtn = document.getElementById('notebook-btn');
    if (notebookBtn) {
      notebookBtn.addEventListener('click', () => this.openNotebook());
    }

    // Remove PDF generator initialization

    this.isInitialized = true;
    console.log('Notebook initialized');
  }

  // Remove loadPDFGenerator method

  /**
   * Load notebooks and messages from localStorage
   */
  loadSavedMessages() {
    try {
      const savedData = localStorage.getItem(this.storageKey);
      if (savedData) {
        this.notebooks = JSON.parse(savedData);
        // Ensure we always have the 'All' notebook
        if (!this.notebooks.find(nb => nb.id === 'all')) {
          this.notebooks.unshift({ id: 'all', name: 'All', color: '#8b5a2b', messages: [] });
        }
        const totalMessages = this.notebooks.reduce((sum, nb) => sum + nb.messages.length, 0);
        console.log(`Loaded ${totalMessages} saved messages across ${this.notebooks.length} notebooks`);
      }
    } catch (error) {
      console.error('Error loading notebooks:', error);
      this.notebooks = [{ id: 'all', name: 'All', color: '#8b5a2b', messages: [] }];
    }
  }

  /**
   * Save notebooks and messages to localStorage
   */
  saveNotebooks() {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.notebooks));
    } catch (error) {
      console.error('Error saving notebooks:', error);
    }
  }

  /**
   * Create the notebook modal
   */
  createNotebookModal() {
    // Check if modal already exists
    if (document.getElementById('notebook-modal')) return;

    // Create modal element
    const modal = document.createElement('div');
    modal.id = 'notebook-modal';
    modal.className = 'notebook-modal';
    modal.innerHTML = `
      <div class="notebook-content">
        <div class="notebook-header">
          <h3>Notebook</h3>
          <div class="notebook-header-actions">
            <button class="delete-all-notes-btn" title="Delete All Notes" id="delete-all-notes-btn" style="display: none;">
              <i class="fa-solid fa-trash-can"></i>
              <span>Delete All</span>
            </button>
            <!-- Remove generate-pdf-btn -->
            <button class="close-notebook">&times;</button>
          </div>
        </div>
        <div class="notebook-container">
          <div class="notebook-body" id="notebook-body">
            <!-- Message previews will be inserted here -->
          </div>
          <div class="notebook-sidebar" id="notebook-sidebar">
            <div class="notebook-sidebar-header">
              <h4>Notebooks</h4>
              <button class="add-notebook-btn" title="Add new notebook">
                <i class="fa-solid fa-plus"></i>
              </button>
            </div>
            <div class="notebook-list" id="notebook-list">
              <!-- Notebook categories will be inserted here -->
            </div>
            <div class="pdf-status-container" id="pdf-status-container">
              <!-- PDF generation status will be shown here -->
            </div>
          </div>
        </div>
      </div>
    `;

    // Add to DOM
    document.body.appendChild(modal);

    // Set up event listeners
    const closeBtn = modal.querySelector('.close-notebook');
    closeBtn.addEventListener('click', () => this.closeNotebook());

    // Close when clicking outside content
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.closeNotebook();
      }
    });

    // Add notebook button
    const addNotebookBtn = modal.querySelector('.add-notebook-btn');
    addNotebookBtn.addEventListener('click', () => this.showAddNotebookDialog());

    // Remove Generate PDF button event listener
    const generatePdfBtn = modal.querySelector('#generate-pdf-btn');
    if (generatePdfBtn) {
      generatePdfBtn.addEventListener('click', () => this.generatePDF());
    }

    // Delete all notes button
    const deleteAllNotesBtn = modal.querySelector('#delete-all-notes-btn');
    if (deleteAllNotesBtn) {
      deleteAllNotesBtn.addEventListener('click', () => this.showDeleteAllNotesDialog());
    }
  }

  /**
   * Show confirmation dialog for deleting all notes in the current notebook
   */
  showDeleteAllNotesDialog() {
    // Find the active notebook
    const activeNotebook = this.notebooks.find(nb => nb.id === this.activeNotebook);
    if (!activeNotebook) return;

    // If there are no messages, show notification and return
    if (activeNotebook.messages.length === 0) {
      this.showNotification('No messages to delete', 'error');
      return;
    }

    // Create dialog element
    const dialog = document.createElement('div');
    dialog.className = 'notebook-dialog delete-dialog';
    dialog.innerHTML = `
      <div class="notebook-dialog-content">
        <h4>Delete All Notes</h4>
        <div class="notebook-dialog-form">
          ${activeNotebook.id === 'all' ?
            `<p>Are you sure you want to delete <strong>all notes</strong> from all notebooks?</p>
            <p class="delete-warning">This action cannot be undone.</p>` :
            `<p>Are you sure you want to delete <strong>all notes</strong> from "${activeNotebook.name}"?</p>
            <p class="delete-warning">This will remove all messages from this notebook.</p>`
          }
        </div>
        <div class="notebook-dialog-actions">
          <button class="cancel-btn">Cancel</button>
          <button class="delete-btn">Delete All</button>
        </div>
      </div>
    `;

    // Add to DOM
    document.body.appendChild(dialog);

    // Set up event listeners
    const cancelBtn = dialog.querySelector('.cancel-btn');
    const deleteBtn = dialog.querySelector('.delete-btn');

    // Cancel button closes dialog
    cancelBtn.addEventListener('click', () => {
      document.body.removeChild(dialog);
    });

    // Delete button deletes all messages
    deleteBtn.addEventListener('click', () => {
      this.deleteAllNotes(activeNotebook.id);
      document.body.removeChild(dialog);
    });

    // Show dialog with animation
    setTimeout(() => {
      dialog.classList.add('active');
    }, 10);
  }

  /**
   * Delete all notes from a notebook
   * @param {string} notebookId - ID of the notebook to delete all notes from
   */
  deleteAllNotes(notebookId) {
    // Find the notebook
    const notebook = this.notebooks.find(nb => nb.id === notebookId);
    if (!notebook) return;

    // If deleting from 'All', clear all notebooks
    if (notebookId === 'all') {
      // Clear messages from all notebooks
      this.notebooks.forEach(nb => {
        nb.messages = [];
      });

      // Show notification
      this.showNotification('All notes deleted from all notebooks');
    } else {
      // Clear messages from the specified notebook
      notebook.messages = [];

      // Show notification
      this.showNotification(`All notes deleted from ${notebook.name}`);
    }

    // Save to localStorage
    this.saveNotebooks();

    // Re-render saved messages and notebook list
    this.renderSavedMessages();
    this.renderNotebooksList();
  }

  /**
   * Save a message to the active notebook
   * @param {HTMLElement} messageContent - The message content element to save
   */
  saveMessageToNotebook(messageContent) {
    // If we couldn't find message content, log and return
    if (!messageContent) {
      console.error('Could not find message content to save');
      return;
    }

    // Create a clone of the content and remove any color styles
    const cleanContent = messageContent.cloneNode(true);
    const allElements = cleanContent.getElementsByTagName('*');
    for (let i = 0; i < allElements.length; i++) {
      allElements[i].removeAttribute('style');
      // Remove any color-related classes
      allElements[i].classList.remove('text-red');
    }

    // Create a new saved message object
    const savedMessage = {
      id: Date.now().toString(),
      content: cleanContent.innerHTML,
      timestamp: new Date().toISOString(),
      preview: this.createPreviewText(messageContent),
      notebookId: 'all' // Always save to 'All' notebook by default
    };

    // Find the 'All' notebook and add the message
    const allNotebook = this.notebooks.find(nb => nb.id === 'all');
    if (allNotebook) {
      allNotebook.messages.push(savedMessage);
    } else {
      // If 'All' notebook doesn't exist (shouldn't happen), create it
      this.notebooks.unshift({ id: 'all', name: 'All', color: '#8b5a2b', messages: [savedMessage] });
    }

    // Save to localStorage
    this.saveNotebooks();

    // Show notification
    this.showNotification('Message saved to notebook');

    // Log for debugging
    console.log('Saved message to notebook:', savedMessage);
  }

  /**
   * Create a preview text from message content
   * @param {HTMLElement} messageContent - The message content element
   * @returns {string} - Preview text
   */
  createPreviewText(messageContent) {
    // Get text content and limit to 100 characters
    const text = messageContent.textContent || '';
    return text.substring(0, 100) + (text.length > 100 ? '...' : '');
  }

  /**
   * Open the notebook modal and display saved messages
   */
  openNotebook() {
    const modal = document.getElementById('notebook-modal');
    if (!modal) return;

    // Render notebooks list
    this.renderNotebooksList();

    // Render saved messages
    this.renderSavedMessages();

    // Update PDF status visibility
    this.updatePDFStatusVisibility();

    // Show modal
    modal.classList.add('active');
  }

  /**
   * Close the notebook modal
   */
  closeNotebook() {
    const modal = document.getElementById('notebook-modal');
    if (modal) {
      modal.classList.remove('active');
    }
  }

  /**
   * Render saved messages in the notebook modal
   */
  renderSavedMessages() {
    const notebookBody = document.getElementById('notebook-body');
    if (!notebookBody) return;

    // Clear existing content
    notebookBody.innerHTML = '';

    // Find the active notebook
    const activeNotebook = this.notebooks.find(nb => nb.id === this.activeNotebook);
    if (!activeNotebook) {
      // If active notebook doesn't exist, switch to 'All'
      this.activeNotebook = 'all';
      return this.renderSavedMessages();
    }

    // Get messages for the active notebook
    const messages = activeNotebook.messages;

    // Show or hide the Delete All button based on message count
    const deleteAllBtn = document.getElementById('delete-all-notes-btn');
    if (deleteAllBtn) {
      if (messages.length === 0) {
        // Hide the button completely when there are no messages
        deleteAllBtn.style.display = 'none';
      } else {
        // Show the button when there are messages
        deleteAllBtn.style.display = 'inline-flex';
        deleteAllBtn.disabled = false;
        deleteAllBtn.style.cursor = 'pointer';
      }
      // Remove any inline styles that might conflict with CSS classes
      deleteAllBtn.style.backgroundColor = '';
      deleteAllBtn.style.color = '';
      deleteAllBtn.style.border = '';

      // Remove any event handlers that might have been added previously
      deleteAllBtn.onmouseover = null;
      deleteAllBtn.onmouseout = null;

      // Ensure proper theme class inheritance
      deleteAllBtn.classList.remove('light-theme', 'dark-theme');
      if (document.body.classList.contains('light-theme')) {
        deleteAllBtn.classList.add('light-theme');
      } else {
        deleteAllBtn.classList.add('dark-theme');
      }
    }

    // If no saved messages, show empty state
    if (messages.length === 0) {
      notebookBody.innerHTML = `
        <div class="notebook-empty">
          <i class="fa-solid fa-book-open"></i>
          <p>This notebook is empty. ${activeNotebook.id === 'all' ?
            'Use the bookmark button on bot messages to save them for later reference.' :
            'Move messages here from the All section or other notebooks.'}</p>
        </div>
      `;
      return;
    }

    // Render each saved message
    messages.forEach((message) => {
      const preview = document.createElement('div');
      preview.className = 'message-preview';
      preview.dataset.messageId = message.id;
      preview.dataset.notebookId = activeNotebook.id;

      // Add notebook color indicator if not in the 'All' notebook
      if (activeNotebook.id !== 'all') {
        preview.style.borderLeft = `4px solid ${activeNotebook.color}`;
      }

      // Format date
      const date = new Date(message.timestamp);
      const formattedDate = date.toLocaleDateString();

      preview.innerHTML = `
        <div class="preview-header">
          <div class="preview-date">
            <i class="fa-regular fa-calendar"></i>
            <span>${formattedDate}</span>
          </div>
          <div class="preview-drag-handle">
            <i class="fa-solid fa-grip-lines"></i>
          </div>
        </div>
        <div class="preview-content">
          <div class="message-content">${message.content}</div>
          <div class="preview-fade"></div>
        </div>
        <div class="preview-actions">
          <div class="preview-actions-group">
            <button class="preview-button view-btn" title="View full message">
              <i class="fa-solid fa-expand"></i>
              <span>View</span>
            </button>
            <button class="preview-button copy-btn icon-only" title="Copy to clipboard">
              <i class="fa-solid fa-copy"></i>
            </button>
          </div>
          <div class="preview-actions-group">
            ${activeNotebook.id === 'all' ? `
            <button class="preview-button move-btn" title="Move to notebook">
              <i class="fa-solid fa-folder-open"></i>
              <span>Move</span>
            </button>` : ''}
            <button class="preview-button delete-btn icon-only" title="Delete from notebook">
              <i class="fa-solid fa-trash"></i>
            </button>
          </div>
        </div>
      `;

      // Add event listeners
      const viewBtn = preview.querySelector('.view-btn');
      const copyBtn = preview.querySelector('.copy-btn');
      const deleteBtn = preview.querySelector('.delete-btn');
      const moveBtn = preview.querySelector('.move-btn');

      viewBtn.addEventListener('click', () => this.viewMessage(message.id, activeNotebook.id));
      copyBtn.addEventListener('click', () => this.copyMessage(message.id, activeNotebook.id));
      deleteBtn.addEventListener('click', () => this.showDeleteMessageDialog(message.id, activeNotebook.id));

      if (moveBtn) {
        moveBtn.addEventListener('click', (e) => {
          this.showMoveToNotebookMenu(e.currentTarget, message.id);
        });
      }

      // Make the message draggable for moving between notebooks, but exclude the preview-actions area
      const previewActions = preview.querySelector('.preview-actions');
      if (previewActions) {
        previewActions.classList.add('no-drag');
        // Add event listeners to prevent drag on buttons
        previewActions.addEventListener('mousedown', (e) => {
          e.stopPropagation();
        });
      }

      this.makeMessageDraggable(preview, message.id, activeNotebook.id);

      notebookBody.appendChild(preview);
    });
  }

  /**
   * View a full message in a modal
   * @param {string} messageId - ID of the message to view
   * @param {string} notebookId - ID of the notebook containing the message
   */
  viewMessage(messageId, notebookId) {
    const notebook = this.notebooks.find(nb => nb.id === notebookId);
    if (!notebook) return;

    const message = notebook.messages.find(m => m.id === messageId);
    if (!message) return;

    // Create a modal for viewing the full message
    const viewModal = document.createElement('div');
    viewModal.className = 'notebook-modal active';

    // Get current theme to apply to the modal
    const currentTheme = document.body.classList.contains('light-theme') ? 'light-theme' : '';
    if (currentTheme) {
      viewModal.classList.add(currentTheme);
    }

    viewModal.innerHTML = `
      <div class="notebook-content" style="max-width: 800px;">
        <div class="notebook-header">
          <h3>Saved Message</h3>
          <button class="close-notebook">&times;</button>
        </div>
        <div class="notebook-body" style="padding: 20px;">
          <div class="message-content">${message.content}</div>
        </div>
      </div>
    `;

    // Add to DOM
    document.body.appendChild(viewModal);

    // Set up close button
    const closeBtn = viewModal.querySelector('.close-notebook');
    closeBtn.addEventListener('click', () => {
      document.body.removeChild(viewModal);
    });

    // Close when clicking outside content
    viewModal.addEventListener('click', (e) => {
      if (e.target === viewModal) {
        document.body.removeChild(viewModal);
      }
    });
  }

  /**
   * Copy a message to clipboard
   * @param {string} messageId - ID of the message to copy
   * @param {string} notebookId - ID of the notebook containing the message
   */
  copyMessage(messageId, notebookId) {
    const notebook = this.notebooks.find(nb => nb.id === notebookId);
    if (!notebook) return;

    const message = notebook.messages.find(m => m.id === messageId);
    if (!message) return;

    // Create a temporary element to extract text content
    const temp = document.createElement('div');
    temp.innerHTML = message.content;
    const textContent = temp.textContent || temp.innerText || '';

    // Copy to clipboard
    navigator.clipboard.writeText(textContent)
      .then(() => {
        this.showNotification('Message copied to clipboard');
      })
      .catch(err => {
        console.error('Failed to copy message:', err);
        this.showNotification('Failed to copy message', 'error');
      });
  }

  /**
   * Show delete confirmation dialog for a message
   * @param {string} messageId - ID of the message to delete
   * @param {string} notebookId - ID of the notebook containing the message
   */
  showDeleteMessageDialog(messageId, notebookId) {
    // Create dialog element
    const dialog = document.createElement('div');
    dialog.className = 'notebook-dialog delete-dialog';
    dialog.innerHTML = `
      <div class="notebook-dialog-content">
        <h4>Delete Message</h4>
        <div class="notebook-dialog-form">
          <p>How would you like to delete this message?</p>
        </div>
        <div class="notebook-dialog-actions">
          <button class="cancel-btn">Cancel</button>
          <button class="delete-current-btn">Delete from this notebook</button>
          ${notebookId !== 'all' ? `<button class="delete-all-btn">Delete from all notebooks</button>` : ''}
        </div>
      </div>
    `;

    // Add to DOM
    document.body.appendChild(dialog);

    // Set up event listeners
    const cancelBtn = dialog.querySelector('.cancel-btn');
    const deleteCurrentBtn = dialog.querySelector('.delete-current-btn');
    const deleteAllBtn = dialog.querySelector('.delete-all-btn');

    // Cancel button closes dialog
    cancelBtn.addEventListener('click', () => {
      document.body.removeChild(dialog);
    });

    // Delete from current notebook only
    deleteCurrentBtn.addEventListener('click', () => {
      this.deleteMessage(messageId, notebookId, false);
      document.body.removeChild(dialog);
    });

    // Delete from all notebooks
    if (deleteAllBtn) {
      deleteAllBtn.addEventListener('click', () => {
        this.deleteMessage(messageId, notebookId, true);
        document.body.removeChild(dialog);
      });
    }

    // Show dialog with animation
    setTimeout(() => {
      dialog.classList.add('active');
    }, 10);
  }

  /**
   * Delete a message from the notebook
   * @param {string} messageId - ID of the message to delete
   * @param {string} notebookId - ID of the notebook containing the message
   * @param {boolean} deleteFromAll - Whether to delete from all notebooks
   */
  deleteMessage(messageId, notebookId, deleteFromAll) {
    if (deleteFromAll) {
      // Delete from all notebooks
      this.notebooks.forEach(notebook => {
        notebook.messages = notebook.messages.filter(m => m.id !== messageId);
      });

      // Show notification
      this.showNotification('Message removed from all notebooks');
    } else {
      // Find the notebook
      const notebook = this.notebooks.find(nb => nb.id === notebookId);
      if (!notebook) return;

      // Filter out the message to delete
      notebook.messages = notebook.messages.filter(m => m.id !== messageId);

      // If deleting from 'All', delete from all other notebooks too
      if (notebookId === 'all') {
        this.notebooks.forEach(nb => {
          if (nb.id !== 'all') {
            nb.messages = nb.messages.filter(m => m.id !== messageId);
          }
        });
      }

      // Show notification
      this.showNotification(`Message removed from ${notebook.name}`);
    }

    // Save to localStorage
    this.saveNotebooks();

    // Re-render saved messages
    this.renderSavedMessages();
    this.renderNotebooksList();
  }

  /**
   * Generate a PDF from the current notebook
   */
  generatePDF() {
    // Get the active notebook
    const notebook = this.notebooks.find(nb => nb.id === this.activeNotebook);
    if (!notebook) {
      this.showNotification('No notebook selected', 'error');
      return;
    }

    // Check if there are messages in the notebook
    if (notebook.messages.length === 0) {
      this.showNotification('Notebook is empty', 'error');
      return;
    }

    // Check if PDF generator is available
    if (!window.pdfGenerator) {
      this.showNotification('PDF generator not available', 'error');
      this.loadPDFGenerator();
      return;
    }

    // Show PDF status container
    this.showPDFStatusContainer(notebook.id);

    // Generate PDF
    window.pdfGenerator.generatePDF(notebook)
      .then(result => {
        console.log('PDF generation result:', result);

        // Update status UI
        this.updatePDFStatusUI(notebook.id, result);

        if (result.status === 'completed') {
          // Show preview and download buttons
          this.showPDFActions(notebook.id, result.pdf_url);
        }
      })
      .catch(error => {
        console.error('Error generating PDF:', error);
        this.showNotification(`Error generating PDF: ${error.message}`, 'error');
      });
  }

  /**
   * Update PDF status visibility based on the active notebook
   */
  updatePDFStatusVisibility() {
    const container = document.getElementById('pdf-status-container');
    if (!container) return;

    // Do not clear the container to retain status visibility
    // Check if the active notebook has a PDF status in memory
    if (window.pdfGenerator) {
      if (window.pdfGenerator.pdfStatus[this.activeNotebook]) {
        const status = window.pdfGenerator.pdfStatus[this.activeNotebook];
        // Show status if it exists and is completed
        if (status.status === 'completed') {
          container.innerHTML = '';
          this.displayPDFStatus(this.activeNotebook, status);
        }
      } else {
        // If no PDF status in memory, check if one exists on the server
        window.pdfGenerator.loadPDFStatus(this.activeNotebook)
          .then(status => {
            if (status && status.status === 'completed') {
              container.innerHTML = '';
              this.displayPDFStatus(this.activeNotebook, status);
            }
          })
          .catch(error => {
            console.error('Error checking PDF status:', error);
          });
      }
    }
  }

  /**
   * Display PDF status for a notebook
   * @param {string} notebookId - ID of the notebook
   * @param {Object} status - PDF status object
   */
  displayPDFStatus(notebookId, status) {
    const container = document.getElementById('pdf-status-container');
    if (!container) return;

    container.innerHTML = `
      <div class="pdf-status" data-notebook-id="${notebookId}">
        <h4>PDF Generation</h4>
        <div class="pdf-progress">
          <div class="pdf-progress-bar" style="width: 100%"></div>
        </div>
        <div class="pdf-status-text">${status.message || 'PDF generated successfully'}</div>
        <div class="pdf-actions">
          <button class="preview-pdf-btn" data-notebook-id="${notebookId}" title="View PDF">
            <i class="fa-solid fa-eye"></i>
          </button>
          <button class="download-pdf-btn" data-notebook-id="${notebookId}" title="Download PDF">
            <i class="fa-solid fa-download"></i>
          </button>
          <button class="delete-pdf-btn" data-notebook-id="${notebookId}" title="Delete PDF">
            <i class="fa-solid fa-trash"></i>
          </button>
        </div>
      </div>
    `;

    // Add event listeners to buttons
    const previewBtn = container.querySelector('.preview-pdf-btn');
    const downloadBtn = container.querySelector('.download-pdf-btn');
    const deleteBtn = container.querySelector('.delete-pdf-btn');

    if (previewBtn) {
      previewBtn.addEventListener('click', () => {
        window.pdfGenerator.previewPDF(notebookId);
      });
    }

    if (downloadBtn) {
      downloadBtn.addEventListener('click', () => {
        window.pdfGenerator.downloadPDF(notebookId);
      });
    }    if (deleteBtn) {
      deleteBtn.addEventListener('click', () => {
        // Use custom confirmation dialog if available, otherwise fallback to browser confirm
        if (window.ChatBot && window.ChatBot.showConfirmationDialog) {
          window.ChatBot.showConfirmationDialog('Are you sure you want to delete this PDF?', {
            confirmText: 'Yes, delete',
            cancelText: 'Cancel'
          }).then((confirmed) => {
            if (confirmed) {
              window.pdfGenerator.deletePDF(notebookId)
                .then(() => {
                  // Clear the PDF status container after successful deletion
                  container.innerHTML = '';

                  // Remove from status tracking
                  if (window.pdfGenerator && window.pdfGenerator.pdfStatus[notebookId]) {
                    delete window.pdfGenerator.pdfStatus[notebookId];
                  }
                });
            }
          });
        } else {
          // Fallback to browser confirm
          if (confirm('Are you sure you want to delete this PDF?')) {
            window.pdfGenerator.deletePDF(notebookId)
              .then(() => {
                // Clear the PDF status container after successful deletion
                container.innerHTML = '';

                // Remove from status tracking
                if (window.pdfGenerator && window.pdfGenerator.pdfStatus[notebookId]) {
                  delete window.pdfGenerator.pdfStatus[notebookId];
                }
              });
          }
        }
      });
    }
  }

  /**
   * Show PDF status container
   * @param {string} notebookId - ID of the notebook
   */
  showPDFStatusContainer(notebookId) {
    const container = document.getElementById('pdf-status-container');
    if (!container) return;

    // Clear any existing PDF status elements
    container.innerHTML = '';

    // Only show PDF status for the current notebook
    if (notebookId !== this.activeNotebook) return;

    container.innerHTML = `
      <div class="pdf-status" data-notebook-id="${notebookId}">
        <h4>PDF Generation</h4>
        <div class="pdf-progress">
          <div class="pdf-progress-bar" style="width: 0%"></div>
        </div>
        <div class="pdf-status-text">Initializing PDF generation...</div>
        <div class="pdf-actions">
          <button class="preview-pdf-btn" data-notebook-id="${notebookId}" style="display: none;" title="View PDF">
            <i class="fa-solid fa-eye"></i>
          </button>
          <button class="download-pdf-btn" data-notebook-id="${notebookId}" style="display: none;" title="Download PDF">
            <i class="fa-solid fa-download"></i>
          </button>
          <button class="delete-pdf-btn" data-notebook-id="${notebookId}" style="display: none;" title="Delete PDF">
            <i class="fa-solid fa-trash"></i>
          </button>
        </div>
      </div>
    `;

    // Add event listeners to buttons
    const previewBtn = container.querySelector('.preview-pdf-btn');
    const downloadBtn = container.querySelector('.download-pdf-btn');
    const deleteBtn = container.querySelector('.delete-pdf-btn');

    if (previewBtn) {
      previewBtn.addEventListener('click', () => {
        window.pdfGenerator.previewPDF(notebookId);
      });
    }

    if (downloadBtn) {
      downloadBtn.addEventListener('click', () => {
        window.pdfGenerator.downloadPDF(notebookId);
      });
    }    if (deleteBtn) {
      deleteBtn.addEventListener('click', () => {
        // Use custom confirmation dialog if available, otherwise fallback to browser confirm
        if (window.ChatBot && window.ChatBot.showConfirmationDialog) {
          window.ChatBot.showConfirmationDialog('Are you sure you want to delete this PDF?', {
            confirmText: 'Yes, delete',
            cancelText: 'Cancel'
          }).then((confirmed) => {
            if (confirmed) {
              window.pdfGenerator.deletePDF(notebookId)
                .then(() => {
                  // Clear the PDF status container after successful deletion
                  container.innerHTML = '';
                });
            }
          });
        } else {
          // Fallback to browser confirm
          if (confirm('Are you sure you want to delete this PDF?')) {
            window.pdfGenerator.deletePDF(notebookId)
              .then(() => {
                // Clear the PDF status container after successful deletion
                container.innerHTML = '';
              });
          }
        }
      });
    }
  }

  /**
   * Update PDF status UI
   * @param {string} notebookId - ID of the notebook
   * @param {Object} status - Status object
   */
  updatePDFStatusUI(notebookId, status) {
    // Only update if this is the active notebook
    if (notebookId !== this.activeNotebook) {
      // If the status is completed, store it for later use
      if (status.status === 'completed' && window.pdfGenerator) {
        window.pdfGenerator.pdfStatus[notebookId] = status;
      }
      return;
    }

    // Find the PDF status element
    const statusElement = document.querySelector(`.pdf-status[data-notebook-id="${notebookId}"]`);
    if (!statusElement) {
      // If the element doesn't exist but status is completed, create it
      if (status.status === 'completed') {
        this.displayPDFStatus(notebookId, status);
      }
      return;
    }

    // Update status text and progress
    const statusText = statusElement.querySelector('.pdf-status-text');
    const progressBar = statusElement.querySelector('.pdf-progress-bar');

    if (statusText) {
      statusText.textContent = status.message || 'Unknown status';
    }

    if (progressBar) {
      progressBar.style.width = `${status.progress || 0}%`;
    }

    // If status is completed, update the PDF actions
    if (status.status === 'completed') {
      this.showPDFActions(notebookId, status.pdf_url);
    }
  }

  /**
   * Show PDF actions (preview, download, and delete buttons)
   * @param {string} notebookId - ID of the notebook
   * @param {string} pdfUrl - URL of the generated PDF
   */
  showPDFActions(notebookId, pdfUrl) {
    // Find the buttons
    const previewBtn = document.querySelector(`.preview-pdf-btn[data-notebook-id="${notebookId}"]`);
    const downloadBtn = document.querySelector(`.download-pdf-btn[data-notebook-id="${notebookId}"]`);
    const deleteBtn = document.querySelector(`.delete-pdf-btn[data-notebook-id="${notebookId}"]`);

    if (previewBtn && downloadBtn && deleteBtn) {
      previewBtn.style.display = 'inline-flex';
      downloadBtn.style.display = 'inline-flex';
      deleteBtn.style.display = 'inline-flex';
    }
  }

  /**
   * Show a notification
   * @param {string} message - Notification message
   * @param {string} type - Notification type (success, error)
   */
  showNotification(message, type = 'success') {
    // Check if notification container exists
    let container = document.getElementById('notification-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'notification-container';
      document.body.appendChild(container);
    }

    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-message">${message}</span>
        <button class="notification-close">&times;</button>
      </div>
    `;

    // Add to container
    container.appendChild(notification);

    // Set up close button
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
      notification.classList.add('closing');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    });

    // Auto-close after 2 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.classList.add('closing');
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }
    }, 2000);
  }

  /**
   * Render the list of notebooks in the sidebar
   */
  renderNotebooksList() {
    const notebookList = document.getElementById('notebook-list');
    if (!notebookList) return;

    // Clear existing content
    notebookList.innerHTML = '';

    // Render each notebook
    this.notebooks.forEach((notebook) => {
      const notebookItem = document.createElement('div');
      notebookItem.className = `notebook-item ${notebook.id === this.activeNotebook ? 'active' : ''}`;
      notebookItem.dataset.notebookId = notebook.id;

      // Create color indicator
      const colorIndicator = document.createElement('span');
      colorIndicator.className = 'notebook-color';
      colorIndicator.style.backgroundColor = notebook.color;

      // Create notebook name
      const nameSpan = document.createElement('span');
      nameSpan.className = 'notebook-name';
      nameSpan.textContent = notebook.name;

      // Create message count
      const countSpan = document.createElement('span');
      countSpan.className = 'notebook-count';
      countSpan.textContent = notebook.messages.length;

      // Add elements to notebook item
      notebookItem.appendChild(colorIndicator);
      notebookItem.appendChild(nameSpan);
      notebookItem.appendChild(countSpan);

      // Add delete button for custom notebooks (not 'All')
      if (notebook.id !== 'all') {
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'notebook-delete-btn';
        deleteBtn.innerHTML = '<i class="fa-solid fa-trash"></i>';
        deleteBtn.title = 'Delete notebook';
        deleteBtn.addEventListener('click', (e) => {
          e.stopPropagation(); // Prevent notebook selection when clicking delete
          this.showDeleteNotebookDialog(notebook.id);
        });
        notebookItem.appendChild(deleteBtn);
      }

      // Add click event to select notebook
      notebookItem.addEventListener('click', () => {
        this.activeNotebook = notebook.id;
        this.renderNotebooksList(); // Update active state
        this.renderSavedMessages(); // Show messages for selected notebook
        this.updatePDFStatusVisibility(); // Update PDF status visibility
      });

      // Make the notebook a drop target for messages
      this.makeNotebookDropTarget(notebookItem, notebook.id);

      notebookList.appendChild(notebookItem);
    });
  }

  /**
   * Show dialog to add a new notebook
   */
  showAddNotebookDialog() {
    // Create dialog element
    const dialog = document.createElement('div');
    dialog.className = 'notebook-dialog';
    dialog.innerHTML = `
      <div class="notebook-dialog-content">
        <h4>Add New Notebook</h4>
        <div class="notebook-dialog-form">
          <div class="form-group">
            <label for="notebook-name">Name</label>
            <input type="text" id="notebook-name" placeholder="Enter notebook name" maxlength="20">
          </div>
          <div class="form-group">
            <label>Color</label>
            <div class="color-options" id="color-options"></div>
          </div>
        </div>
        <div class="notebook-dialog-actions">
          <button class="cancel-btn">Cancel</button>
          <button class="save-btn" disabled>Create</button>
        </div>
      </div>
    `;

    // Add to DOM
    document.body.appendChild(dialog);

    // Add color options
    const colorOptions = dialog.querySelector('#color-options');
    let selectedColor = this.notebookColors[0];

    this.notebookColors.forEach((color, index) => {
      const colorOption = document.createElement('div');
      colorOption.className = 'color-option';

      // Use CSS custom properties for theme-aware colors
      // Create a data attribute for the color and let CSS handle the styling
      colorOption.dataset.color = color;
      colorOption.dataset.colorIndex = index;

      // Apply theme-aware styling using CSS classes instead of inline styles
      colorOption.classList.add(`notebook-color-${index}`);

      // Select first color by default
      if (color === selectedColor) {
        colorOption.classList.add('selected');
      }

      colorOption.addEventListener('click', () => {
        // Remove selected class from all options
        colorOptions.querySelectorAll('.color-option').forEach(opt => {
          opt.classList.remove('selected');
        });

        // Add selected class to clicked option
        colorOption.classList.add('selected');
        selectedColor = color;
      });

      colorOptions.appendChild(colorOption);
    });

    // Set up event listeners
    const nameInput = dialog.querySelector('#notebook-name');
    const saveBtn = dialog.querySelector('.save-btn');
    const cancelBtn = dialog.querySelector('.cancel-btn');

    // Enable/disable save button based on input
    nameInput.addEventListener('input', () => {
      saveBtn.disabled = !nameInput.value.trim();
    });

    // Cancel button closes dialog
    cancelBtn.addEventListener('click', () => {
      document.body.removeChild(dialog);
    });

    // Save button creates new notebook
    saveBtn.addEventListener('click', () => {
      const name = nameInput.value.trim();
      if (name) {
        this.createNotebook(name, selectedColor);
        document.body.removeChild(dialog);
      }
    });

    // Show dialog with animation
    setTimeout(() => {
      dialog.classList.add('active');
      nameInput.focus();
    }, 10);
  }

  /**
   * Create a new notebook
   * @param {string} name - Name of the notebook
   * @param {string} color - Color of the notebook
   */
  createNotebook(name, color) {
    // Generate a unique ID
    const id = 'nb-' + Date.now().toString();

    // Create new notebook object
    const newNotebook = {
      id,
      name,
      color,
      messages: []
    };

    // Add to notebooks array
    this.notebooks.push(newNotebook);

    // Save to localStorage
    this.saveNotebooks();

    // Switch to the new notebook
    this.activeNotebook = id;

    // Update UI
    this.renderNotebooksList();
    this.renderSavedMessages();

    // Show notification
    this.showNotification(`Notebook "${name}" created`);
  }

  /**
   * Show delete confirmation dialog for a notebook
   * @param {string} notebookId - ID of the notebook to delete
   */
  showDeleteNotebookDialog(notebookId) {
    // Cannot delete 'All' notebook
    if (notebookId === 'all') return;

    // Find the notebook
    const notebookIndex = this.notebooks.findIndex(nb => nb.id === notebookId);
    if (notebookIndex === -1) return;

    const notebook = this.notebooks[notebookIndex];

    // Create dialog element
    const dialog = document.createElement('div');
    dialog.className = 'notebook-dialog delete-dialog';
    dialog.innerHTML = `
      <div class="notebook-dialog-content">
        <h4>Delete Notebook</h4>
        <div class="notebook-dialog-form">
          <p>Are you sure you want to delete the notebook "${notebook.name}"?</p>
          <p class="delete-warning">This will remove the notebook but keep the messages in the All section.</p>
        </div>
        <div class="notebook-dialog-actions">
          <button class="cancel-btn">Cancel</button>
          <button class="delete-btn">Delete Notebook</button>
        </div>
      </div>
    `;

    // Add to DOM
    document.body.appendChild(dialog);

    // Set up event listeners
    const cancelBtn = dialog.querySelector('.cancel-btn');
    const deleteBtn = dialog.querySelector('.delete-btn');

    // Cancel button closes dialog
    cancelBtn.addEventListener('click', () => {
      document.body.removeChild(dialog);
    });

    // Delete button deletes the notebook
    deleteBtn.addEventListener('click', () => {
      this.deleteNotebook(notebookId);
      document.body.removeChild(dialog);
    });

    // Show dialog with animation
    setTimeout(() => {
      dialog.classList.add('active');
    }, 10);
  }

  /**
   * Delete a notebook
   * @param {string} notebookId - ID of the notebook to delete
   */
  deleteNotebook(notebookId) {
    // Cannot delete 'All' notebook
    if (notebookId === 'all') return;

    // Find the notebook
    const notebookIndex = this.notebooks.findIndex(nb => nb.id === notebookId);
    if (notebookIndex === -1) return;

    const notebook = this.notebooks[notebookIndex];

    // Remove the notebook
    this.notebooks.splice(notebookIndex, 1);

    // If the active notebook was deleted, switch to 'All'
    if (this.activeNotebook === notebookId) {
      this.activeNotebook = 'all';
    }

    // Save to localStorage
    this.saveNotebooks();

    // Update UI
    this.renderNotebooksList();
    this.renderSavedMessages();

    // Show notification
    this.showNotification(`Notebook "${notebook.name}" deleted`);
  }

  /**
   * Show menu to move a message to another notebook
   * @param {HTMLElement} buttonElement - The button that was clicked
   * @param {string} messageId - ID of the message to move
   */
  showMoveToNotebookMenu(buttonElement, messageId) {
    // Create menu element
    const menu = document.createElement('div');
    menu.className = 'move-to-menu';

    // Get custom notebooks (excluding 'All')
    const customNotebooks = this.notebooks.filter(nb => nb.id !== 'all');

    // If no custom notebooks, show message
    if (customNotebooks.length === 0) {
      menu.innerHTML = `
        <div class="move-to-menu-item no-notebooks">
          <span>No notebooks available</span>
        </div>
        <div class="move-to-menu-item create-notebook">
          <i class="fa-solid fa-plus"></i>
          <span>Create new notebook</span>
        </div>
      `;
    } else {
      // Create menu items for each notebook
      let menuHTML = '';
      customNotebooks.forEach(notebook => {
        menuHTML += `
          <div class="move-to-menu-item" data-notebook-id="${notebook.id}">
            <span class="notebook-color" style="background-color: ${notebook.color}"></span>
            <span>${notebook.name}</span>
          </div>
        `;
      });

      // Add option to create new notebook
      menuHTML += `
        <div class="move-to-menu-item create-notebook">
          <i class="fa-solid fa-plus"></i>
          <span>Create new notebook</span>
        </div>
      `;

      menu.innerHTML = menuHTML;
    }

    // Position menu near the button
    const buttonRect = buttonElement.getBoundingClientRect();
    menu.style.position = 'fixed';
    menu.style.top = `${buttonRect.bottom + 5}px`;
    menu.style.left = `${buttonRect.left}px`;

    // Add to DOM
    document.body.appendChild(menu);

    // Add event listeners to menu items
    menu.querySelectorAll('.move-to-menu-item[data-notebook-id]').forEach(item => {
      item.addEventListener('click', () => {
        const targetNotebookId = item.dataset.notebookId;
        this.moveMessageToNotebook(messageId, targetNotebookId);
        document.body.removeChild(menu);
      });
    });

    // Add event listener for create notebook option
    const createNotebookItem = menu.querySelector('.create-notebook');
    if (createNotebookItem) {
      createNotebookItem.addEventListener('click', () => {
        document.body.removeChild(menu);
        this.showAddNotebookDialog();
      });
    }

    // Close menu when clicking outside
    const closeMenu = (e) => {
      if (!menu.contains(e.target) && e.target !== buttonElement) {
        document.body.removeChild(menu);
        document.removeEventListener('click', closeMenu);
      }
    };

    // Add event listener with a delay to prevent immediate closing
    setTimeout(() => {
      document.addEventListener('click', closeMenu);
    }, 10);
  }

  /**
   * Move a message to another notebook
   * @param {string} messageId - ID of the message to move
   * @param {string} targetNotebookId - ID of the target notebook
   */
  moveMessageToNotebook(messageId, targetNotebookId) {
    // Find the source notebook (always 'All' when using the move button)
    const sourceNotebook = this.notebooks.find(nb => nb.id === 'all');
    if (!sourceNotebook) return;

    // Find the target notebook
    const targetNotebook = this.notebooks.find(nb => nb.id === targetNotebookId);
    if (!targetNotebook) return;

    // Find the message
    const messageIndex = sourceNotebook.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    // Clone the message
    const message = {...sourceNotebook.messages[messageIndex]};

    // Add to target notebook if not already there
    if (!targetNotebook.messages.some(m => m.id === messageId)) {
      targetNotebook.messages.push(message);
    }

    // Save to localStorage
    this.saveNotebooks();

    // Update UI
    this.renderNotebooksList();
    this.renderSavedMessages();

    // Show notification
    this.showNotification(`Message moved to "${targetNotebook.name}"`);
  }

  /**
   * Make a message draggable for moving between notebooks
   * @param {HTMLElement} element - The message element to make draggable
   * @param {string} messageId - ID of the message
   * @param {string} notebookId - ID of the source notebook
   */
  makeMessageDraggable(element, messageId, notebookId) {
    // Check if custom drag-drop is available
    if (!window.customDragDrop) return;

    // Make the element draggable
    window.customDragDrop.makeDraggable(
      element,
      () => {
        // On drag start
        element.classList.add('dragging');
        // Store data for the drag operation
        element.dataset.dragMessageId = messageId;
        element.dataset.dragSourceNotebook = notebookId;
      },
      (source, target) => {
        // On drag end
        element.classList.remove('dragging');

        // If dropped on a valid target
        if (target && target.classList.contains('notebook-item')) {
          const targetNotebookId = target.dataset.notebookId;
          // Don't move to the same notebook
          if (targetNotebookId !== notebookId) {
            this.moveMessageToNotebook(messageId, targetNotebookId);
          }
        }
      }
    );
  }

  /**
   * Make a notebook a drop target for messages
   * @param {HTMLElement} element - The notebook element
   * @param {string} notebookId - ID of the notebook
   */
  makeNotebookDropTarget(element, notebookId) {
    // Check if custom drag-drop is available
    if (!window.customDragDrop) return;

    // Register as drop target
    window.customDragDrop.registerDropTarget(
      element,
      (source) => {
        // On drag over
        if (source.dataset.dragSourceNotebook !== notebookId) {
          element.classList.add('drop-target');
        }
      },
      () => {
        // On drag leave
        element.classList.remove('drop-target');
      },
      (source) => {
        // On drop
        element.classList.remove('drop-target');
        // The actual move is handled in the draggable's onDragEnd callback
      }
    );
  }
}

// Create and export notebook instance
const notebook = new Notebook();

// Make notebook available globally
window.notebook = notebook;

/**
 * Initialize the notebook functionality
 */
export function initializeNotebook() {
  notebook.initialize();
}
