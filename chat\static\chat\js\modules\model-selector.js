/**
 * <PERSON>ódulo model-selector.js - Selector de modelos de LLM
 *
 * Este módulo implementa:
 * - Carga de modelos disponibles
 * - Interfaz para seleccionar modelos
 * - Cambio de modelo activo
 */

// Importar utilidades
import { getCookie } from './utils.js';

class ModelSelector {
    constructor() {
        this.models = {};
        this.activeProvider = '';
        this.activeModel = '';
        this.activeModelCapabilities = [];
        this.isInitialized = false;
        this.modelIndicator = document.querySelector('.model-indicator');

        // Inicializar eventos
        if (this.modelIndicator) {
            this.modelIndicator.addEventListener('click', () => this.openModelSelector());
        }
    }

    /**
     * Verifica si el modelo activo soporta entradas de imagen
     * @returns {boolean} - Indica si el modelo soporta imágenes
     */
    supportsImageInput() {
        // Siempre devuelve falso ya que la funcionalidad de imágenes ha sido eliminada
        return false;
    }

    /**
     * Inicializa el selector de modelos
     */
    async initialize() {
        try {
            // Cargar modelos disponibles
            await this.loadAvailableModels();

            // Crear modal si no existe
            this.createModalIfNeeded();

            // Actualizar el indicador de modelo con la clase del proveedor
            if (this.modelIndicator && this.activeProvider) {
                this.modelIndicator.classList.add(this.activeProvider);
            }

            this.isInitialized = true;
        } catch (error) {
            console.error('Error al inicializar el selector de modelos:', error);
        }
    }

    /**
     * Carga los modelos disponibles desde el servidor
     */
    async loadAvailableModels() {
        try {
            const response = await fetch('/chat/api/models/');
            const data = await response.json();

            if (data.status === 'success') {
                this.models = data.models;
                this.activeProvider = data.active_provider;
                this.activeModel = data.active_model;

                console.log('Modelos cargados:', this.models);
                console.log('Proveedor activo:', this.activeProvider);
                console.log('Modelo activo:', this.activeModel);

                // Obtener capacidades del modelo activo
                if (this.activeProvider && this.activeModel) {
                    const provider = this.models[this.activeProvider];
                    if (provider) {
                        const model = provider.models.find(m => m.id === this.activeModel);
                        if (model) {
                            this.activeModelCapabilities = model.capabilities || [];
                            console.log('Capacidades del modelo activo:', this.activeModelCapabilities);
                        }
                    }
                }

                // Actualizar el indicador de modelo
                this.updateModelIndicator();
            } else {
                console.error('Error al cargar modelos:', data.message);
            }
        } catch (error) {
            console.error('Error al cargar modelos:', error);
        }
    }

    /**
     * Actualiza el indicador de modelo en la UI
     */
    updateModelIndicator() {
        if (this.modelIndicator) {
            console.log('Actualizando indicador de modelo...');
            console.log('Modelo activo antes de actualizar:', this.modelIndicator.textContent);
            console.log('Nuevo modelo a mostrar:', this.activeModel);
            console.log('Nuevo proveedor a mostrar:', this.activeProvider);

            // Actualizar el texto del indicador
            this.modelIndicator.textContent = this.activeModel;

            // Tooltip removed

            // Actualizar la clase para el estilo
            this.modelIndicator.classList.remove('grok', 'groq', 'gemini', 'multi_agent', 'mistral');
            this.modelIndicator.classList.add(this.activeProvider);

            console.log(`Indicador de modelo actualizado a: ${this.activeModel} (${this.activeProvider})`);
            console.log('Clases del indicador después de actualizar:', this.modelIndicator.className);
        } else {
            console.error('No se encontró el elemento modelIndicator en el DOM');
        }
    }

    /**
     * Obtiene el nombre amigable del proveedor
     * @param {string} providerId - ID del proveedor
     * @returns {string} - Nombre amigable del proveedor
     */
    getProviderName(providerId) {
        const provider = this.models[providerId];
        return provider ? provider.name : providerId;
    }

    /**
     * Crea el modal de selección de modelos si no existe
     */
    createModalIfNeeded() {
        // Verificar si ya existe el modal
        if (document.getElementById('model-selector-modal')) {
            return;
        }

        // Crear el modal
        const modal = document.createElement('div');
        modal.id = 'model-selector-modal';
        modal.className = 'model-selector-modal';
        modal.innerHTML = `
            <div class="model-selector-content">
                <div class="model-selector-header">
                    <h3>Seleccionar Modelo</h3>
                    <button class="close-model-selector">&times;</button>
                </div>
                <div class="model-selector-body">
                    <div class="model-tabs"></div>
                    <div class="model-list"></div>
                </div>
            </div>
        `;

        // Agregar al DOM
        document.body.appendChild(modal);

        // Configurar eventos
        const closeBtn = modal.querySelector('.close-model-selector');
        closeBtn.addEventListener('click', () => this.closeModelSelector());

        // Cerrar al hacer clic fuera del contenido
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModelSelector();
            }
        });
    }

    /**
     * Abre el selector de modelos
     */
    async openModelSelector() {
        // Asegurar que esté inicializado
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Recargar modelos para asegurar datos actualizados
        await this.loadAvailableModels();

        const modal = document.getElementById('model-selector-modal');
        if (!modal) return;

        // Renderizar pestañas de proveedores
        this.renderProviderTabs();

        // Mostrar el modal
        modal.classList.add('active');

        // Seleccionar el proveedor activo por defecto
        this.selectProvider(this.activeProvider);
    }

    /**
     * Cierra el selector de modelos
     */
    closeModelSelector() {
        const modal = document.getElementById('model-selector-modal');
        if (modal) {
            modal.classList.remove('active');
        }
    }

    /**
     * Renderiza las pestañas de proveedores
     */
    renderProviderTabs() {
        const tabsContainer = document.querySelector('.model-tabs');
        if (!tabsContainer) return;

        tabsContainer.innerHTML = '';

        // Crear una pestaña para cada proveedor
        Object.keys(this.models).forEach(providerId => {
            const provider = this.models[providerId];
            const tab = document.createElement('div');
            tab.className = `model-tab ${providerId === this.activeProvider ? 'active' : ''}`;
            tab.setAttribute('data-provider', providerId);

            // Format provider name - replace underscores with spaces
            const displayName = provider.name.replace(/_/g, ' ');
            tab.textContent = displayName;

            tab.addEventListener('click', () => this.selectProvider(providerId));

            tabsContainer.appendChild(tab);
        });
    }

    /**
     * Selecciona un proveedor y muestra sus modelos
     * @param {string} providerId - ID del proveedor
     */
    selectProvider(providerId) {
        // Actualizar pestañas activas
        const tabs = document.querySelectorAll('.model-tab');
        tabs.forEach(tab => {
            tab.classList.toggle('active', tab.getAttribute('data-provider') === providerId);
        });

        // Renderizar modelos del proveedor
        this.renderModels(providerId);
    }

    /**
     * Renderiza los modelos de un proveedor
     * @param {string} providerId - ID del proveedor
     */
    renderModels(providerId) {
        const modelListContainer = document.querySelector('.model-list');
        if (!modelListContainer) return;

        modelListContainer.innerHTML = '';

        const provider = this.models[providerId];
        if (!provider || !provider.models) return;

        // Create a card for each model
        provider.models.forEach(model => {
            const modelCard = document.createElement('div');
            modelCard.className = `model-card ${model.id === this.activeModel ? 'active' : ''}`;
            modelCard.setAttribute('data-model-id', model.id);

            // Format model name - replace underscores with spaces
            const displayName = model.name.replace(/_/g, ' ');

            // Create card content
            let capabilitiesHtml = '';
            if (model.capabilities && model.capabilities.length > 0) {
                capabilitiesHtml = `
                    <div class="model-capabilities">
                        ${model.capabilities.map(cap => `<span class="capability-tag">${cap}</span>`).join('')}
                    </div>
                `;
            }

            modelCard.innerHTML = `
                <div class="model-info">
                    <h4 class="model-name">${displayName}</h4>
                    <p class="model-description">${model.description || ''}</p>
                    ${capabilitiesHtml}
                </div>
                <button class="select-model-btn ${model.id === this.activeModel ? 'active' : ''}">
                    ${model.id === this.activeModel ? 'Activo' : 'Seleccionar'}
                </button>
            `;

            // Configure selection event
            const selectBtn = modelCard.querySelector('.select-model-btn');
            selectBtn.addEventListener('click', () => this.switchModel(providerId, model.id));

            modelListContainer.appendChild(modelCard);
        });
    }

    /**
     * Cambia el modelo activo
     * @param {string} providerId - ID del proveedor
     * @param {string} modelId - ID del modelo
     */
    async switchModel(providerId, modelId) {
        try {
            // No hacer nada si ya es el modelo activo
            if (providerId === this.activeProvider && modelId === this.activeModel) {
                this.closeModelSelector();
                return;
            }

            // Mostrar indicador de carga
            this.setLoadingState(true);

            // Obtener el token CSRF
            const csrftoken = getCookie('csrftoken');

            // Enviar solicitud al servidor
            const response = await fetch('/chat/api/switch_model/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrftoken
                },
                body: JSON.stringify({
                    provider: providerId,
                    model: modelId
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Actualizar estado local
                this.activeProvider = providerId;
                this.activeModel = data.model_info.name;

                // Obtener información del modelo para las capacidades
                const provider = this.models[providerId];
                if (provider) {
                    const model = provider.models.find(m => m.id === modelId);
                    if (model) {
                        this.activeModelCapabilities = model.capabilities || [];
                    }
                }

                // Actualizar UI
                this.updateModelIndicator();

                // Cerrar el selector
                this.closeModelSelector();

                // Recargar la lista de modelos para asegurar que todo esté actualizado
                this.loadAvailableModels();
            } else {
                console.error('Error al cambiar modelo:', data.message);
            }
        } catch (error) {
            console.error('Error al cambiar de modelo:', error);
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * Establece el estado de carga
     * @param {boolean} isLoading - Indica si está cargando
     */
    setLoadingState(isLoading) {
        // Actualizar cursor
        document.body.style.cursor = isLoading ? 'wait' : '';

        // Deshabilitar botones durante la carga
        const buttons = document.querySelectorAll('.select-model-btn');
        buttons.forEach(btn => {
            btn.disabled = isLoading;
        });
    }
}

// Exportar una instancia única
export const modelSelector = new ModelSelector();

/**
 * Inicializa el selector de modelos
 */
export function initializeModelSelector() {
    // Hacer disponible globalmente
    window.modelSelector = modelSelector;

    // Inicializar
    modelSelector.initialize();
}
