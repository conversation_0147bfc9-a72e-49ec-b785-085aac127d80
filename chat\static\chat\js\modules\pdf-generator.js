/**
 * Module pdf-generator.js - PDF generation functionality for notebooks
 *
 * This module handles the generation of PDFs from notebook content
 * using LaTeX conversion through the backend API.
 */

export class PDFGenerator {
  constructor() {
    this.pdfStatus = {}; // Track PDF generation status by notebook ID
    this.statusCheckInterval = null;
  }

  /**
   * Generate a PDF for a notebook
   * @param {Object} notebook - Notebook object with messages
   * @returns {Promise} - Promise that resolves with the PDF generation result
   */
  async generatePDF(notebook) {
    try {
      // Show loading notification
      this.showNotification('Generating PDF...', 'info');

      // Call the API to generate the PDF
      const response = await fetch('/chat/api/notebook/generate-pdf/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        },
        body: JSON.stringify({ notebook })
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // Start checking status if processing
      if (result.status === 'processing') {
        this.startStatusCheck(notebook.id);
      }

      // Show success or error notification
      if (result.status === 'completed') {
        this.showNotification('PDF generated successfully', 'success');
      } else if (result.status === 'error') {
        this.showNotification(`Error: ${result.message}`, 'error');
      }

      return result;
    } catch (error) {
      console.error('Error generating PDF:', error);
      this.showNotification(`Error generating PDF: ${error.message}`, 'error');
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Start checking the status of PDF generation
   * @param {string} notebookId - ID of the notebook
   */
  startStatusCheck(notebookId) {
    // Clear any existing interval
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
    }

    // Set up status check interval (every 2 seconds)
    this.statusCheckInterval = setInterval(async () => {
      const status = await this.checkPDFStatus(notebookId);

      // Update UI based on status
      this.updatePDFStatusUI(notebookId, status);

      // If completed or error, stop checking
      if (status.status === 'completed' || status.status === 'error') {
        clearInterval(this.statusCheckInterval);
        this.statusCheckInterval = null;

        // Show notification
        if (status.status === 'completed') {
          this.showNotification('PDF generated successfully', 'success');
        } else if (status.status === 'error') {
          this.showNotification(`Error: ${status.message}`, 'error');
        }
      }
    }, 2000);
  }

  /**
   * Check the status of PDF generation
   * @param {string} notebookId - ID of the notebook
   * @returns {Promise} - Promise that resolves with the PDF status
   */
  async checkPDFStatus(notebookId) {
    try {
      const response = await fetch(`/chat/api/notebook/pdf-status/${notebookId}/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        }
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const status = await response.json();
      this.pdfStatus[notebookId] = status;
      return status;
    } catch (error) {
      console.error('Error checking PDF status:', error);
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Update the UI based on PDF generation status
   * @param {string} notebookId - ID of the notebook
   * @param {Object} status - Status object
   */
  updatePDFStatusUI(notebookId, status) {
    // Find the PDF status element
    const statusElement = document.querySelector(`.pdf-status[data-notebook-id="${notebookId}"]`);
    if (!statusElement) return;

    // Update status text and progress
    const statusText = statusElement.querySelector('.pdf-status-text');
    const progressBar = statusElement.querySelector('.pdf-progress-bar');

    if (statusText) {
      statusText.textContent = status.message || 'Unknown status';
    }

    if (progressBar) {
      progressBar.style.width = `${status.progress || 0}%`;
    }

    // Show/hide buttons based on status
    const previewBtn = document.querySelector(`.preview-pdf-btn[data-notebook-id="${notebookId}"]`);
    const downloadBtn = document.querySelector(`.download-pdf-btn[data-notebook-id="${notebookId}"]`);
    const deleteBtn = document.querySelector(`.delete-pdf-btn[data-notebook-id="${notebookId}"]`);

    if (previewBtn && downloadBtn && deleteBtn) {
      if (status.status === 'completed') {
        previewBtn.style.display = 'inline-flex';
        downloadBtn.style.display = 'inline-flex';
        deleteBtn.style.display = 'inline-flex';
      } else {
        previewBtn.style.display = 'none';
        downloadBtn.style.display = 'none';
        deleteBtn.style.display = 'none';
      }
    }
  }

  /**
   * Preview a generated PDF
   * @param {string} notebookId - ID of the notebook
   */
  previewPDF(notebookId) {
    // Open the PDF in a new tab
    window.open(`/chat/api/notebook/preview-pdf/${notebookId}/`, '_blank');
  }

  /**
   * Download a generated PDF
   * @param {string} notebookId - ID of the notebook
   */
  downloadPDF(notebookId) {
    // Trigger download
    window.location.href = `/chat/api/notebook/download-pdf/${notebookId}/`;
  }

  /**
   * Delete a generated PDF
   * @param {string} notebookId - ID of the notebook
   * @returns {Promise} - Promise that resolves with the delete result
   */
  async deletePDF(notebookId) {
    try {
      // Call the API to delete the PDF
      const response = await fetch(`/chat/api/notebook/delete-pdf/${notebookId}/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        }
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // Remove from status tracking
      if (this.pdfStatus[notebookId]) {
        delete this.pdfStatus[notebookId];
      }

      // Show success notification
      this.showNotification('PDF deleted successfully', 'success');

      return result;
    } catch (error) {
      console.error('Error deleting PDF:', error);
      this.showNotification(`Error deleting PDF: ${error.message}`, 'error');
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Load PDF status for a notebook
   * @param {string} notebookId - ID of the notebook
   * @returns {Promise} - Promise that resolves with the PDF status
   */
  async loadPDFStatus(notebookId) {
    try {
      // Check if we already have the status
      if (this.pdfStatus[notebookId] && this.pdfStatus[notebookId].status === 'completed') {
        return this.pdfStatus[notebookId];
      }

      // Call the API to get the PDF status
      const status = await this.checkPDFStatus(notebookId);

      // If the status is completed, store it
      if (status.status === 'completed') {
        this.pdfStatus[notebookId] = status;

        // If the notebook is currently active, update the UI
        if (window.notebook && window.notebook.activeNotebook === notebookId) {
          window.notebook.updatePDFStatusVisibility();
        }
      }

      return status;
    } catch (error) {
      console.error(`Error loading PDF status for notebook ${notebookId}:`, error);
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Get the CSRF token from cookies
   * @returns {string} - CSRF token
   */
  getCSRFToken() {
    const name = 'csrftoken';
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  /**
   * Show a notification
   * @param {string} message - Notification message
   * @param {string} type - Notification type (info, success, error)
   */
  showNotification(message, type = 'info') {
    // Check if notification container exists
    let container = document.getElementById('notification-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'notification-container';
      document.body.appendChild(container);
    }

    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-message">${message}</span>
        <button class="notification-close">&times;</button>
      </div>
    `;

    // Add close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        container.removeChild(notification);
      });
    }

    // Add to container
    container.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode === container) {
        container.removeChild(notification);
      }
    }, 3000);
  }
}

// Create and export PDF generator instance
const pdfGenerator = new PDFGenerator();

// Make PDF generator available globally
window.pdfGenerator = pdfGenerator;

/**
 * Initialize the PDF generator functionality
 */
export function initializePDFGenerator() {
  console.log('PDF Generator initialized');

  // Load PDF status for all notebooks
  if (window.notebook && window.notebook.notebooks) {
    window.notebook.notebooks.forEach(notebook => {
      if (notebook.id !== 'all') {
        pdfGenerator.loadPDFStatus(notebook.id);
      }
    });
  }
}
