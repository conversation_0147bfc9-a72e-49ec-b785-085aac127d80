/**
 * Módulo global-scroll.js - Configuración de scroll global
 *
 * Este módulo asegura que el scroll funcione en toda la página,
 * sin importar dónde esté posicionado el cursor.
 */

/**
 * Inicializa la configuración de scroll global
 */
export function initializeGlobalScroll() {
  // Forzar scroll global en el documento
  document.documentElement.style.overflowY = 'scroll';
  document.documentElement.style.height = '100%';
  document.documentElement.style.scrollbarWidth = 'none';
  document.documentElement.style.msOverflowStyle = 'none';

  document.body.style.overflowY = 'visible';
  document.body.style.overflowX = 'hidden';
  document.body.style.display = 'block';
  document.body.style.minHeight = '100vh';
  // Asegurarse de que los fondos sean transparentes o tengan el gradiente correcto
  document.body.style.background = 'transparent';
  document.documentElement.style.background = 'linear-gradient(135deg, var(--chat-bg-secondary) 0%, var(--chat-bg-primary) 100%)';

  // Crear un elemento de fondo si no existe
  if (!document.querySelector('.dynamic-background')) {
    const dynamicBg = document.createElement('div');
    dynamicBg.className = 'dynamic-background';
    dynamicBg.style.position = 'fixed';
    dynamicBg.style.top = '0';
    dynamicBg.style.left = '0';
    dynamicBg.style.width = '100%';
    dynamicBg.style.height = '100%';
    dynamicBg.style.zIndex = '-10';
    dynamicBg.style.background = 'linear-gradient(135deg, var(--chat-bg-secondary) 0%, var(--chat-bg-primary) 100%)';
    dynamicBg.style.pointerEvents = 'none';
    document.body.appendChild(dynamicBg);
  }

  // Forzar que todos los contenedores principales permitan el scroll global
  const containers = [
    '.main-content',
    '.chat-section',
    '.chat-container',
    '.chat-box',
    '.chat-box-content'
  ];

  containers.forEach(selector => {
    const element = document.querySelector(selector);
    if (element) {
      element.style.overflow = 'visible';
      element.style.overflowY = 'visible';
      element.style.display = 'block';
    }
  });

  // Asegurarse de que los mensajes no interfieran con el scroll global
  document.querySelectorAll('.chat-message').forEach(message => {
    message.style.overflow = 'visible';
  });

  // Observar cambios en el DOM para aplicar estilos a nuevos mensajes
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.classList && node.classList.contains('chat-message')) {
              node.style.overflow = 'visible';
            }

            const messages = node.querySelectorAll('.chat-message');
            messages.forEach(message => {
              message.style.overflow = 'visible';
            });
          }
        });
      }
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // Ocultar scrollbar en WebKit (Chrome, Safari)
  const style = document.createElement('style');
  style.textContent = `
    ::-webkit-scrollbar {
      display: none;
    }
  `;
  document.head.appendChild(style);
}
