"""
Middleware para la aplicación de chat.
"""
from django.utils.deprecation import MiddlewareMixin
from .llm_providers.context import LLMRequestContext

class LLMContextMiddleware(MiddlewareMixin):
    """
    Middleware que inicializa el contexto LLM para cada solicitud.
    """
    
    def process_request(self, request):
        """
        Procesa la solicitud y crea el contexto LLM.
        
        Args:
            request: Objeto HttpRequest de Django
        """
        # Solo procesar solicitudes autenticadas
        if hasattr(request, 'user') and request.user.is_authenticated:
            # Crear contexto a partir de la sesión
            request.llm_context = LLMRequestContext(request)
        return None
