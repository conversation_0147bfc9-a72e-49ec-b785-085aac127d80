/**
 * particles.js - Creates and manages background particles
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize particles
  initParticles();
});

/**
 * Initialize particles in the background
 */
function initParticles() {
  const container = document.getElementById('particles-container');
  if (!container) return;

  // Clear any existing particles
  container.innerHTML = '';

  // Create particles
  const particleCount = window.innerWidth < 768 ? 15 : 30;
  const sizes = ['particle-sm', 'particle-md', 'particle-lg'];
  const colors = ['particle-primary', 'particle-secondary', 'particle-tertiary'];

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div');
    const sizeClass = sizes[Math.floor(Math.random() * sizes.length)];
    const colorIndex = Math.floor(Math.random() * 4) + 1; // Use 1-4 for CSS variables

    particle.className = `particle ${sizeClass}`;

    // Apply theme-aware color using CSS custom properties
    particle.style.backgroundColor = `var(--particle-color-${colorIndex})`;

    // Random position
    particle.style.left = `${Math.random() * 100}%`;
    particle.style.top = `${Math.random() * 100}%`;

    // Random animation delay
    particle.style.animationDelay = `${Math.random() * 5}s`;

    // Add to container
    container.appendChild(particle);
  }

  // Add a few glow effects
  const glowCount = window.innerWidth < 768 ? 2 : 4;
  const glowColors = ['glow-primary', 'glow-secondary', 'glow-tertiary'];

  for (let i = 0; i < glowCount; i++) {
    const glow = document.createElement('div');
    glow.className = `glow ${glowColors[Math.floor(Math.random() * glowColors.length)]}`;

    // Random position
    glow.style.left = `${Math.random() * 100}%`;
    glow.style.top = `${Math.random() * 100}%`;

    // Add to container
    container.appendChild(glow);
  }
}

// Handle window resize
window.addEventListener('resize', function() {
  // Reinitialize particles on window resize
  initParticles();
});
