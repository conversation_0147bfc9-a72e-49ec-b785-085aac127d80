# llm_interface.py
import time
import json
import re
import logging
from typing import List, Dict, Any, Optional, Literal, Tuple

# Import clients and config from config module
from config import (
    groq_client,
    grok_xai_client,
    LLM_MAX_ATTEMPTS,
    DEBUG_MODE,
    check_client_availability,
    DEFAULT_TEMPERATURE
)
from openai import (
    OpenAI,
    APIConnectionError,
    RateLimitError,
    APITimeoutError,
    APIStatusError,
    AuthenticationError,
    APIError # Generic API error
)

# Define custom exceptions
class LLMError(Exception):
    """Base exception for LLM call errors."""
    def __init__(self, message, provider=None, model=None, original_exception=None):
        super().__init__(message)
        self.provider = provider
        self.model = model
        self.original_exception = original_exception

class ProviderClientError(LLMError):
    """Exception raised when the client for a provider is not available."""
    pass

class APICommunicationError(LLMError):
    """Exception raised for potentially transient API communication issues."""
    pass

def call_llm(
    provider: Literal["groq", "grok"], # Extend with more providers as needed
    model_name: str,
    messages: List[Dict[str, str]],
    temperature: float = DEFAULT_TEMPERATURE,
    max_tokens: int = 4000,
    response_format: Optional[Dict[str, str]] = None, # e.g., {"type": "json_object"}
    max_attempts: int = LLM_MAX_ATTEMPTS
) -> str:
    """
    Unified function to call different LLM providers with retry logic
    and standardized error handling.

    Args:
        provider: The LLM provider ('groq', 'grok').
        model_name: The specific model name for the provider.
        messages: The list of messages for the chat completion.
        temperature: The generation temperature.
        max_tokens: The maximum number of tokens to generate.
        response_format: OpenAI API response_format parameter.
        max_attempts: Maximum number of retry attempts.

    Returns:
        The LLM response content as a string.

    Raises:
        ProviderClientError: If the client for the specified provider is not initialized.
        APICommunicationError: If a potentially transient API error occurs after retries.
        LLMError: For other non-retriable API errors or unexpected issues.
    """
    client: Optional[OpenAI] = None
    provider_name = provider.upper()

    if provider == "groq":
        client = groq_client
        provider_name = "Groq"
    elif provider == "grok":
        client = grok_xai_client
        provider_name = "Grok (xAI)"
    # Add elif for other providers

    if not client:
        error_msg = f"Client for provider '{provider_name}' is not initialized. Check API Key and configuration."
        logging.error(error_msg)
        raise ProviderClientError(error_msg, provider=provider)

    if not check_client_availability(provider):
         error_msg = f"Attempted to call {provider_name} model '{model_name}', but the client is not available or failed initialization."
         logging.error(error_msg)
         raise ProviderClientError(error_msg, provider=provider)


    logging.info(f"Calling {provider_name} model: {model_name} (Max Tokens: {max_tokens}, Temp: {temperature})")
    if DEBUG_MODE:
        # Avoid logging full messages in production if they contain sensitive data
        messages_preview = json.dumps(messages)[:500] + ('...' if len(json.dumps(messages)) > 500 else '')
        logging.debug(f"Messages ({len(messages)} items): {messages_preview}")
        logging.debug(f"Parameters: temp={temperature}, max_tokens={max_tokens}" + (f", response_format={response_format}" if response_format else ""))

    last_exception: Optional[Exception] = None

    for attempt in range(1, max_attempts + 1):
        start_time = time.time()
        try:
            request_args = {
                "model": model_name,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                # Note: timeout is often set at client level now
                # "timeout": LLM_TIMEOUT_SECONDS # Can be set per-request too if needed
            }
            if response_format:
                if isinstance(response_format, dict) and "type" in response_format:
                    request_args["response_format"] = response_format
                else:
                    logging.warning(f"Invalid response_format specified: {response_format}. Ignoring.")

            response = client.chat.completions.create(**request_args)
            elapsed_time = time.time() - start_time
            logging.info(f"[TIMING] {provider_name} model {model_name} call took {elapsed_time:.2f}s", extra={'is_timing': True})


            finish_reason = getattr(response.choices[0], 'finish_reason', 'unknown')
            if finish_reason == 'length':
                logging.warning(f"{provider_name} response may be truncated (max_tokens={max_tokens}). Finish Reason: {finish_reason}")
            elif finish_reason not in ['stop', 'tool_calls', 'eos']: # Common stop reasons
                 logging.warning(f"{provider_name} response finished unexpectedly. Finish Reason: {finish_reason}")

            # Handle potential None content (e.g., tool calls without text)
            content = response.choices[0].message.content
            if content is None:
                 # Check if it was due to tool calls (expected) or something else
                 if response.choices[0].message.tool_calls:
                      logging.info(f"{provider_name} response contains tool calls, content is None.")
                      # Decide how to handle tool calls - this function expects text response for now
                      # Might need modification if tool calls should be returned directly
                      return "" # Return empty string if only tool calls
                 else:
                      logging.warning(f"{provider_name} model {model_name} returned None content unexpectedly. Response obj: {response.choices[0].message}")
                      return "" # Return empty string or raise error?

            return content.strip()

        except (APIConnectionError, RateLimitError, APITimeoutError, APIStatusError) as e:
            elapsed_time = time.time() - start_time
            logging.warning(f"Attempt {attempt}/{max_attempts}: API communication error calling {provider_name} model {model_name} after {elapsed_time:.2f}s: {type(e).__name__} - {e}")
            last_exception = APICommunicationError(f"API Error: {e}", provider=provider, model=model_name, original_exception=e)

        except AuthenticationError as e:
             elapsed_time = time.time() - start_time
             logging.error(f"Authentication Error calling {provider_name} model {model_name} after {elapsed_time:.2f}s: {e}. Check API Key.")
             # Non-retriable
             raise LLMError(f"Authentication failed for {provider_name}: {e}", provider=provider, model=model_name, original_exception=e) from e

        except APIError as e: # Catch other specific API errors if needed
            elapsed_time = time.time() - start_time
            logging.error(f"Non-retriable API Error calling {provider_name} model {model_name} after {elapsed_time:.2f}s: {type(e).__name__} - {e}")
            # Depending on the error type (e.g., InvalidRequestError), might not be retriable
            last_exception = LLMError(f"API Error: {e}", provider=provider, model=model_name, original_exception=e)
            # Decide if this specific APIError subclass is retriable or not
            # For now, assume most other APIErrors are not transient
            break # Exit retry loop for assumed non-retriable API errors


        except Exception as e:
            elapsed_time = time.time() - start_time
            logging.error(f"Attempt {attempt}/{max_attempts}: Unexpected error calling {provider_name} model {model_name} after {elapsed_time:.2f}s: {type(e).__name__} - {e}", exc_info=DEBUG_MODE)
            last_exception = LLMError(f"Unexpected error: {e}", provider=provider, model=model_name, original_exception=e)
            # Potentially break if the error seems non-recoverable
            # break # Uncomment if you want to stop retrying on any general exception


        # Retry logic
        if attempt < max_attempts:
            retry_delay = 2 ** attempt # Exponential backoff
            logging.info(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
        else:
             logging.error(f"Max retries ({max_attempts}) reached for {provider_name} model {model_name}.")
             if isinstance(last_exception, APICommunicationError):
                  raise last_exception # Raise the specific communication error
             else:
                  # Raise generic LLM error if the last exception was different or unexpected
                  raise LLMError(
                      f"Failed after {max_attempts} attempts. Last error: {last_exception or 'Unknown error'}",
                      provider=provider, model=model_name, original_exception=last_exception
                      ) from last_exception

    # Should not be reachable if loop completes or raises
    raise LLMError("Exited call_llm loop unexpectedly", provider=provider, model=model_name)