/* think-container.css - Styles for collapsible think containers */

/* Base think container */
.think-container {
  background: rgba(var(--bg-tertiary-rgb), 0.2);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  transition: var(--transition-theme);
  position: relative;
}

/* Think header */
.think-header {
  background: rgba(var(--bg-tertiary-rgb), 0.5);
  color: var(--accent-primary);
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: var(--transition-theme);
  position: relative; /* Para posicionar el toggle button dentro del header */
  padding-right: 40px; /* Espacio para el botón de toggle */
}

.think-header:hover {
  background: rgba(var(--bg-tertiary-rgb), 0.7);
}

.think-header.expanded {
  background: rgba(var(--bg-tertiary-rgb), 0.8);
}

/* Think bubble (content) */
.think-bubble {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height);
  padding: var(--spacing-md);
  max-height: 0;
  overflow: hidden;
  transition: var(--transition-theme), max-height 0.3s ease, padding 0.3s ease;
  position: relative;
  padding-top: 0;
  padding-bottom: 0;
}

.think-bubble.scrollable {
  overflow-y: auto;
}

/* Toggle button */
.think-toggle {
  color: var(--text-dimmed);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: var(--transition-theme);
  z-index: 2;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  /* Asegurar que el botón permanezca en el header */
  position: absolute;
  right: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
}

.think-toggle:hover {
  color: var(--accent-primary);
  background-color: rgba(var(--bg-primary-rgb), 0.1);
}

/* Mist overlay for collapsed state */
.mist-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom, transparent, rgba(var(--bg-tertiary-rgb), 0.7));
  pointer-events: none;
  transition: var(--transition-theme), opacity 0.3s ease;
  opacity: 0;
}

.think-container.expanded .mist-overlay {
  opacity: 1;
}

.mist-overlay.hidden {
  opacity: 0 !important;
}

/* Expanded state */
.think-container.expanded .think-bubble {
  max-height: 200px;
  padding: var(--spacing-md);
}

/* Light theme styles - Café Study Theme */
.light-theme .think-container {
  background: rgba(var(--bg-secondary-rgb), 0.3);
  border-color: rgba(var(--accent-primary-rgb), 0.2);
}

.light-theme .think-header {
  background: rgba(var(--bg-secondary-rgb), 0.7);
  color: var(--accent-primary);
}

.light-theme .think-header:hover {
  background: rgba(var(--bg-secondary-rgb), 0.9);
}

.light-theme .think-header.expanded {
  background: rgba(var(--bg-secondary-rgb), 1);
}

.light-theme .think-bubble {
  color: var(--text-primary);
}

.light-theme .mist-overlay {
  background: linear-gradient(to bottom, transparent, rgba(var(--bg-secondary-rgb), 0.7));
}

.light-theme .think-toggle {
  color: var(--accent-primary);
}

.light-theme .think-toggle:hover {
  background-color: rgba(var(--accent-primary-rgb), 0.1);
  color: var(--accent-secondary);
}

