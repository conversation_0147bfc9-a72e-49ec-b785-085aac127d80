/**
 * Módulo para manejar respuestas de tipo imagen.
 */
const ImageHandler = {
    /**
     * Inicializa el manejador de imágenes.
     */
    init() {
        console.log('ImageHandler inicializado');
    },

    /**
     * Maneja una respuesta de tipo imagen.
     * @param {Object} response - Respuesta del servidor con la URL de la imagen.
     * @returns {HTMLElement} - Elemento con la imagen.
     */
    handleImageResponse(response) {
        console.log('Procesando respuesta de tipo imagen:', response);
        
        if (!response || !response.data) {
            console.error('Respuesta de imagen inválida');
            return null;
        }

        try {
            const imageUrl = response.data;
            
            // Crear contenedor para la imagen
            const imageContainer = document.createElement('div');
            imageContainer.className = 'image-container';
            
            // Crear la imagen
            const image = document.createElement('img');
            image.src = imageUrl;
            image.alt = 'Imagen generada';
            image.className = 'generated-image';
            
            // Añadir evento para manejar errores de carga
            image.onerror = () => {
                console.error('Error al cargar la imagen:', imageUrl);
                image.alt = 'Error al cargar la imagen';
                image.classList.add('image-error');
            };
            
            // Crear pie de imagen con la URL
            const imageFooter = document.createElement('div');
            imageFooter.className = 'image-footer';
            
            // Botón para abrir la imagen en una nueva pestaña
            const openButton = document.createElement('button');
            openButton.className = 'image-action-btn';
            openButton.innerHTML = '<i class="fa-solid fa-external-link-alt"></i> Abrir imagen';
            openButton.onclick = () => window.open(imageUrl, '_blank');
            
            // Botón para copiar la URL
            const copyButton = document.createElement('button');
            copyButton.className = 'image-action-btn';
            copyButton.innerHTML = '<i class="fa-solid fa-copy"></i> Copiar URL';
            copyButton.onclick = () => {
                navigator.clipboard.writeText(imageUrl)
                    .then(() => {
                        copyButton.innerHTML = '<i class="fa-solid fa-check"></i> URL copiada';
                        setTimeout(() => {
                            copyButton.innerHTML = '<i class="fa-solid fa-copy"></i> Copiar URL';
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Error al copiar URL:', err);
                        copyButton.innerHTML = '<i class="fa-solid fa-times"></i> Error al copiar';
                        setTimeout(() => {
                            copyButton.innerHTML = '<i class="fa-solid fa-copy"></i> Copiar URL';
                        }, 2000);
                    });
            };
            
            // Añadir botones al pie de imagen
            imageFooter.appendChild(openButton);
            imageFooter.appendChild(copyButton);
            
            // Añadir imagen y pie al contenedor
            imageContainer.appendChild(image);
            imageContainer.appendChild(imageFooter);
            
            return imageContainer;
        } catch (e) {
            console.error('Error al procesar respuesta de imagen:', e);
            return null;
        }
    }
};

// Exportar el módulo
window.ImageHandler = ImageHandler;
