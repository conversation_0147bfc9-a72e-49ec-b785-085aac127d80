"""
Proveedor de LLM para Groq.
"""
from django.conf import settings
import logging
from typing import Dict, List, Optional, Any
from langchain.chains import <PERSON><PERSON>hain
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.messages import SystemMessage, AIMessage, HumanMessage
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langchain_groq import ChatGroq

from .base import BaseLLMProvider

logger = logging.getLogger(__name__)

class GroqProvider(BaseLLMProvider):
    """Implementación del proveedor de LLM para Groq."""

    # Modelos disponibles de Groq
    AVAILABLE_MODELS = {
        'deepseek-r1-distill-llama-70b': {
            'id': 'deepseek-r1-distill-llama-70b',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 4096,
            'description': 'Modelo principal de Groq basado en DeepSeek'
        },
        'llama3-8b-8192': {
            'id': 'llama3-8b-8192',
            'capabilities': ['text', 'code'],
            'max_tokens': 8192,
            'description': 'Llama 3 8B optimizado por Groq'
        },
        'llama3-70b-8192': {
            'id': 'llama3-70b-8192',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 8192,
            'description': 'Llama 3 70B optimizado por Groq'
        },
        'qwen-qwq-32b': {
            'id': 'qwen-qwq-32b',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 4096,
            'description': 'Qwen QWQ 32B con capacidades avanzadas de razonamiento',
            'temperature': 0.6,
            'top_p': 0.95
        }
    }

    def __init__(self):
        """Inicializa el proveedor de Groq."""
        self._model_name = "deepseek-r1-distill-llama-70b"
        self._groq_chat = ChatGroq(
            groq_api_key=settings.GROQ_API_KEY,
            model_name=self._model_name
        )

    def set_model(self, model_name: str) -> None:
        """
        Configura el modelo Groq a utilizar.

        Args:
            model_name: Nombre del modelo Groq a utilizar.
        """
        if model_name in self.AVAILABLE_MODELS:
            self._model_name = model_name
            # Recrear el cliente con el nuevo modelo
            self._groq_chat = ChatGroq(
                groq_api_key=settings.GROQ_API_KEY,
                model_name=model_name
            )
            logger.info(f"Modelo Groq cambiado a: {model_name}")
        else:
            logger.warning(f"Modelo Groq '{model_name}' no reconocido. Manteniendo {self._model_name}.")

    @property
    def model_name(self) -> str:
        """Nombre del modelo utilizado."""
        return self._model_name

    def query(self, user_input: str, memory_instance: ConversationBufferWindowMemory) -> str:
        """
        Consulta el modelo Groq con la entrada del usuario y la memoria de conversación.

        Args:
            user_input: Texto de entrada del usuario.
            memory_instance: Instancia de memoria de conversación.

        Returns:
            Respuesta del modelo como texto.
        """
        try:
            # Sistema de prompts simplificado
            system_prompt = """Eres un asistente de chat útil y amigable. Responde de manera clara y concisa."""

            prompt = ChatPromptTemplate.from_messages([
                SystemMessage(content=system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{human_input}"),
            ])

            conversation = LLMChain(llm=self._groq_chat, prompt=prompt, memory=memory_instance)
            return conversation.predict(human_input=user_input)

        except Exception as e:
            logger.error(f"Error al consultar Groq: {e}")
            return f"Error: {e}"

    def get_model_info(self) -> Dict[str, Any]:
        """
        Obtiene información sobre el modelo.

        Returns:
            Diccionario con información del modelo.
        """
        model_info = self.AVAILABLE_MODELS.get(self._model_name, {})
        return {
            "name": self._model_name,
            "provider": "Groq",
            "max_tokens": model_info.get('max_tokens'),
            "temperature": 0.7,  # Valor por defecto
            "capabilities": model_info.get('capabilities', ['text', 'code']),
            "description": model_info.get('description', '')
        }

    @classmethod
    def get_available_models(cls) -> Dict[str, Dict[str, Any]]:
        """
        Obtiene la lista de modelos disponibles.

        Returns:
            Diccionario con información de los modelos disponibles.
        """
        return cls.AVAILABLE_MODELS
