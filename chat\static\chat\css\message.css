/* message.css - Styles for chat messages */

/* Message container */
.message,
.chat-message {
  display: flex;
  flex-direction: column;
  width: fit-content;
  max-width: 100%;
  animation: fadeIn var(--animation-speed) ease-out;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  line-height: var(--line-height);
  font-size: var(--font-size);
}

/* User message */
.user-message,
.chat-message.user-message {
  align-self: flex-end;
  margin-left: auto;
  background: rgba(var(--accent-primary-rgb), 0.3);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius) 0 var(--border-radius) var(--border-radius);
  color: var(--text-primary);
  animation: slideIn var(--animation-speed) ease-out;
  max-width: 100%;
  /* Theme transitions are now handled in theme-transitions.css */
}

/* User message - Light theme */
.light-theme .user-message,
.light-theme .chat-message.user-message {
  background: rgba(var(--accent-primary-rgb), 0.08);
  color: var(--text-primary);
  border: 1px solid rgba(var(--accent-primary-rgb), 0.15);
}

/* Bot message */
.bot-message,
.chat-message.bot-message {
  align-self: flex-start;
  text-align:justify;
  margin-right: auto;
  color: var(--text-response);
  animation: fadeIn var(--animation-speed) ease-out;
  max-width: 100%;
  position: relative; /* For absolute positioning of footer */
  /* Theme transitions are now handled in theme-transitions.css */
  padding-bottom: var(--spacing-xl); /* Added padding to make room for the footer */
}

/* Special handling for bot messages with exams */
.chat-message.bot-message:has(.exam-container) {
  width: 100%;
  max-width: 100%;
  align-self: center;
}

/* Message metadata */
.message-metadata {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-dimmed);
}

.message-time {
  opacity: 0.7;
}

/* Message actions */
.message-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.message-action-button {
  background: transparent;
  border: none;
  color: var(--text-dimmed);
  font-size: var(--font-size-xs);
  padding: var(--spacing-xs);
  cursor: pointer;
  transition: var(--transition-theme);
  opacity: 0.7;
}

.message-action-button:hover {
  color: var(--accent-primary);
  opacity: 1;
}

/* Message footer with action buttons */
.message-footer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: calc(var(--spacing-xs) / 2);
  margin-top: calc(var(--spacing-xs) / 2);
  padding: 0;
  transition: var(--transition-theme);
  position: absolute;
  left: var(--spacing-sm);
  bottom: var(--spacing-sm);
  opacity: 1; /* Make footer always visible */
}

.message-footer button {
  background: transparent;
  border: none;
  color: var(--text-dimmed);
  font-size: calc(var(--font-size-xs) * 1.1);
  padding: calc(var(--spacing-xs) / 2);
  cursor: pointer;
  transition: var(--transition-theme);
  border-radius: var(--border-radius-sm);
  opacity: 0.4;
}

.message-footer button:hover {
  color: var(--accent-primary);
  background-color: rgba(var(--bg-primary-rgb), 0.1);
  opacity: 1;
}

/* Specific styles for action buttons */
.bookmark-button {
  color: var(--text-dimmed);
}

.bookmark-button:hover {
  color: var(--accent-primary);
}

.delete-button {
  color: var(--text-dimmed);
}

.delete-button:hover {
  color: var(--danger-color);
}

/* Think container styles moved to think-container.css */

/* Footer text for model disclaimer */
.footer-text {
  color: var(--text-dimmed);
  font-size: var(--font-size-xs);
  font-style: italic;
}
