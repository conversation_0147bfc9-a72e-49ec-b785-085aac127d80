{% load static %}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Regístrate en Agora - Tu espacio de trabajo de IA unificado">
    <title>Registro - Agora</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'chat/css/landing.css' %}">
</head>
<body>
    <!-- Header -->
    <header>
        <nav>
            <a href="{% url 'home' %}" class="logo">Agora</a>
            <div class="nav-cta">
                <a href="{% url 'login' %}" class="btn btn-ghost">¿Ya tienes cuenta?</a>
                <a href="{% url 'login' %}" class="btn btn-primary">Iniciar <PERSON></a>
            </div>
        </nav>
    </header>

    <!-- <PERSON><PERSON><PERSON> Principal -->
    <section class="auth-section">
        <div class="registration-layout">
            <!-- Main Registration Form -->
            <div class="auth-container">
                <h2>Crear Cuenta</h2>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="POST">
                    {% csrf_token %}

                    <!-- Username Field -->
                    <p>
                        <label for="{{ form.username.id_for_label }}">{{ form.username.label }}</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <ul class="errorlist">
                                {% for error in form.username.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </p>

                    <!-- Password Field -->
                    <p>
                        <label for="{{ form.password1.id_for_label }}">{{ form.password1.label }}</label>
                        {{ form.password1 }}
                        {% if form.password1.errors %}
                            <ul class="errorlist">
                                {% for error in form.password1.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </p>

                    <!-- Password Confirmation Field -->
                    <p>
                        <label for="{{ form.password2.id_for_label }}">{{ form.password2.label }}</label>
                        {{ form.password2 }}
                        {% if form.password2.errors %}
                            <ul class="errorlist">
                                {% for error in form.password2.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </p>

                    <button type="submit" class="auth-button">Registrarme</button>
                </form>
                <p class="auth-link">¿Ya tienes cuenta? <a href="{% url 'login' %}">Inicia sesión aquí</a></p>
            </div>

            <!-- Help Text Container -->
            <div class="help-text-container">
                <h3>Guía de Registro</h3>

                <div class="help-section">
                    <h4>Nombre de Usuario</h4>
                    <ul>
                        <li>Máximo 150 caracteres</li>
                        <li>Solo letras, números y los caracteres @/./+/-/_</li>
                        <li>Debe ser único en la plataforma</li>
                        <li>Se distingue entre mayúsculas y minúsculas</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>Contraseña</h4>
                    <div class="requirement">
                        <strong>Requisitos de seguridad:</strong>
                    </div>
                    <ul>
                        <li>Mínimo 8 caracteres de longitud</li>
                        <li>No puede ser muy similar a tu información personal</li>
                        <li>No puede ser una contraseña comúnmente utilizada</li>
                        <li>No puede ser completamente numérica</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>Confirmación de Contraseña</h4>
                    <ul>
                        <li>Debe coincidir exactamente con la contraseña anterior</li>
                        <li>Ayuda a prevenir errores de escritura</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>Consejos de Seguridad</h4>
                    <div class="requirement">
                        <strong>Recomendaciones:</strong>
                    </div>
                    <ul>
                        <li>Usa una combinación de letras, números y símbolos</li>
                        <li>Evita información personal como fechas de nacimiento</li>
                        <li>Considera usar una frase memorable</li>
                        <li>No reutilices contraseñas de otras cuentas</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <div class="logo" style="color: white; margin-bottom: 1rem;">Agora</div>
                <p style="color: #9ca3af; margin-bottom: 1rem;">Tu Espacio de Trabajo de IA Unificado</p>
                <p style="color: #6b7280; font-size: 0.9rem;">© 2025 Agora Inc. Todos los derechos reservados.</p>
            </div>
            <div class="footer-section">
                <h3>Producto</h3>
                <ul>
                    <li><a href="#features">Características</a></li>
                    <li><a href="#pricing">Precios</a></li>
                    <li><a href="#integrations">Integraciones</a></li>
                    <li><a href="#roadmap">Hoja de Ruta</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Recursos</h3>
                <ul>
                    <li><a href="#blog">Blog</a></li>
                    <li><a href="#documentation">Documentación</a></li>
                    <li><a href="#help">Centro de Ayuda</a></li>
                    <li><a href="#api">API</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Empresa</h3>
                <ul>
                    <li><a href="#about">Acerca de Nosotros</a></li>
                    <li><a href="#careers">Carreras</a></li>
                    <li><a href="#contact">Contacto</a></li>
                    <li><a href="#privacy">Política de Privacidad</a></li>
                    <li><a href="#terms">Términos de Servicio</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script>
        // Header remains transparent - no background change on scroll
    </script>
</body>
</html>
