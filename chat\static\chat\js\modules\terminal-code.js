/**
 * Módulo terminal-code.js - Manejo de bloques de código en formato terminal
 *
 * Este módulo implementa la nueva visualización de código en formato terminal
 * con numeración de líneas y botones flotantes.
 */

/**
 * Inicializa los bloques de código en formato terminal
 */
export function initializeTerminals() {
  // Buscar todos los bloques de código pre>code que no han sido convertidos a terminal
  document.querySelectorAll('pre > code:not(.terminal-processed)').forEach(codeBlock => {
    convertToTerminal(codeBlock);
  });

  // Configurar observador para detectar scroll y actualizar botones flotantes
  setupScrollObserver();
}

/**
 * Convierte un bloque de código en una terminal
 * @param {HTMLElement} codeBlock - Elemento de código a convertir
 */
function convertToTerminal(codeBlock) {
  if (!codeBlock || codeBlock.classList.contains('terminal-processed')) return;

  // Marcar como procesado
  codeBlock.classList.add('terminal-processed');

  // Obtener el elemento pre padre
  const preElement = codeBlock.parentElement;
  if (!preElement || preElement.tagName !== 'PRE') return;

  // Detectar lenguaje
  let language = 'text';
  let languageIcon = '';
  let fileExtension = 'txt';
  let isCommandTerminal = false;  // Flag para identificar terminales de comandos
  let isTextTerminal = false;     // Flag para identificar terminales de texto plano

  if (codeBlock.className) {
    const langMatch = codeBlock.className.match(/language-(\w+)/);
    if (langMatch && langMatch[1]) {
      language = langMatch[1] === 'plaintext' ? 'text' : langMatch[1];
      
      // Determine if this is a text terminal
      if (language === 'text' || language === 'txt' || language === 'plaintext') {
        isTextTerminal = true;
      }
      
      // Set appropriate file extension for download
      switch (language) {
        case 'javascript':
        case 'js':
          fileExtension = 'js';
          languageIcon = '<i class="fab fa-js"></i> ';
          break;
        case 'python':
        case 'py':
          fileExtension = 'py';
          languageIcon = '<i class="fab fa-python"></i> ';
          break;
        case 'html':
          fileExtension = 'html';
          languageIcon = '<i class="fab fa-html5"></i> ';
          break;
        case 'css':
          fileExtension = 'css';
          languageIcon = '<i class="fab fa-css3-alt"></i> ';
          break;
        case 'java':
          fileExtension = 'java';
          languageIcon = '<i class="fab fa-java"></i> ';
          break;
        case 'csharp':
        case 'cs':
          fileExtension = 'cs';
          languageIcon = '<i class="fab fa-microsoft"></i> ';
          break;
        case 'bash':
        case 'shell':
        case 'sh':
        case 'zsh':
        case 'terminal':
        case 'console':
        case 'cmd':
        case 'powershell':
        case 'ps':
          fileExtension = 'sh';
          languageIcon = '<i class="fas fa-terminal"></i> ';
          isCommandTerminal = true;
          break;
        case 'sql':
          fileExtension = 'sql';
          languageIcon = '<i class="fas fa-database"></i> ';
          break;
        case 'jsx':
          fileExtension = 'jsx';
          languageIcon = '<i class="fab fa-react"></i> ';
          break;
        case 'tsx':
          fileExtension = 'tsx';
          languageIcon = '<i class="fab fa-react"></i> ';
          break;
        case 'markdown':
        case 'md':
          fileExtension = 'md';
          languageIcon = '<i class="fab fa-markdown"></i> ';
          break;
        case 'typescript':
        case 'ts':
          fileExtension = 'ts';
          languageIcon = '<i class="fab fa-js"></i> ';
          break;
        case 'json':
          fileExtension = 'json';
          languageIcon = '<i class="fas fa-code"></i> ';
          break;
        case 'xml':
          fileExtension = 'xml';
          languageIcon = '<i class="fas fa-code"></i> ';
          break;
        case 'yaml':
        case 'yml':
          fileExtension = 'yml';
          languageIcon = '<i class="fas fa-file-code"></i> ';
          break;
        // For txt and other formats without specific icons, we'll leave the icon empty
        default:
          fileExtension = language;
          languageIcon = '';
      }
    }
  }

  // Obtener el contenido del código
  const codeContent = codeBlock.textContent || '';

  // Crear la terminal
  const terminal = document.createElement('div');
  terminal.className = 'code-terminal';

  // Add special class for bash/shell commands
  if (isCommandTerminal || language === 'bash' || language === 'shell') {
    terminal.classList.add('bash-terminal');
  }

  terminal.dataset.language = language;
  terminal.dataset.fileExtension = fileExtension;

  // Crear el header de la terminal
  const header = document.createElement('div');
  header.className = 'terminal-header';

  // Ya no agregamos los círculos de control decorativos

  // Agregar etiqueta de lenguaje
  const langLabel = document.createElement('div');
  langLabel.className = 'terminal-language';
  langLabel.innerHTML = `${languageIcon}${language}`;

  // Agregar elementos al header
  header.appendChild(langLabel);

  // Crear el cuerpo de la terminal
  const body = document.createElement('div');
  body.className = 'terminal-body';

  // Crear el contenedor con scroll
  const scrollContainer = document.createElement('div');
  scrollContainer.className = 'terminal-scroll';

  // Crear la tabla para el código con numeración de líneas
  const table = document.createElement('table');
  table.className = 'code-table';
  table.setAttribute('cellspacing', '0');
  table.setAttribute('cellpadding', '0');
  table.setAttribute('border', '0');

  // Dividir el código en líneas y eliminar espacios en blanco al final
  const lines = codeContent.split('\n').map(line => line.trimRight());

  // Crear filas para cada línea de código
  lines.forEach((line, index) => {
    const row = document.createElement('tr');

    // Determinar si debemos mostrar números de línea (no para bash/shell/txt)
    const isBashOrText = isCommandTerminal || isTextTerminal || language === 'bash' || language === 'shell' || language === 'text' || language === 'txt';

    // Celda para el número de línea (solo si no es bash/shell/txt)
    if (!isBashOrText) {
      const lineNumberCell = document.createElement('td');
      lineNumberCell.className = 'line-numbers';
      lineNumberCell.textContent = (index + 1).toString();
      row.appendChild(lineNumberCell);
    }

    // Celda para el código con resaltado de sintaxis
    const codeCell = document.createElement('td');
    codeCell.className = 'code-cell';

    // Crear un span para aplicar el resaltado de sintaxis
    const codeSpan = document.createElement('span');
    codeSpan.textContent = line;
    codeSpan.className = `language-${language}`;

    // Agregar el span a la celda
    codeCell.appendChild(codeSpan);

    // Agregar celda de código a la fila
    row.appendChild(codeCell);

    // Agregar fila a la tabla
    table.appendChild(row);
  });

  // Agregar tabla al contenedor con scroll
  scrollContainer.appendChild(table);

  // Agregar contenedor con scroll al cuerpo
  body.appendChild(scrollContainer);

  // Crear botones en el header
  const headerActions = document.createElement('div');
  headerActions.className = 'header-actions';

  // Botón de copiar para el header
  const headerCopyButton = document.createElement('button');
  headerCopyButton.className = 'terminal-action-button copy-button';
  headerCopyButton.textContent = 'Copy';
  headerCopyButton.title = 'Copy code';
  headerCopyButton.addEventListener('click', () => {
    navigator.clipboard.writeText(codeContent)
      .then(() => {
        headerCopyButton.textContent = 'Copied!';
        setTimeout(() => {
          headerCopyButton.textContent = 'Copy';
        }, 2000);
      })
      .catch(err => {
        console.error('Error al copiar código:', err);
        headerCopyButton.textContent = 'Error';
        setTimeout(() => {
          headerCopyButton.textContent = 'Copy';
        }, 2000);
      });
  });

  // Botón de descargar código - Solo para bloques que NO son de bash o texto
  const shouldShowDownloadButton = !isCommandTerminal && !isTextTerminal;
  
  let headerDownloadButton;
  if (shouldShowDownloadButton) {
    headerDownloadButton = document.createElement('button');
    headerDownloadButton.className = 'terminal-action-button download-button';
    headerDownloadButton.textContent = 'Download';
    headerDownloadButton.title = `Download code (${fileExtension})`;
    
    headerDownloadButton.addEventListener('click', () => {
      // Create a blob with the code content
      const blob = new Blob([codeContent], { type: 'text/plain' });
      
      // Create a temporary link and trigger download
      const downloadLink = document.createElement('a');
      downloadLink.href = URL.createObjectURL(blob);
      downloadLink.download = `codigo.${fileExtension}`;
      
      // Append to body, click and remove
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      
      // Clean up the URL object
      URL.revokeObjectURL(downloadLink.href);
      
      // Show visual feedback
      headerDownloadButton.textContent = 'Downloaded!';
      setTimeout(() => {
        headerDownloadButton.textContent = 'Download';
      }, 2000);
    });
  }

  // Botón de expandir/colapsar para el header (no para bash/shell/txt)
  const isBashOrText = isCommandTerminal || isTextTerminal || language === 'bash' || language === 'shell' || language === 'text' || language === 'txt';
  let headerToggleButton;

  if (!isBashOrText) {
    headerToggleButton = document.createElement('button');
    headerToggleButton.className = 'terminal-action-button toggle-button';
    headerToggleButton.textContent = 'Collapse';
    headerToggleButton.title = 'Collapse code block';
    headerToggleButton.addEventListener('click', () => {
      terminal.classList.toggle('collapsed');

      // Actualizar el texto del botón
      const newText = terminal.classList.contains('collapsed') ? 'Expand' : 'Collapse';
      headerToggleButton.textContent = newText;
      headerToggleButton.title = `${newText} code block`;
    });
  }

  // Agregar botones al header
  headerActions.appendChild(headerCopyButton);
  
  // Solo agregar el botón de descarga si corresponde
  if (shouldShowDownloadButton && headerDownloadButton) {
    headerActions.appendChild(headerDownloadButton);
  }
  
  if (!isBashOrText && headerToggleButton) {
    headerActions.appendChild(headerToggleButton);
  }
  header.appendChild(headerActions);

  // Floating buttons removed
  let floatingToggleButton = null;

  // Agregar elementos a la terminal
  terminal.appendChild(header);
  terminal.appendChild(body);

  // Colapsar automáticamente si el código es muy largo (no para bash/shell/txt)
  if (!isBashOrText && lines.length > 15) {
    terminal.classList.add('collapsed');

    // Actualizar el texto del botón para reflejar el estado colapsado
    if (headerToggleButton) {
      headerToggleButton.textContent = 'Expand';
      headerToggleButton.title = 'Expand code block';
    }
  } else if (!isBashOrText) {
    // Para códigos cortos, mostrar expandido con el texto de colapsar
    if (headerToggleButton) {
      headerToggleButton.textContent = 'Collapse';
      headerToggleButton.title = 'Collapse code block';
    }
  }

  // Reemplazar el elemento pre con la terminal
  preElement.parentNode.replaceChild(terminal, preElement);

  // Aplicar resaltado de sintaxis a los spans de código
  if (window.hljs) {
    terminal.querySelectorAll('.code-cell span').forEach(span => {
      if (span.className.includes('language-')) {
        hljs.highlightElement(span);
      }
    });
  }
}

/**
 * Configura un observador para detectar scroll y actualizar botones flotantes
 */
function setupScrollObserver() {
  // Floating buttons functionality removed
  // Now the copy button stays fixed in the header

  // Hide all floating actions
  document.querySelectorAll('.floating-actions').forEach(actions => {
    actions.style.display = 'none';
  });
}

/**
 * Configura un observador para detectar nuevos bloques de código
 */
export function setupTerminalObserver() {
  // Crear observador de mutaciones
  const observer = new MutationObserver(mutations => {
    let hasNewCode = false;

    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Buscar bloques de código en el nodo añadido
            const codeBlocks = node.querySelectorAll('pre > code:not(.terminal-processed)');
            if (codeBlocks.length > 0) {
              hasNewCode = true;
            }
          }
        });
      }
    });

    // Si se encontraron nuevos bloques de código, procesarlos
    if (hasNewCode) {
      initializeTerminals();
    }
  });

  // Observar cambios en el contenedor de mensajes
  const chatBoxContent = document.getElementById('chat-box-content');
  if (chatBoxContent) {
    observer.observe(chatBoxContent, {
      childList: true,
      subtree: true
    });
  }
}
