# config.py
import os
import logging
from dotenv import load_dotenv
from openai import OpenAI, AzureOpenAI # Import Azure if needed later

# Load environment variables from .env file
load_dotenv()

def get_env_var(var_name: str, default_value: str | None = None) -> str | None:
    """Gets an environment variable or returns a default."""
    value = os.environ.get(var_name)
    if value is None:
        if default_value is None:
            # Log a warning if a critical variable is missing and has no default
            # Depending on the variable, might want to raise an error instead
            logging.warning(f"Environment variable '{var_name}' not set and no default provided.")
        return default_value
    return value

# --- General Settings ---
DEBUG_MODE = get_env_var("DEBUG_MODE", "false").lower() == "true"

# --- API Keys ---
GROQ_API_KEY = get_env_var("GROQ_API_KEY")
XAI_API_KEY = get_env_var("XAI_API_KEY")

# --- LLM Call Parameters ---
DEFAULT_TEMPERATURE = float(get_env_var("DEFAULT_TEMPERATURE", "0.7"))
LLM_TIMEOUT_SECONDS = float(get_env_var("LLM_TIMEOUT_SECONDS", "120.0"))
LLM_MAX_ATTEMPTS = int(get_env_var("LLM_MAX_ATTEMPTS", "3"))

# --- Model Role Configuration ---
# Load provider and model for each role from .env
# Provides defaults matching the original script if not set in .env
ROLE_CONFIG = {
    "classifier": {
        "provider": get_env_var("CLASSIFIER_PROVIDER", "groq"),
        "model": get_env_var("CLASSIFIER_MODEL", "llama3-8b-8192"),
        "max_tokens": int(get_env_var("MAX_TOKENS_CLASSIFY", "150")),
    },
    "planner": {
        "provider": get_env_var("PLANNER_PROVIDER", "groq"),
        "model": get_env_var("PLANNER_MODEL", "qwen-qwq-32b"),
        "max_tokens": int(get_env_var("MAX_TOKENS_PLAN", "1500")),
    },
    "worker": {
        "provider": get_env_var("WORKER_PROVIDER", "grok"),
        "model": get_env_var("WORKER_MODEL", "grok-3-mini-beta"),
        "max_tokens": int(get_env_var("MAX_TOKENS_WORKER", "8000")),
    },
    "synthesizer": {
        "provider": get_env_var("SYNTHESIZER_PROVIDER", "grok"),
        "model": get_env_var("SYNTHESIZER_MODEL", "grok-3-beta"),
        "max_tokens": int(get_env_var("MAX_TOKENS_SYNTHESIZE", "12000")),
    },
    "simple_query": {
        "provider": get_env_var("SIMPLE_QUERY_PROVIDER", "groq"),
        "model": get_env_var("SIMPLE_QUERY_MODEL", "llama3-8b-8192"),
        "max_tokens": int(get_env_var("MAX_TOKENS_SIMPLE", "2000")),
    },
    "tool_identifier": { # Placeholder role
        "provider": get_env_var("TOOL_IDENTIFIER_PROVIDER", "groq"),
        "model": get_env_var("TOOL_IDENTIFIER_MODEL", "llama3-8b-8192"),
        "max_tokens": 100, # Example default
    },
}

# Determine which providers are actually needed based on config
PROVIDERS_USED = set(role['provider'] for role in ROLE_CONFIG.values())

# --- API Client Initialization ---
groq_client: OpenAI | None = None
grok_xai_client: OpenAI | None = None

if "groq" in PROVIDERS_USED:
    if GROQ_API_KEY:
        try:
            groq_client = OpenAI(
                api_key=GROQ_API_KEY,
                base_url="https://api.groq.com/openai/v1",
                timeout=LLM_TIMEOUT_SECONDS
            )
            logging.info("Groq client initialized successfully.")
        except Exception as e:
            logging.error(f"Error initializing Groq client: {e}", exc_info=DEBUG_MODE)
    else:
        logging.warning("GROQ_API_KEY not found. Groq models will be unavailable.")

if "grok" in PROVIDERS_USED:
    if XAI_API_KEY:
        try:
            grok_xai_client = OpenAI(
                api_key=XAI_API_KEY,
                base_url="https://api.x.ai/v1",
                timeout=LLM_TIMEOUT_SECONDS
            )
            logging.info("Grok (xAI) client initialized successfully.")
        except Exception as e:
            logging.error(f"Error initializing Grok (xAI) client: {e}", exc_info=DEBUG_MODE)
    else:
        logging.warning("XAI_API_KEY not found. Grok models will be unavailable.")

# Add other clients here if needed (e.g., AzureOpenAI)

# --- Workflow Parameters ---
MAX_CONCURRENT_WORKERS = int(get_env_var("MAX_CONCURRENT_WORKERS", "5"))

# --- Validation ---
def check_client_availability(provider: str) -> bool:
    """Checks if the client for a given provider is initialized."""
    if provider == "groq":
        return groq_client is not None
    elif provider == "grok":
        return grok_xai_client is not None
    # Add checks for other providers
    return False

# Check if required providers have initialized clients
required_providers_available = all(check_client_availability(p) for p in PROVIDERS_USED)

if not required_providers_available:
     logging.critical("One or more required LLM providers failed to initialize. Check API keys and logs. Exiting.")
     # Consider exiting here if critical, or let main.py handle it
     # sys.exit(1) # Uncomment to force exit

logging.info("Configuration loaded.")