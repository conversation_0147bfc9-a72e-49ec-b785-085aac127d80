/* test-feature.css - Styles for the test feature */

/* Test mode indicator */
.test-mode-indicator {
  background-color: rgba(var(--accent-primary-rgb), 0.1);
  color: var(--accent-primary);
  padding: 8px 12px;
  border-radius: var(--border-radius);
  margin: 8px 0;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(var(--accent-primary-rgb), 0.3);
}

.test-mode-indicator i {
  margin-right: 8px;
  font-size: 1rem;
}

/* Keyword highlight container */
.keyword-highlight-container {
  position: absolute;
  display: inline-block;
  background: transparent;
  color: var(--text-primary);
  z-index: 10;
  pointer-events: none;
  overflow: hidden;
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: inherit;
  font-family: inherit;
  font-size: inherit;
}

/* Highlighted keyword */
.test-keyword-highlight {
  background-color: rgba(var(--accent-primary-rgb), 0.2);
  border-radius: 4px;
  padding: 2px 4px;
  font-weight: 500;
  color: var(--accent-primary);
}

/* Test toggle button */
.test-toggle-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: var(--accent-primary);
  z-index: 20;
  pointer-events: auto;
  transition: transform 0.2s ease;
}

.test-toggle-btn:hover {
  transform: translateY(-50%) scale(1.2);
}

/* Active test mode styling for input */
.test-mode-active {
  border: 2px solid var(--accent-primary) !important;
  box-shadow: 0 0 0 2px rgba(var(--accent-primary-rgb), 0.2) !important;
}

/* Notification styles moved to notifications.css */

/* Test container enhancements for math */
.exam-container .MathJax {
  display: inline-block;
  margin: 0 2px;
}

.exam-container .math-block {
  display: block;
  margin: 10px 0;
  overflow-x: auto;
  text-align: center;
}

.exam-container .math-inline {
  display: inline;
}

/* Responsive styles */
@media (max-width: 768px) {
  .test-toggle-btn {
    right: 5px;
    font-size: 1rem;
  }
}
