document.addEventListener('DOMContentLoaded', function() {
    const profileContainer = document.querySelector('.profile-container');
    const profileMenu = document.getElementById('profile-menu');
    
    if (!profileContainer || !profileMenu) return;
    
    // Toggle profile menu on click
    profileContainer.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        profileMenu.classList.toggle('active');
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!profileMenu.contains(e.target) && !profileContainer.contains(e.target)) {
            profileMenu.classList.remove('active');
        }
    });
    
    // Prevent menu from closing when clicking inside it
    profileMenu.addEventListener('click', function(e) {
        e.stopPropagation();
    });
});
