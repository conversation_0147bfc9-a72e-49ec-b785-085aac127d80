/* chat-nav.css - Estilos para los botones de navegación del chat */

.chat-nav-buttons {
    position: fixed;
    bottom: var(--ui-element-spacing, 30px);
    right: var(--ui-element-spacing, 30px);
    display: none; /* Hidden by default, will be shown with JavaScript when there are messages */
    flex-direction: column;
    gap: 10px;
    z-index: 100;
    pointer-events: auto; /* Ensure it's interactive */
}

.chat-nav-buttons.visible {
    display: flex;
}

.chat-nav-button {
    width: var(--ui-element-height, 36px);
    height: var(--ui-element-height, 36px);
    background: rgba(var(--bg-quaternary-rgb), 0.4);
    color: var(--text-dimmed);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-theme);
    opacity: 0.9;
}

.light-theme .chat-nav-button {
    background: rgba(var(--bg-quaternary-rgb), 0.6);
    color: var(--text-dimmed);
    border: none;
}

.chat-nav-button:hover {
    color: var(--accent-primary);
    opacity: 1;
    transform: translateY(-2px);
}

.light-theme .chat-nav-button:hover {
    color: var(--accent-primary);
}

.chat-nav-button i {
    font-size: 1.2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .chat-nav-buttons {
        bottom: 20px;
        right: 20px;
    }

    .chat-nav-button {
        width: 32px;
        height: 32px;
    }

    .chat-nav-button i {
        font-size: 1rem;
    }
}
