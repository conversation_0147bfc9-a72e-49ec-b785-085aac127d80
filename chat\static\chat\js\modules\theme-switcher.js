/**
 * <PERSON><PERSON><PERSON><PERSON> theme-switcher.js - Manejo de temas claro y oscuro
 *
 * Este módulo implementa:
 * - Cambio entre tema claro y oscuro con transición simplificada
 * - Persistencia de la preferencia de tema
 * - Inicialización del tema basado en preferencias guardadas
 * - Integración con el preloader de temas para transiciones más suaves
 */

// Nombre de la clave para almacenar la preferencia de tema en localStorage
const THEME_STORAGE_KEY = 'preferred-theme';

// Temas disponibles
const THEMES = {
  LIGHT: 'light-theme',
  DARK: 'dark-theme'
};

// No transition duration needed

// No need to track transition state

// No preloader needed

/**
 * Inicializa el switcher de temas
 */
export function initializeThemeSwitcher() {
  // Configurar el botón de cambio de tema
  const themeToggleBtn = document.getElementById('theme-toggle-btn');
  if (themeToggleBtn) {
    themeToggleBtn.addEventListener('click', toggleTheme);
    updateThemeButtonIcon(getCurrentTheme());
  }

  // Cargar tema guardado o usar el tema por defecto (oscuro)
  loadSavedTheme();
}

/**
 * Alterna entre los temas claro y oscuro - sin transiciones
 */
function toggleTheme() {
  const currentTheme = getCurrentTheme();
  const newTheme = currentTheme === THEMES.LIGHT ? THEMES.DARK : THEMES.LIGHT;

  // Aplicar el nuevo tema directamente
  document.body.classList.remove(THEMES.LIGHT, THEMES.DARK);
  document.body.classList.add(newTheme);

  // Actualizar elementos principales
  updateMainThemeClasses(newTheme);

  // Guardar preferencia y actualizar icono
  saveThemePreference(newTheme);
  updateThemeButtonIcon(newTheme);
}

/**
 * Obtiene el tema actual
 * @returns {string} Nombre del tema actual
 */
function getCurrentTheme() {
  return document.body.classList.contains(THEMES.LIGHT) ? THEMES.LIGHT : THEMES.DARK;
}

// Removed transition function - no longer needed

/**
 * Actualiza las clases de tema solo en los elementos principales
 * @param {string} theme - Nombre del tema a aplicar
 */
function updateMainThemeClasses(theme) {
  // Usar un selector más limitado para reducir la carga
  const elementsToUpdate = document.querySelectorAll(
    '.message, .sidebar, .right-sidebar, .input-container, .notebook-content, .notebook-body, .notebook-sidebar'
  );

  // Actualizar solo los elementos principales
  elementsToUpdate.forEach(element => {
    element.classList.remove(THEMES.LIGHT, THEMES.DARK);
    if (theme === THEMES.LIGHT) {
      element.classList.add('light-theme');
    }
  });
}

/**
 * Guarda la preferencia de tema en localStorage
 * @param {string} theme - Nombre del tema a guardar
 */
function saveThemePreference(theme) {
  localStorage.setItem(THEME_STORAGE_KEY, theme);
}

/**
 * Carga el tema guardado o usa el tema por defecto
 */
function loadSavedTheme() {
  const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);

  // Si hay un tema guardado, aplicarlo sin transición
  if (savedTheme) {
    applyThemeWithoutTransition(savedTheme);
    updateThemeButtonIcon(savedTheme);
  } else {
    // Por defecto, usar tema oscuro
    updateThemeButtonIcon(THEMES.DARK);
  }
}

/**
 * Aplica un tema (usado para la carga inicial)
 * @param {string} theme - Nombre del tema a aplicar
 */
function applyThemeWithoutTransition(theme) {
  // Aplicar el tema
  document.body.classList.remove(THEMES.LIGHT, THEMES.DARK);
  document.body.classList.add(theme);

  // Actualizar los elementos principales
  updateMainThemeClasses(theme);
}

/**
 * Actualiza el icono del botón de tema según el tema actual
 * @param {string} theme - Tema actual
 */
function updateThemeButtonIcon(theme) {
  const themeToggleBtn = document.getElementById('theme-toggle-btn');
  if (!themeToggleBtn) return;

  // Actualizar el icono según el tema
  const iconElement = themeToggleBtn.querySelector('i');
  if (iconElement) {
    // Para Boxicons, necesitamos mantener la clase 'bx' y solo cambiar la clase del icono específico
    // Primero aseguramos que tenga la clase base 'bx'
    if (!iconElement.classList.contains('bx')) {
      iconElement.className = ''; // Limpiar todas las clases
      iconElement.classList.add('bx'); // Añadir la clase base de Boxicons
    } else {
      // Si ya tiene la clase 'bx', solo eliminamos las clases específicas de iconos
      iconElement.classList.remove('bx-sun', 'bx-moon');
    }

    // Agregar la clase de icono correspondiente al tema actual (Boxicons)
    if (theme === THEMES.LIGHT) {
      iconElement.classList.add('bx-moon'); // Mostrar luna en tema claro (para cambiar a oscuro)
    } else {
      iconElement.classList.add('bx-sun'); // Mostrar sol en tema oscuro (para cambiar a claro)
    }
  }
}
