/**
 * Module custom-drag-drop.js - Custom drag and drop functionality
 *
 * This module provides a custom drag and drop implementation that
 * gives full control over the cursor and drag image.
 */

class CustomDragDrop {
  constructor() {
    this.isDragging = false;
    this.dragElement = null;
    this.dragImage = null;
    this.dragSource = null;
    this.dropTargets = [];
    this.offsetX = 0;
    this.offsetY = 0;
    this.lastX = 0;
    this.lastY = 0;
    this.onDrop = null;
  }

  /**
   * Initialize the custom drag and drop functionality
   */
  initialize() {
    // Add global mouse event listeners
    document.addEventListener('mousemove', this.handleMouseMove.bind(this));
    document.addEventListener('mouseup', this.handleMouseUp.bind(this));
  }

  /**
   * Make an element draggable
   * @param {HTMLElement} element - The element to make draggable
   * @param {Function} onDragStart - Callback when drag starts
   * @param {Function} onDragEnd - Callback when drag ends
   */
  makeDraggable(element, onDragStart, onDragEnd) {
    element.classList.add('custom-draggable');

    // Add mousedown event listener to start drag
    element.addEventListener('mousedown', (e) => {
      // Only start drag on left mouse button
      if (e.button !== 0) return;

      // Don't start drag if clicking on a button or interactive element
      if (e.target.closest('button') ||
          e.target.closest('a') ||
          e.target.closest('.preview-actions') ||
          e.target.closest('.no-drag') ||
          e.target.closest('.interactive') ||
          e.target.tagName === 'BUTTON' ||
          e.target.tagName === 'A' ||
          e.target.tagName === 'INPUT' ||
          e.target.tagName === 'I') { // Font Awesome icons
        e.stopPropagation();
        return;
      }

      // Prevent text selection during drag
      e.preventDefault();

      // Get the position of the mouse relative to the element
      const rect = element.getBoundingClientRect();
      this.offsetX = e.clientX - rect.left;
      this.offsetY = e.clientY - rect.top;

      // Store the last mouse position
      this.lastX = e.clientX;
      this.lastY = e.clientY;

      // Start dragging
      this.startDrag(element, e.clientX, e.clientY, onDragStart, onDragEnd);
    });
  }

  /**
   * Register a drop target
   * @param {HTMLElement} element - The element to register as a drop target
   * @param {Function} onDragOver - Callback when drag is over the target
   * @param {Function} onDragLeave - Callback when drag leaves the target
   * @param {Function} onDrop - Callback when drop occurs on the target
   */
  registerDropTarget(element, onDragOver, onDragLeave, onDrop) {
    this.dropTargets.push({
      element,
      onDragOver,
      onDragLeave,
      onDrop,
      isOver: false
    });
  }

  /**
   * Start dragging an element
   * @param {HTMLElement} element - The element being dragged
   * @param {number} x - The x position of the mouse
   * @param {number} y - The y position of the mouse
   * @param {Function} onDragStart - Callback when drag starts
   * @param {Function} onDragEnd - Callback when drag ends
   */
  startDrag(element, x, y, onDragStart, onDragEnd) {
    // Set dragging state
    this.isDragging = true;
    this.dragSource = element;
    this.onDragEnd = onDragEnd;

    // Store the initial mouse position
    this.lastX = x;
    this.lastY = y;

    // Create drag image
    this.createDragImage(element);

    // Add dragging class to body
    document.body.classList.add('custom-dragging');

    // Call the onDragStart callback
    if (onDragStart) {
      onDragStart(element);
    }
  }

  /**
   * Create a drag image for the element
   * @param {HTMLElement} element - The element being dragged
   */
  createDragImage(element) {
    // Create a drag image element
    const dragImage = document.createElement('div');
    dragImage.className = 'custom-drag-image';

    // Check if we're in light theme
    const isLightTheme = document.documentElement.classList.contains('light-theme');

    // Set styles based on theme
    dragImage.style.position = 'fixed';
    dragImage.style.zIndex = '9999';
    dragImage.style.width = '90px';
    dragImage.style.height = '120px';
    dragImage.style.pointerEvents = 'none';
    dragImage.style.borderRadius = '4px';
    dragImage.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
    dragImage.style.overflow = 'hidden';
    dragImage.style.padding = '8px';
    dragImage.style.fontSize = '6px';
    dragImage.style.lineHeight = '1.2';
    dragImage.style.cursor = 'grabbing';
    dragImage.style.opacity = '0.85';
    dragImage.style.transition = 'none'; // Ensure no transition for smooth movement

    // Apply theme-aware styling using CSS custom properties
    dragImage.style.backgroundColor = 'var(--drag-image-bg)';
    dragImage.style.border = '1px solid var(--drag-image-border)';
    dragImage.style.color = 'var(--drag-image-text)';

    // Add theme class for proper CSS variable inheritance
    if (isLightTheme) {
      dragImage.classList.add('light-theme');
    } else {
      dragImage.classList.add('dark-theme');
    }

    // Get text content from the element
    let textContent = '';
    if (element.classList.contains('message-content')) {
      textContent = element.textContent || '';
    } else {
      const messageContent = element.querySelector('.message-content');
      if (messageContent) {
        textContent = messageContent.textContent || '';
      }
    }

    // Add a preview of the content
    dragImage.textContent = textContent.substring(0, 100) + (textContent.length > 100 ? '...' : '');

    // Add a small paper fold in the top-right corner
    const paperFold = document.createElement('div');
    paperFold.style.position = 'absolute';
    paperFold.style.top = '0';
    paperFold.style.right = '0';
    paperFold.style.width = '15px';
    paperFold.style.height = '15px';

    if (isLightTheme) {
      paperFold.style.background = 'linear-gradient(135deg, transparent 50%, #f0f0f0 50%)';
      paperFold.style.borderLeft = '1px solid rgba(0, 0, 0, 0.1)';
      paperFold.style.borderBottom = '1px solid rgba(0, 0, 0, 0.1)';
    } else {
      paperFold.style.background = 'linear-gradient(135deg, transparent 50%, #2d3748 50%)';
      paperFold.style.borderLeft = '1px solid rgba(255, 255, 255, 0.1)';
      paperFold.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
    }

    dragImage.appendChild(paperFold);

    // Position the drag image at the mouse position before adding to DOM
    const offsetX = 15; // Offset to the right of cursor
    const offsetY = 20; // Offset below the cursor
    dragImage.style.left = `${this.lastX + offsetX}px`;
    dragImage.style.top = `${this.lastY + offsetY}px`;

    // Add to DOM
    document.body.appendChild(dragImage);

    // Add a subtle scale animation when the drag image appears
    dragImage.style.transform = 'scale(0.9)';
    dragImage.style.opacity = '0.7';

    // Trigger animation after a tiny delay
    setTimeout(() => {
      dragImage.style.transition = 'transform 0.15s ease-out, opacity 0.15s ease-out';
      dragImage.style.transform = 'scale(1)';
      dragImage.style.opacity = '0.85';

      // Remove transition after animation completes to ensure smooth movement
      setTimeout(() => {
        dragImage.style.transition = 'none';
      }, 150);
    }, 10);

    // Store reference to drag image
    this.dragImage = dragImage;
  }

  /**
   * Update the position of the drag image
   * @param {number} x - The x position of the mouse
   * @param {number} y - The y position of the mouse
   */
  updateDragImagePosition(x, y) {
    if (!this.dragImage) return;

    // Use requestAnimationFrame for smoother updates
    requestAnimationFrame(() => {
      // Position the drag image slightly offset from the cursor
      // Using transform instead of left/top for better performance
      const offsetX = 15; // Offset to the right of cursor
      const offsetY = 20; // Offset below the cursor

      // Check if we're still in the initial animation phase
      if (this.dragImage.style.transition && this.dragImage.style.transition !== 'none') {
        // During animation, just update position without transform
        this.dragImage.style.left = `${x + offsetX}px`;
        this.dragImage.style.top = `${y + offsetY}px`;
      } else {
        // After animation, use transform for better performance
        this.dragImage.style.transform = `translate3d(${x + offsetX}px, ${y + offsetY}px, 0)`;
        this.dragImage.style.left = '0';
        this.dragImage.style.top = '0';
      }
    });
  }

  /**
   * Handle mouse move event
   * @param {MouseEvent} e - The mouse event
   */
  handleMouseMove(e) {
    if (!this.isDragging) return;

    // Prevent default to avoid any unwanted browser behaviors
    e.preventDefault();

    // Update the position of the drag image
    this.updateDragImagePosition(e.clientX, e.clientY);

    // Store the last mouse position
    this.lastX = e.clientX;
    this.lastY = e.clientY;

    // Check if we're over any drop targets
    this.checkDropTargets(e.clientX, e.clientY);
  }

  /**
   * Check if the mouse is over any drop targets
   * @param {number} x - The x position of the mouse
   * @param {number} y - The y position of the mouse
   */
  checkDropTargets(x, y) {
    this.dropTargets.forEach(target => {
      const rect = target.element.getBoundingClientRect();
      const isOver = (
        x >= rect.left &&
        x <= rect.right &&
        y >= rect.top &&
        y <= rect.bottom
      );

      // If we just moved over the target
      if (isOver && !target.isOver) {
        target.isOver = true;
        if (target.onDragOver) {
          target.onDragOver(this.dragSource);
        }
      }
      // If we just moved out of the target
      else if (!isOver && target.isOver) {
        target.isOver = false;
        if (target.onDragLeave) {
          target.onDragLeave(this.dragSource);
        }
      }
    });
  }

  /**
   * Handle mouse up event
   * @param {MouseEvent} e - The mouse event
   */
  handleMouseUp(e) {
    if (!this.isDragging) return;

    // Check if we're over any drop targets
    const droppedOn = this.dropTargets.find(target => target.isOver);

    // If we dropped on a target, call its onDrop callback
    if (droppedOn && droppedOn.onDrop) {
      console.log('Dropping source on target:', this.dragSource, droppedOn.element);
      droppedOn.onDrop(this.dragSource);
    }

    // Call the onDragEnd callback
    if (this.onDragEnd) {
      const dropTarget = droppedOn ? droppedOn.element : null;
      console.log('Calling onDragEnd with source and target:', this.dragSource, dropTarget);
      this.onDragEnd(this.dragSource, dropTarget);
    }

    // Clean up
    this.endDrag();
  }

  /**
   * End the drag operation and clean up
   */
  endDrag() {
    // Remove the drag image
    if (this.dragImage && this.dragImage.parentNode) {
      this.dragImage.parentNode.removeChild(this.dragImage);
    }

    // Reset drop targets
    this.dropTargets.forEach(target => {
      if (target.isOver && target.onDragLeave) {
        target.onDragLeave(this.dragSource);
      }
      target.isOver = false;
    });

    // Remove dragging class from body
    document.body.classList.remove('custom-dragging');

    // Reset state
    this.isDragging = false;
    this.dragImage = null;
    this.dragSource = null;
    this.onDragEnd = null;
  }
}

// Create and export instance
const customDragDrop = new CustomDragDrop();

/**
 * Initialize the custom drag and drop functionality
 */
export function initializeCustomDragDrop() {
  customDragDrop.initialize();
  return customDragDrop;
}
