from django.urls import path
from django.conf import settings
from django.conf.urls.static import static
from . import views
from . import views_pdf

urlpatterns = [
    # Authentication URLs
    path('register/', views.register_view, name='register'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('home/', views.home_view, name='home'),

    # Chat URLs
    path('', views.chat_view, name='chat'),
    path('api/', views.ChatAPIView.as_view(), name='chat_api'),
    path('api/multimodal/', views.MultimodalChatAPIView.as_view(), name='multimodal_chat_api'),
    path('api/segment/', views.SegmentChatAPIView.as_view(), name='segment_chat_api'),
    path('load_chat_messages/<uuid:chat_id>/', views.load_chat_messages, name='load_chat_messages'),
    path('delete_chat/<uuid:chat_id>/', views.delete_chat, name='delete_chat'),
    path('delete_all_chats/', views.delete_all_chats, name='delete_all_chats'),
    path('update_chat_title/<uuid:chat_id>/', views.update_chat_title, name='update_chat_title'),
    path('api/models/', views.get_available_models, name='get_available_models'),
    path('api/switch_model/', views.switch_model, name='switch_model'),
    path('delete_message/<int:message_id>/', views.delete_message, name='delete_message'),

    # PDF endpoints
    path('api/notebook/generate-pdf/', views_pdf.generate_notebook_pdf, name='generate_notebook_pdf'),
    path('api/notebook/pdf-status/<str:notebook_id>/', views_pdf.get_notebook_pdf_status, name='get_notebook_pdf_status'),
    path('api/notebook/download-pdf/<str:notebook_id>/', views_pdf.download_notebook_pdf, name='download_notebook_pdf'),
    path('api/notebook/preview-pdf/<str:notebook_id>/', views_pdf.preview_notebook_pdf, name='preview_notebook_pdf'),
    path('api/notebook/delete-pdf/<str:notebook_id>/', views_pdf.delete_notebook_pdf, name='delete_notebook_pdf'),

    # Test endpoints
    path('test/inline-code/', views.test_inline_code_view, name='test_inline_code'),
]

# Serve PDF files during development
if settings.DEBUG:
    urlpatterns += static('/pdf/', document_root=settings.MEDIA_ROOT + '/pdfs')