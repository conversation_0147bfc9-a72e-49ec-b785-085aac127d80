/* code.css - Styles for code blocks and syntax highlighting */

/* Terminal de código */
.code-terminal {
  position: relative;
  margin: var(--spacing-md) 0;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  background: var(--bg-primary);
  font-family: var(--font-family-code);
  transition: var(--transition-theme);
}

/* Terminal de código - Light theme */
.light-theme .code-terminal {
  box-shadow: var(--card-shadow);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

/* Header de la terminal */
.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-md);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  font-size: var(--font-size-xs);
  color: var(--text-primary);
  height: 36px;
  position: relative;
  z-index: 1;
  transition: var(--transition-theme);
}

/* Header de la terminal - Light theme */
.light-theme .terminal-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

/* Acciones en el header */
.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Ya no usamos círculos de control */

/* Etiqueta de lenguaje */
.terminal-language {
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-transform: lowercase;
  transition: var(--transition-theme);
}

/* Etiqueta de lenguaje - Light theme */
.light-theme .terminal-language {
  color: var(--text-primary);
}

.terminal-language i {
  font-size: var(--font-size-xs);
  color: var(--accent-primary);
}

/* Botones flotantes - Removed */
.floating-actions {
  display: none !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Botones de acción */
.terminal-action-button {
  background: transparent;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-sm);
  transition: var(--transition-theme);
  font-size: var(--font-size-xs);
}

/* Dark theme terminal action button hover effects */
.dark-theme .terminal-action-button:hover {
  color: var(--accent-primary);
  background-color: rgba(var(--accent-primary-rgb), 0.15);
  transform: translateY(-1px);
}



/* Download button special hover effect */
.terminal-action-button.download-button:hover {
  color: var(--success-color);
  background-color: rgba(var(--success-color-rgb), 0.1);
}

/* Botones de acción - Light theme */
.light-theme .terminal-action-button {
  color: var(--text-secondary);
}

.light-theme .terminal-action-button:hover {
  color: var(--accent-primary);
  background-color: rgba(var(--accent-primary-rgb), 0.1);
}

.terminal-action-button i {
  font-size: var(--font-size-xs);
}

/* Contenedor del código */
.terminal-body {
  position: relative;
  overflow: hidden;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  background-color: var(--bg-primary);
  border: none !important;
  transition: var(--transition-theme);
}

/* Contenedor del código - Light theme */
.light-theme .terminal-body {
  background-color: var(--bg-secondary);
}

/* Tabla de código con numeración de líneas */
.code-table {
  width: 100%;
  border: none !important;
  border-collapse: collapse;
  border-spacing: 0;
  font-family: var(--font-family-code);
  font-size: var(--font-size-sm);
  line-height: var(--line-height);
  background-color: transparent;
  transition: var(--transition-theme);
}

/* Tabla de código - Light theme */
.light-theme .code-table {
  background-color: transparent;
  color: var(--text-primary);
}

/* Numeración de líneas */
.line-numbers {
  user-select: none;
  text-align: right;
  color: var(--text-dimmed);
  background-color: transparent;
  padding: 0 var(--spacing-xs) 0 0;
  min-width: 30px;
  border: none;
  vertical-align: top;
  transition: var(--transition-theme);
}

/* Numeración de líneas - Light theme */
.light-theme .line-numbers {
  background-color: transparent;
  color: var(--text-dimmed);
}

/* Celda de código */
.code-cell {
  padding: 0 var(--spacing-sm);
  white-space: pre;
  overflow-x: visible;
  border: none;
  vertical-align: top;
  color: var(--text-primary);
  background-color: transparent !important;
  transition: var(--transition-theme);
}

/* Celda de código - Light theme */
.light-theme .code-cell {
  background-color: transparent !important;
  color: var(--text-primary);
}

/* Ensure code spans in light theme have transparent background */
.light-theme .code-cell span {
  background-color: transparent !important;
}

/* Filas y celdas de la tabla */
.code-table tr,
.code-table tr td,
.code-table tbody,
.code-table thead,
.code-table tfoot {
  border: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
  outline: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

.code-table td {
  border: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
  outline: none !important;
  box-shadow: none !important;
  padding-top: 0;
  padding-bottom: 0;
  background-color: transparent !important;
}

/* Sin efectos hover */

/* Contenedor con scroll */
.terminal-scroll {
  overflow-x: auto;
  max-height: 500px;
  border: none !important;
  background-color: var(--bg-primary);
  transition: var(--transition-theme);
}

/* Contenedor con scroll - Light theme */
.light-theme .terminal-scroll {
  background-color: var(--bg-secondary);
}

/* Ajuste para bash y text */
.code-terminal.bash-terminal .terminal-scroll,
.code-terminal[data-language="text"] .terminal-scroll,
.code-terminal[data-language="txt"] .terminal-scroll {
  max-height: none;
  height: auto;
}

/* Estado colapsado */
.code-terminal.collapsed .terminal-body {
  display: none;
}

/* Mantener visible solo el header cuando está colapsado */
.code-terminal.collapsed .terminal-header {
  border-radius: var(--border-radius);
  border-bottom: none;
}

/* Code text */
code, .code-cell span {
  font-family: var(--font-family-code) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height) !important;
}

/* Estilos para el resaltado de sintaxis dentro de la terminal */
.code-cell span {
  display: inline;
}

/* Syntax highlighting colors using theme variables */
.code-cell .hljs-keyword,
.code-cell .hljs-selector-tag,
.code-cell .hljs-literal,
.code-cell .hljs-section,
.code-cell .hljs-link {
  color: var(--syntax-keyword);
}

.code-cell .hljs-string {
  color: var(--syntax-string);
}

.code-cell .hljs-comment,
.code-cell .hljs-quote {
  color: var(--syntax-comment);
}

.code-cell .hljs-number {
  color: var(--syntax-number);
}

.code-cell .hljs-function,
.code-cell .hljs-title {
  color: var(--syntax-function);
}

.code-cell .hljs-variable,
.code-cell .hljs-name {
  color: var(--syntax-variable);
}

.code-cell .hljs-subst,
.code-cell .hljs-title,
.code-cell .hljs-name,
.code-cell .hljs-type,
.code-cell .hljs-attribute,
.code-cell .hljs-symbol,
.code-cell .hljs-bullet,
.code-cell .hljs-built_in,
.code-cell .hljs-addition,
.code-cell .hljs-variable,
.code-cell .hljs-template-tag,
.code-cell .hljs-template-variable {
  color: var(--syntax-variable);
}

.code-cell .hljs-comment,
.code-cell .hljs-quote,
.code-cell .hljs-deletion,
.code-cell .hljs-meta {
  color: var(--syntax-comment);
}

.code-cell .hljs-keyword,
.code-cell .hljs-selector-tag,
.code-cell .hljs-literal,
.code-cell .hljs-title,
.code-cell .hljs-section,
.code-cell .hljs-doctag,
.code-cell .hljs-type,
.code-cell .hljs-name,
.code-cell .hljs-strong {
  font-weight: bold;
}

.code-cell .hljs-number {
  color: var(--syntax-number);
}

.code-cell .hljs-emphasis {
  font-style: italic;
}

/* Inline code */
:not(pre) > code {
  background: var(--inline-code-bg);
  color: var(--inline-code-text);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.85em;
  font-family: var(--font-family-code);
  border: 1px solid var(--inline-code-border);
}

/* Bash/Shell Command Terminal */
.code-terminal.bash-terminal {
  width: fit-content;
  min-width: 300px;
  max-width: 100%;
  margin-left: 0;
  margin-right: auto;
}

.code-terminal.bash-terminal .terminal-body {
  background-color: var(--terminal-bg-primary);
}

.code-terminal.bash-terminal .terminal-header {
  background-color: var(--terminal-header-bg);
  color: var(--terminal-text);
  border-bottom: 1px solid var(--terminal-border);
}

.code-terminal.bash-terminal .code-table {
  background-color: var(--terminal-bg-primary);
  width: 100%;
  margin: 0;
}

.code-terminal.bash-terminal .line-numbers {
  display: none;
}

.code-terminal.bash-terminal .code-cell {
  padding-left: 16px;
  padding-right: 16px;
  white-space: pre;
  color: var(--terminal-text);
  background-color: transparent !important;
}

.code-terminal.bash-terminal .toggle-button {
  display: none;
}

/* Text format terminal */
.code-terminal[data-language="text"],
.code-terminal[data-language="txt"] {
  width: fit-content;
  min-width: 300px;
  max-width: 100%;
  margin-left: 0;
  margin-right: auto;
}

/* Text format terminal styling */
.code-terminal[data-language="text"] .terminal-body,
.code-terminal[data-language="txt"] .terminal-body {
  background-color: var(--terminal-bg-primary);
}

.code-terminal[data-language="text"] .terminal-header,
.code-terminal[data-language="txt"] .terminal-header {
  background-color: var(--terminal-header-bg);
  color: var(--terminal-text);
  border-bottom: 1px solid var(--terminal-border);
}

.code-terminal[data-language="text"] .code-table,
.code-terminal[data-language="txt"] .code-table {
  background-color: var(--terminal-bg-primary);
}

.code-terminal[data-language="text"] .code-cell,
.code-terminal[data-language="txt"] .code-cell {
  color: var(--terminal-text);
  background-color: transparent !important;
}

.code-terminal[data-language="text"] .line-numbers,
.code-terminal[data-language="txt"] .line-numbers {
  display: none;
}

.code-terminal[data-language="text"] .toggle-button,
.code-terminal[data-language="txt"] .toggle-button {
  display: none;
}

/* Syntax highlighting theme */
.hljs {
  color: var(--syntax-text);
  background: var(--syntax-background);
  transition: var(--transition-theme);
}

.hljs-comment,
.hljs-quote {
  color: var(--syntax-comment);
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag {
  color: var(--syntax-keyword);
}

.hljs-literal,
.hljs-number,
.hljs-boolean {
  color: var(--syntax-number);
}

.hljs-string,
.hljs-doctag {
  color: var(--syntax-string);
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: var(--syntax-function);
}

.hljs-subst {
  color: var(--syntax-text);
}

.hljs-tag,
.hljs-name {
  color: var(--syntax-keyword);
}

.hljs-attr {
  color: var(--syntax-variable);
}

.hljs-symbol,
.hljs-bullet {
  color: var(--syntax-variable);
}

/* Custom syntax highlighting classes for theme consistency */
.syntax-keyword {
  color: var(--syntax-keyword);
}

.syntax-string {
  color: var(--syntax-string);
}

.syntax-comment {
  color: var(--syntax-comment);
}

.syntax-number {
  color: var(--syntax-number);
}

.syntax-function {
  color: var(--syntax-function);
}

.syntax-variable {
  color: var(--syntax-variable);
}

.syntax-regex {
  color: var(--syntax-regex);
}

.syntax-constant {
  color: var(--syntax-constant);
}

.syntax-type {
  color: var(--syntax-type);
}

.syntax-decorator {
  color: var(--syntax-decorator);
}

.syntax-operator {
  color: var(--syntax-operator);
}

.syntax-bold {
  font-weight: bold;
}

/* Ensure all syntax highlighting elements inherit theme transitions */
.syntax-keyword,
.syntax-string,
.syntax-comment,
.syntax-number,
.syntax-function,
.syntax-variable,
.syntax-regex,
.syntax-constant,
.syntax-type,
.syntax-decorator,
.syntax-operator {
  transition: var(--transition-theme);
}

.hljs-built_in,
.hljs-builtin-name {
  color: var(--syntax-variable);
}

.hljs-template-tag,
.hljs-template-variable {
  color: var(--syntax-function);
}

.hljs-addition {
  background-color: rgba(var(--success-color-rgb), 0.2);
  display: inline-block;
  width: 100%;
}

.hljs-deletion {
  background-color: rgba(var(--danger-color-rgb), 0.2);
  display: inline-block;
  width: 100%;
}

/* Terminal-specific colors for light theme */
.light-theme .code-terminal.bash-terminal .terminal-body {
  background-color: var(--terminal-bg-primary);
}

.light-theme .code-terminal.bash-terminal .code-cell {
  color: var(--terminal-text);
  background-color: transparent !important;
}

/* Fix text color in light theme terminals - Use terminal-specific colors */
.light-theme .terminal-body {
  color: var(--terminal-text);
}

/* Ensure code cells in light theme use terminal text color */
.light-theme .code-cell {
  color: var(--terminal-text) !important;
  background-color: transparent !important;
}

/* Remove background from code spans in light theme */
.light-theme .code-cell span {
  background-color: transparent !important;
}

/* Ensure text is visible in bash terminals for light theme */
.light-theme .code-terminal.bash-terminal .code-cell,
.light-theme .code-terminal[data-language="text"] .code-cell,
.light-theme .code-terminal[data-language="txt"] .code-cell {
  color: var(--terminal-text) !important;
}

/* Strengthen syntax highlighting to override external libraries with maximum specificity */
.code-terminal .hljs-keyword,
.code-terminal .hljs-selector-tag,
.code-terminal .hljs-literal,
.code-terminal .hljs-section,
.code-terminal .hljs-link,
.code-terminal .code-cell .hljs-keyword,
.code-terminal .code-cell .hljs-selector-tag,
.code-terminal .code-cell .hljs-literal,
.code-terminal .code-cell .hljs-section,
.code-terminal .code-cell .hljs-link {
  color: var(--syntax-keyword) !important;
  background-color: transparent !important;
}

.code-terminal .hljs-string,
.code-terminal .hljs-doctag,
.code-terminal .code-cell .hljs-string,
.code-terminal .code-cell .hljs-doctag {
  color: var(--syntax-string) !important;
  background-color: transparent !important;
}

.code-terminal .hljs-comment,
.code-terminal .hljs-quote,
.code-terminal .code-cell .hljs-comment,
.code-terminal .code-cell .hljs-quote {
  color: var(--syntax-comment) !important;
  background-color: transparent !important;
}

.code-terminal .hljs-number,
.code-terminal .hljs-literal,
.code-terminal .hljs-boolean,
.code-terminal .code-cell .hljs-number,
.code-terminal .code-cell .hljs-literal,
.code-terminal .code-cell .hljs-boolean {
  color: var(--syntax-number) !important;
  background-color: transparent !important;
}

.code-terminal .hljs-function,
.code-terminal .hljs-title,
.code-terminal .hljs-section,
.code-terminal .hljs-selector-id,
.code-terminal .code-cell .hljs-function,
.code-terminal .code-cell .hljs-title,
.code-terminal .code-cell .hljs-section,
.code-terminal .code-cell .hljs-selector-id {
  color: var(--syntax-function) !important;
  background-color: transparent !important;
}

.code-terminal .hljs-variable,
.code-terminal .hljs-name,
.code-terminal .hljs-attr,
.code-terminal .hljs-built_in,
.code-terminal .hljs-builtin-name,
.code-terminal .code-cell .hljs-variable,
.code-terminal .code-cell .hljs-name,
.code-terminal .code-cell .hljs-attr,
.code-terminal .code-cell .hljs-built_in,
.code-terminal .code-cell .hljs-builtin-name {
  color: var(--syntax-variable) !important;
  background-color: transparent !important;
}

/* Ensure no individual line backgrounds from external libraries */
.code-terminal .hljs,
.code-terminal .hljs *,
.code-terminal pre,
.code-terminal pre *,
.code-terminal code,
.code-terminal code * {
  background-color: transparent !important;
}

/* Ensure unified terminal background is maintained */
.code-terminal .terminal-body,
.code-terminal .terminal-scroll {
  background-color: var(--bg-primary) !important;
}

.light-theme .code-terminal .terminal-body,
.light-theme .code-terminal .terminal-scroll {
  background-color: var(--bg-secondary) !important;
}

/* Override global table styling for code terminals */
.code-terminal table,
.code-terminal table th,
.code-terminal table td,
.code-terminal table tr {
  border: none !important;
  border-bottom: none !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
  background-color: transparent !important;
  background: transparent !important;
  margin: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* Comprehensive background removal for all terminal elements */
.code-terminal,
.code-terminal *,
.code-terminal .hljs,
.code-terminal .hljs *,
.code-terminal pre,
.code-terminal pre *,
.code-terminal code,
.code-terminal code *,
.code-terminal table,
.code-terminal table *,
.code-terminal tr,
.code-terminal tr *,
.code-terminal td,
.code-terminal td *,
.code-terminal th,
.code-terminal th *,
.code-terminal span,
.code-terminal span * {
  background-color: transparent !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Restore only the main terminal container backgrounds */
.code-terminal {
  background: var(--bg-primary) !important;
}

.light-theme .code-terminal {
  background: var(--bg-secondary) !important;
}

.code-terminal .terminal-body {
  background-color: var(--bg-primary) !important;
}

.light-theme .code-terminal .terminal-body {
  background-color: var(--bg-secondary) !important;
}

.code-terminal .terminal-header {
  background: var(--bg-secondary) !important;
}

.light-theme .code-terminal .terminal-header {
  background: var(--bg-secondary) !important;
}

/* Strengthen light theme terminal text colors with highest specificity */
.light-theme .code-terminal .code-cell,
.light-theme .code-terminal .code-cell *,
.light-theme .code-terminal .hljs,
.light-theme .code-terminal .hljs *,
.light-theme .code-terminal pre,
.light-theme .code-terminal pre *,
.light-theme .code-terminal code,
.light-theme .code-terminal code * {
  color: var(--terminal-text) !important;
}

/* Override external library backgrounds with maximum specificity */
.code-terminal .hljs-addition,
.code-terminal .hljs-deletion {
  background-color: transparent !important;
  background: transparent !important;
}

/* Ensure terminal-specific backgrounds for special terminal types */
.code-terminal.bash-terminal .terminal-body,
.code-terminal[data-language="text"] .terminal-body,
.code-terminal[data-language="txt"] .terminal-body {
  background-color: var(--terminal-bg-primary) !important;
}

.light-theme .code-terminal.bash-terminal .terminal-body,
.light-theme .code-terminal[data-language="text"] .terminal-body,
.light-theme .code-terminal[data-language="txt"] .terminal-body {
  background-color: var(--terminal-bg-primary) !important;
}

/* Override formatted.css message-content styling for terminals */
.message-content .code-terminal,
.message-content .code-terminal *,
.message-content .code-terminal pre,
.message-content .code-terminal pre *,
.message-content .code-terminal code,
.message-content .code-terminal code *,
.message-content .code-terminal table,
.message-content .code-terminal table *,
.message-content .code-terminal tr,
.message-content .code-terminal tr *,
.message-content .code-terminal td,
.message-content .code-terminal td *,
.message-content .code-terminal th,
.message-content .code-terminal th * {
  background-color: transparent !important;
  background: transparent !important;
  border: none !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  margin: 0 !important;
}

/* Restore terminal container backgrounds even within message content */
.message-content .code-terminal {
  background: var(--bg-primary) !important;
  margin: var(--spacing-md) 0 !important;
}

.message-content .light-theme .code-terminal,
.light-theme .message-content .code-terminal {
  background: var(--bg-secondary) !important;
}

.message-content .code-terminal .terminal-body {
  background-color: var(--bg-primary) !important;
}

.message-content .light-theme .code-terminal .terminal-body,
.light-theme .message-content .code-terminal .terminal-body {
  background-color: var(--bg-secondary) !important;
}

.message-content .code-terminal .terminal-header {
  background: var(--bg-secondary) !important;
}

.message-content .light-theme .code-terminal .terminal-header,
.light-theme .message-content .code-terminal .terminal-header {
  background: var(--bg-secondary) !important;
}

/* Ensure terminal text colors are maintained in message content */
.message-content .code-terminal .code-cell,
.message-content .code-terminal .code-cell *,
.message-content .code-terminal pre,
.message-content .code-terminal pre *,
.message-content .code-terminal code,
.message-content .code-terminal code * {
  color: var(--terminal-text) !important;
}

.message-content .light-theme .code-terminal .code-cell,
.message-content .light-theme .code-terminal .code-cell *,
.message-content .light-theme .code-terminal pre,
.message-content .light-theme .code-terminal pre *,
.message-content .light-theme .code-terminal code,
.message-content .light-theme .code-terminal code *,
.light-theme .message-content .code-terminal .code-cell,
.light-theme .message-content .code-terminal .code-cell *,
.light-theme .message-content .code-terminal pre,
.light-theme .message-content .code-terminal pre *,
.light-theme .message-content .code-terminal code,
.light-theme .message-content .code-terminal code * {
  color: var(--terminal-text) !important;
}
