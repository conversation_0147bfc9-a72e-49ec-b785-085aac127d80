from django.db import models
from django.contrib.auth.models import User
import uuid

class Chat(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chats')
    chat_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    title = models.CharField(max_length=255, blank=True, null=True, default="New Chat")
    created_at = models.DateTimeField(auto_now_add=True)

class Message(models.Model):
    chat = models.ForeignKey(Chat, on_delete=models.CASCADE, related_name='messages')
    sender = models.CharField(max_length=10, choices=[('user', 'User'), ('bot', 'Bot')])
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

class Memory(models.Model):
    chat = models.ForeignKey(Chat, on_delete=models.CASCADE, related_name="memories")
    memory_data = models.TextField()  # Aquí puedes almacenar la memoria en formato JSON o cualquier otro formato que elijas
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Memory for chat {self.chat.chat_id} at {self.created_at}"

class SegmentChat(models.Model):
    """Model for segment-specific chat conversations"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='segment_chats')
    segment_chat_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    parent_message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='segment_chats')
    selected_text = models.TextField()  # The text segment that was selected
    title = models.CharField(max_length=255, blank=True, null=True, default="Segment Discussion")
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"Segment chat {self.segment_chat_id} for user {self.user.username}"

class SegmentMessage(models.Model):
    """Model for messages within segment-specific chats"""
    segment_chat = models.ForeignKey(SegmentChat, on_delete=models.CASCADE, related_name='messages')
    sender = models.CharField(max_length=10, choices=[('user', 'User'), ('bot', 'Bot')])
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Segment message from {self.sender} in {self.segment_chat.segment_chat_id}"

class SegmentMemory(models.Model):
    """Model for storing memory data for segment-specific chats"""
    segment_chat = models.ForeignKey(SegmentChat, on_delete=models.CASCADE, related_name="memories")
    memory_data = models.TextField()  # Serialized LangChain memory data
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Segment memory for chat {self.segment_chat.segment_chat_id} at {self.created_at}"
