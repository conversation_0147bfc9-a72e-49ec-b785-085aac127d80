/**
 * <PERSON><PERSON><PERSON><PERSON> code-handler.js - Manejo de bloques de código
 *
 * Este módulo contiene funciones para el manejo y resaltado de bloques de código.
 */

import { escapeHtml } from './utils.js';

/**
 * Configura el resaltado de sintaxis para bloques de código.
 */
export function setupCodeHighlighting() {
  if (!window.hljs) {
    console.warn('highlight.js no está disponible');
    return;
  }

  // Configurar highlight.js
  hljs.configure({
    languages: ['javascript', 'python', 'java', 'csharp', 'cpp', 'css', 'html', 'xml', 'json', 'bash', 'sql', 'typescript'],
    ignoreUnescapedHTML: true
  });

  // Resaltar bloques de código existentes
  document.querySelectorAll('pre code:not(.hljs)').forEach(block => {
    // Detectar lenguaje si no está especificado
    if (!block.className.includes('language-')) {
      detectLanguage(block);
    }

    // Aplicar resaltado
    hljs.highlightElement(block);

    // Agregar botón de copiar
    addCopyButton(block);
  });
}

/**
 * Detecta el lenguaje de un bloque de código.
 * @param {HTMLElement} codeElement - Elemento de código.
 */
function detectLanguage(codeElement) {
  if (!codeElement) return;

  const content = codeElement.textContent || '';

  // Patrones para detectar lenguajes comunes
  const patterns = [
    { lang: 'python', regex: /^\s*(import|from|def|class|if __name__|print)\s/m },
    { lang: 'javascript', regex: /\b(const|let|var|function|=>|document\.|window\.|console\.log)\b/ },
    { lang: 'html', regex: /^\s*<!DOCTYPE html>|<html|<body|<div|<p>|<span>|<h1>/i },
    { lang: 'css', regex: /\s*[\.\#]?[\w-]+\s*\{[\s\S]*?\}/ },
    { lang: 'json', regex: /^\s*\{[\s\S]*\}\s*$|^\s*\[[\s\S]*\]\s*$/ },
    { lang: 'bash', regex: /^\s*(\#\!|\/bin\/|cd |ls |mkdir|rm |grep |curl |wget |sudo )/ },
    { lang: 'sql', regex: /^\s*(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP|FROM|WHERE|GROUP BY)/i }
  ];

  // Verificar patrones
  for (const pattern of patterns) {
    if (pattern.regex.test(content)) {
      codeElement.className = `language-${pattern.lang}`;
      return;
    }
  }

  // Si no se detecta ningún lenguaje, usar texto plano
  codeElement.className = 'language-plaintext';
}

/**
 * Agrega un botón de copiar a un bloque de código.
 * @param {HTMLElement} codeBlock - Bloque de código.
 */
function addCopyButton(codeBlock) {
  // Verificar si el bloque ya tiene un botón de copiar
  const parentPre = codeBlock.parentNode;
  if (parentPre.querySelector('.copy-code-button')) return;

  // Crear botón
  const copyButton = document.createElement('button');
  copyButton.className = 'copy-code-button';
  copyButton.innerHTML = '<i class="fas fa-copy"></i>';
  copyButton.title = 'Copiar código';

  // Agregar evento de clic
  copyButton.addEventListener('click', () => {
    const code = codeBlock.textContent;

    navigator.clipboard.writeText(code)
      .then(() => {
        // Cambiar ícono temporalmente para indicar éxito
        copyButton.innerHTML = '<i class="fas fa-check"></i>';
        setTimeout(() => {
          copyButton.innerHTML = '<i class="fas fa-copy"></i>';
        }, 2000);
      })
      .catch(err => {
        console.error('Error al copiar código:', err);
        copyButton.innerHTML = '<i class="fas fa-times"></i>';
        setTimeout(() => {
          copyButton.innerHTML = '<i class="fas fa-copy"></i>';
        }, 2000);
      });
  });

  // Agregar botón al contenedor
  parentPre.classList.add('code-block-container');

  // Crear header para el bloque de código
  const codeHeader = document.createElement('div');
  codeHeader.className = 'code-header';

  // Detectar lenguaje
  let language = 'texto';
  if (codeBlock.className) {
    const langMatch = codeBlock.className.match(/language-(\w+)/);
    if (langMatch && langMatch[1]) {
      language = langMatch[1] === 'plaintext' ? 'texto' : langMatch[1];
    }
  }

  // Agregar etiqueta de lenguaje
  const langLabel = document.createElement('span');
  langLabel.className = 'code-language';
  langLabel.textContent = language;
  codeHeader.appendChild(langLabel);

  // Agregar botón de copiar
  codeHeader.appendChild(copyButton);

  // Insertar header antes del bloque de código
  parentPre.insertBefore(codeHeader, codeBlock);
}

/**
 * Escapa HTML en bloques de código.
 */
export function escapeHtmlInCodeBlocks() {
  document.querySelectorAll('pre code').forEach(block => {
    // Verificar si el bloque ya ha sido procesado
    if (block.dataset.processed) return;

    const content = block.textContent || '';

    // Verificar si el contenido tiene HTML sin escapar
    if (/<[a-z][\s\S]*>/i.test(content)) {
      // Escapar HTML
      block.textContent = content;
    }

    // Marcar como procesado
    block.dataset.processed = 'true';
  });
}

/**
 * Configura un observador para procesar nuevos bloques de código.
 */
export function setupCodeObserver() {
  // Crear observador de mutaciones
  const observer = new MutationObserver(mutations => {
    let hasNewCode = false;

    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Buscar bloques de código en el nodo añadido
            const codeBlocks = node.querySelectorAll('pre code:not([data-processed="true"])');
            if (codeBlocks.length > 0) {
              hasNewCode = true;
            }
          }
        });
      }
    });

    // Si se encontraron nuevos bloques de código, procesarlos
    if (hasNewCode) {
      escapeHtmlInCodeBlocks();
      setupCodeHighlighting();
    }
  });

  // Observar cambios en el contenedor de mensajes
  const chatBoxContent = document.getElementById('chat-box-content');
  if (chatBoxContent) {
    observer.observe(chatBoxContent, {
      childList: true,
      subtree: true
    });
  }
}
