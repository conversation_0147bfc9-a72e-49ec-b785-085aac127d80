/**
 * <PERSON>ó<PERSON><PERSON> file-upload.js - Manejo de carga de archivos para modelos multimodales
 *
 * Este módulo implementa:
 * - Carga de imágenes para modelos multimodales
 * - Previsualización de imágenes
 * - Envío de imágenes junto con texto al LLM
 */

// Importar utilidades
import { getCookie } from './utils.js';

class FileUploadHandler {
    constructor() {
        this.uploadedFiles = [];
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        this.isInitialized = false;
    }

    /**
     * Inicializa el manejador de carga de archivos
     */
    initialize() {
        // Crear botón de carga si no existe
        this.createUploadButtonIfNeeded();

        // Crear contenedor de previsualización si no existe
        this.createPreviewContainerIfNeeded();

        this.isInitialized = true;
    }

    /**
     * Crea el botón de carga de archivos si no existe
     */
    createUploadButtonIfNeeded() {
        // Verificar si ya existe el botón
        if (document.getElementById('file-upload-btn')) {
            return;
        }

        // Obtener el contenedor de entrada
        const inputContainer = document.querySelector('.input-container');
        if (!inputContainer) return;

        // Crear el botón de carga
        const uploadBtn = document.createElement('button');
        uploadBtn.id = 'file-upload-btn';
        uploadBtn.className = 'file-upload-btn tooltip';
        uploadBtn.setAttribute('data-tooltip', 'Adjuntar imagen');
        uploadBtn.innerHTML = '<i class="bx bx-image-add"></i>';

        // Crear input de archivo oculto
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.id = 'file-input';
        fileInput.className = 'hidden-file-input';
        fileInput.accept = this.allowedTypes.join(',');
        fileInput.style.display = 'none';

        // Agregar al DOM
        inputContainer.insertBefore(uploadBtn, inputContainer.querySelector('.send-btn'));
        inputContainer.appendChild(fileInput);

        // Configurar eventos
        uploadBtn.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files);
        });
    }

    /**
     * Crea el contenedor de previsualización si no existe
     */
    createPreviewContainerIfNeeded() {
        // Verificar si ya existe el contenedor
        if (document.getElementById('file-preview-container')) {
            return;
        }

        // Obtener el contenedor de entrada
        const inputArea = document.querySelector('.input-area');
        if (!inputArea) return;

        // Crear el contenedor de previsualización
        const previewContainer = document.createElement('div');
        previewContainer.id = 'file-preview-container';
        previewContainer.className = 'file-preview-container';

        // Agregar al DOM antes del contenedor de entrada
        inputArea.insertBefore(previewContainer, inputArea.firstChild);
    }

    /**
     * Maneja la selección de archivos
     * @param {FileList} files - Archivos seleccionados
     */
    handleFileSelection(files) {
        if (!files || files.length === 0) return;

        // Limpiar archivos anteriores
        this.clearFiles();

        // Procesar el archivo (por ahora solo tomamos el primero)
        const file = files[0];

        // Validar tipo y tamaño
        if (!this.validateFile(file)) return;

        // Guardar archivo
        this.uploadedFiles.push(file);

        // Mostrar previsualización
        this.showPreview(file);
    }

    /**
     * Valida un archivo
     * @param {File} file - Archivo a validar
     * @returns {boolean} - Indica si el archivo es válido
     */
    validateFile(file) {
        // Validar tipo
        if (!this.allowedTypes.includes(file.type)) {
            this.showError('Tipo de archivo no permitido. Solo se permiten imágenes (JPEG, PNG, GIF, WEBP).');
            return false;
        }

        // Validar tamaño
        if (file.size > this.maxFileSize) {
            this.showError(`El archivo es demasiado grande. El tamaño máximo es ${this.maxFileSize / (1024 * 1024)}MB.`);
            return false;
        }

        return true;
    }

    /**
     * Muestra un mensaje de error
     * @param {string} message - Mensaje de error
     */
    showError(message) {
        // Verificar si existe el contenedor de notificaciones
        let notificationContainer = document.getElementById('notification-container');

        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'notification-container';
            document.body.appendChild(notificationContainer);
        }

        // Crear notificación
        const notification = document.createElement('div');
        notification.className = 'notification error';
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Agregar al contenedor
        notificationContainer.appendChild(notification);

        // Configurar cierre
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.classList.add('closing');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });

        // Auto-cerrar después de 5 segundos
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.add('closing');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 5000);
    }

    /**
     * Muestra la previsualización de un archivo
     * @param {File} file - Archivo a previsualizar
     */
    showPreview(file) {
        const previewContainer = document.getElementById('file-preview-container');
        if (!previewContainer) return;

        // Limpiar contenedor
        previewContainer.innerHTML = '';

        // Crear elemento de previsualización
        const previewItem = document.createElement('div');
        previewItem.className = 'file-preview-item';

        // Crear imagen de previsualización
        const img = document.createElement('img');
        img.className = 'file-preview-img';
        img.src = URL.createObjectURL(file);
        img.alt = file.name;

        // Crear botón de eliminar
        const removeBtn = document.createElement('button');
        removeBtn.className = 'file-preview-remove';
        removeBtn.innerHTML = '&times;';
        removeBtn.addEventListener('click', () => this.clearFiles());

        // Agregar elementos al contenedor
        previewItem.appendChild(img);
        previewItem.appendChild(removeBtn);
        previewContainer.appendChild(previewItem);

        // Mostrar el contenedor
        previewContainer.style.display = 'flex';
    }

    /**
     * Limpia los archivos cargados
     */
    clearFiles() {
        // Limpiar array de archivos
        this.uploadedFiles = [];

        // Limpiar input de archivo
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.value = '';
        }

        // Ocultar previsualización
        const previewContainer = document.getElementById('file-preview-container');
        if (previewContainer) {
            previewContainer.innerHTML = '';
            previewContainer.style.display = 'none';
        }
    }

    /**
     * Obtiene los archivos cargados
     * @returns {Array} - Archivos cargados
     */
    getFiles() {
        return this.uploadedFiles;
    }

    /**
     * Verifica si hay archivos cargados
     * @returns {boolean} - Indica si hay archivos cargados
     */
    hasFiles() {
        return this.uploadedFiles.length > 0;
    }
}

// Exportar una instancia única
export const fileUploadHandler = new FileUploadHandler();

/**
 * Inicializa el manejador de carga de archivos
 */
export function initializeFileUpload() {
    fileUploadHandler.initialize();
}
