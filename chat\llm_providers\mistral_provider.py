"""
Proveedor de LLM para Mistral AI.
"""
from django.conf import settings
import logging
from typing import Dict, List, Optional, Any
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langchain_core.messages import AIMessage, HumanMessage
from mistralai import Mistral

from .base import BaseLLMProvider

logger = logging.getLogger(__name__)

class MistralProvider(BaseLLMProvider):
    """Implementación del proveedor de LLM para Mistral AI."""

    # Modelos disponibles de Mistral AI
    AVAILABLE_MODELS = {
        'mistral-large-latest': {
            'id': 'mistral-large-latest',
            'capabilities': ['text', 'code', 'reasoning', 'multilingual'],
            'max_tokens': 32768,
            'description': 'Modelo más avanzado de Mistral AI con capacidades superiores de razonamiento'
        },
        'mistral-medium-latest': {
            'id': 'mistral-medium-latest',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 32768,
            'description': 'Modelo equilibrado de Mistral AI para uso general'
        },
        'mistral-small-latest': {
            'id': 'mistral-small-latest',
            'capabilities': ['text', 'code'],
            'max_tokens': 32768,
            'description': 'Modelo rápido y eficiente de Mistral AI'
        },
        'codestral-latest': {
            'id': 'codestral-latest',
            'capabilities': ['code', 'text'],
            'max_tokens': 32768,
            'description': 'Modelo especializado en programación y código'
        },
        'pixtral-12b-2409': {
            'id': 'pixtral-12b-2409',
            'capabilities': ['text', 'vision', 'multimodal'],
            'max_tokens': 128000,
            'description': 'Modelo multimodal con capacidades de visión'
        }
    }

    def __init__(self):
        """Inicializa el proveedor de Mistral AI."""
        # Verificar que la clave API esté configurada
        self.api_key = settings.MISTRAL_API_KEY
        if not self.api_key:
            logger.warning("MISTRAL_API_KEY no está configurada. Asegúrate de configurar esta variable de entorno.")

        self._model_name = "mistral-large-latest"
        self.max_tokens = 4096
        self.temperature = 0.7

        # Verificar que el modelo exista en la lista de modelos disponibles
        if self._model_name not in self.AVAILABLE_MODELS:
            logger.warning(f"Modelo Mistral '{self._model_name}' no reconocido. Usando mistral-large-latest como fallback.")
            self._model_name = 'mistral-large-latest'

        # Inicializar el cliente de Mistral
        if self.api_key:
            self._client = Mistral(api_key=self.api_key)
        else:
            self._client = None

        logger.info(f"MistralProvider inicializado con modelo: {self._model_name}")

    def set_model(self, model_name: str) -> None:
        """
        Configura el modelo Mistral a utilizar.

        Args:
            model_name: Nombre del modelo Mistral a utilizar.
        """
        if model_name in self.AVAILABLE_MODELS:
            self._model_name = model_name
            logger.info(f"Modelo Mistral cambiado a: {model_name}")
        else:
            logger.warning(f"Modelo Mistral '{model_name}' no reconocido. Manteniendo {self._model_name}.")

    @property
    def model_name(self) -> str:
        """Nombre del modelo utilizado."""
        return self._model_name

    def query(self, user_input: str, memory_instance: ConversationBufferWindowMemory) -> str:
        """
        Consulta el modelo Mistral con la entrada del usuario y la memoria de conversación.

        Args:
            user_input: Texto de entrada del usuario.
            memory_instance: Instancia de memoria de conversación.

        Returns:
            Respuesta del modelo como texto.
        """
        try:
            # Verificar que la clave API esté configurada
            if not self.api_key or not self._client:
                logger.error("MISTRAL_API_KEY no está configurada. Configura esta variable de entorno.")
                return "Error de configuración: La clave API de Mistral no está configurada. Por favor, contacta al administrador del sistema."

            logger.info(f"Consultando modelo Mistral: {self._model_name}")

            # Obtener mensajes de la memoria
            chat_history = memory_instance.chat_memory.messages
            messages = []

            # Agregar mensaje del sistema
            messages.append({
                "role": "system",
                "content": "Eres un asistente de chat útil y amigable. Responde de manera clara y concisa."
            })

            # Convertir mensajes de la memoria al formato de Mistral
            for message in chat_history:
                if hasattr(message, 'type'):
                    if message.type == 'human':
                        messages.append({"role": "user", "content": message.content})
                    elif message.type == 'ai':
                        messages.append({"role": "assistant", "content": message.content})
                # Compatibilidad con LangChain 0.1.0+
                elif hasattr(message, 'content'):
                    if isinstance(message, HumanMessage):
                        messages.append({"role": "user", "content": message.content})
                    elif isinstance(message, AIMessage):
                        messages.append({"role": "assistant", "content": message.content})

            # Agregar el mensaje actual del usuario
            messages.append({"role": "user", "content": user_input})

            # Llamar a la API de Mistral
            chat_response = self._client.chat.complete(
                model=self._model_name,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            # Extraer la respuesta
            response = chat_response.choices[0].message.content

            # Actualizar la memoria con la respuesta
            memory_instance.chat_memory.add_user_message(user_input)
            memory_instance.chat_memory.add_ai_message(response)

            return response

        except Exception as e:
            # Capturar todos los errores de la API
            logger.error(f"Error al consultar Mistral: {e}")

            error_message = str(e).lower()

            if "unauthorized" in error_message or "authentication" in error_message:
                return "Error: No autorizado. La clave API de Mistral no es válida o ha expirado."
            elif "rate limit" in error_message or "too many requests" in error_message:
                return "Error: Se ha excedido el límite de solicitudes a la API. Por favor, intenta de nuevo más tarde."
            elif "bad request" in error_message or "invalid request" in error_message:
                return "Error: La solicitud al modelo de lenguaje no es válida. Esto puede deberse a un problema con el formato de la solicitud."
            elif "connection" in error_message or "timeout" in error_message:
                return "Error: No se pudo establecer conexión con el servicio de Mistral. Por favor, verifica tu conexión a Internet."
            else:
                return f"Error: No se pudo completar la solicitud al modelo de lenguaje. Detalles: {str(e)}"

    def get_model_info(self) -> Dict[str, Any]:
        """
        Obtiene información sobre el modelo.

        Returns:
            Diccionario con información del modelo.
        """
        model_info = self.AVAILABLE_MODELS.get(self._model_name, {})
        return {
            "name": self._model_name,
            "provider": "Mistral AI",
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "capabilities": model_info.get('capabilities', ['text', 'code']),
            "description": model_info.get('description', '')
        }

    @classmethod
    def get_available_models(cls) -> Dict[str, Dict[str, Any]]:
        """
        Obtiene la lista de modelos disponibles.

        Returns:
            Diccionario con información de los modelos disponibles.
        """
        return cls.AVAILABLE_MODELS
