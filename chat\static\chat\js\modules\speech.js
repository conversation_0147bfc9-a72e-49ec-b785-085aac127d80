/**
 * <PERSON><PERSON><PERSON><PERSON> speech.js - Reconocimiento de voz
 * 
 * Este módulo contiene funciones para el reconocimiento de voz.
 */

import { isFeatureSupported } from './utils.js';

/**
 * Inicializa el reconocimiento de voz.
 */
export function initializeSpeechRecognition() {
  // Verificar si el navegador soporta reconocimiento de voz
  if (!isFeatureSupported('speechRecognition')) {
    console.warn('El reconocimiento de voz no está soportado en este navegador');
    return;
  }
  
  const micButton = document.getElementById('voice-input-btn');
  if (!micButton) {
    console.warn('Botón de micrófono no encontrado');
    return;
  }
  
  let isRecording = false;
  let recognition;
  
  // Crear instancia de reconocimiento de voz
  if ('webkitSpeechRecognition' in window) {
    recognition = new webkitSpeechRecognition();
  } else if ('SpeechRecognition' in window) {
    recognition = new SpeechRecognition();
  } else {
    return;
  }
  
  // Configurar reconocimiento
  recognition.continuous = true;
  recognition.interimResults = true;
  recognition.lang = 'es-ES';
  
  // Gestión de timeout
  let timeoutId = null;
  const RECORDING_TIMEOUT = 30000; // 30 segundos
  
  // Agregar estilos para animación
  addBlinkingStyles();
  
  // Eventos de reconocimiento
  recognition.onstart = function() {
    isRecording = true;
    micButton.classList.add('active', 'blinking');
    
    // Limpiar timeout existente
    if (timeoutId) clearTimeout(timeoutId);
    
    // Establecer nuevo timeout
    timeoutId = setTimeout(() => {
      if (isRecording) recognition.stop();
    }, RECORDING_TIMEOUT);
  };
  
  recognition.onend = function() {
    isRecording = false;
    micButton.classList.remove('active', 'blinking');
    
    if (timeoutId) clearTimeout(timeoutId);
  };
  
  recognition.onresult = function(event) {
    const transcript = Array.from(event.results)
      .map(result => result[0].transcript)
      .join('');
    
    document.getElementById('user-input').value = transcript;
    
    // Resetear timeout con nuevos resultados
    if (isRecording) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        if (isRecording) recognition.stop();
      }, RECORDING_TIMEOUT);
    }
  };
  
  recognition.onerror = function(event) {
    console.error('Error de reconocimiento de voz:', event.error);
    isRecording = false;
    micButton.classList.remove('active', 'blinking');
  };
  
  // Evento de clic en el botón
  micButton.addEventListener('click', function() {
    if (isRecording) {
      recognition.stop();
    } else {
      recognition.start();
    }
  });
}

/**
 * Agrega estilos para la animación de parpadeo.
 */
function addBlinkingStyles() {
  // Verificar si los estilos ya existen
  if (document.getElementById('speech-recognition-styles')) return;
  
  const style = document.createElement('style');
  style.id = 'speech-recognition-styles';
  style.textContent = `
    .active {
      color: #ff0000 !important;
    }
    
    .blinking {
      animation: blink 1s infinite;
    }
    
    @keyframes blink {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
  `;
  
  document.head.appendChild(style);
}
