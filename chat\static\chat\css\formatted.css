/* formatted.css - Styles for formatted message content */

/* General formatting */
.message-content {
  line-height: 1.5;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

/* Headings */
.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  font-weight: 600;
  line-height: 1.25;
}

.message-content h1 {
  font-size: 1.75em;
}

.message-content h2 {
  font-size: 1.5em;
}

.message-content h3 {
  font-size: 1.25em;
}

/* Paragraphs */
.message-content p {
  margin-bottom: 1em;
}

/* Lists */
.message-content ul,
.message-content ol {
  margin-top: 0.5em;
  margin-bottom: 1em;
  padding-left: 2em;
  list-style-position: outside;
}

.message-content li {
  margin-bottom: 0.25em;
  display: list-item;
}

.message-content li > ul,
.message-content li > ol {
  margin-top: 0.25em;
  margin-bottom: 0.5em;
}

/* Code blocks */
.message-content pre {
  margin: 1em 0;
  padding: 1em;
  border-radius: 0.375rem;
  background-color: rgba(0, 0, 0, 0.2);
  overflow-x: auto;
}

.message-content code {
  font-family: var(--font-family-code);
  font-size: 0.85em;
  padding: 2px 4px;
  border-radius: 3px;
  background: var(--inline-code-bg);
  color: var(--inline-code-text);
  border: 1px solid var(--inline-code-border);
}

.message-content pre code {
  padding: 0;
  background-color: transparent;
  border: none;
  border-radius: 0;
  font-size: 0.9em;
  line-height: 1.5;
}

/* Tables */
.message-content table {
  margin: 1em 0;
  border-collapse: collapse;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-content th,
.message-content td {
  padding: 0.5em 0.75em;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: left;
}

.message-content th {
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.1);
  border-bottom: 2px solid rgba(126, 231, 135, 0.5);
}

.message-content tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Blockquotes */
.message-content blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid rgba(126, 231, 135, 0.5);
  background-color: rgba(0, 0, 0, 0.1);
  font-style: italic;
  border-radius: 0.425rem;
}

/* Links */
.message-content a {
  color: #7ee787;
  text-decoration: none;
  transition: color 0.2s ease;
}

.message-content a:hover {
  text-decoration: underline;
  color: #a5f0a5;
}

/* Theme-specific text colors */
.light-theme .message-content {
  color: var(--text-primary);
}

.light-theme .message-content a {
  color: var(--accent-primary);
}

.light-theme .message-content a:hover {
  color: var(--accent-secondary);
}

/* Math blocks */
.message-content .math-block {
  margin: 1em 0;
  padding: 0.5em;
  overflow-x: auto;
  text-align: center;
}

.message-content .math-inline {
  padding: 0 0.2em;
}

/* Copy button for code blocks */
.message-content pre {
  position: relative;
}

.message-content pre .copy-code-button {
  position: absolute;
  top: 0.5em;
  right: 0.5em;
  padding: 0.25em 0.5em;
  background-color: rgba(0, 0, 0, 0.3);
  border: none;
  border-radius: 0.25rem;
  color: #fff;
  font-size: 0.8em;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-content pre:hover .copy-code-button {
  opacity: 1;
}

.message-content pre .copy-code-button:hover {
  background-color: rgba(126, 231, 135, 0.5);
}

/* Horizontal rule */
.message-content hr {
  margin: 1.5em 0;
  border: 0;
  border-top: 1px solid var(--border-color);
}

/* Images */
.message-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1em 0;
}
