/* button-patterns.css - Common button patterns to reduce duplication */

/* Base button reset - used by all custom buttons */
.btn-reset {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    outline: none;
}

/* Close button pattern - used across modals and dialogs */
.btn-close {
    background: none;
    border: none;
    color: var(--text-dimmed);
    cursor: pointer;
    padding: 0;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
}

.btn-close:hover {
    color: var(--text-primary);
    background-color: rgba(var(--text-dimmed-rgb), 0.1);
}

.btn-close:active {
    transform: scale(0.95);
}

/* Icon button pattern - square buttons with icons */
.btn-icon {
    background: none;
    border: none;
    color: var(--text-dimmed);
    cursor: pointer;
    width: var(--icon-button-size);
    height: var(--icon-button-size);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    padding: 0;
}

.btn-icon:hover {
    color: var(--text-primary);
    background-color: rgba(var(--accent-primary-rgb), 0.1);
}

.btn-icon:active {
    transform: scale(0.95);
}

/* Large icon button variant */
.btn-icon-lg {
    width: var(--icon-button-size-lg);
    height: var(--icon-button-size-lg);
}

/* Action button pattern - primary actions */
.btn-action {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: var(--button-padding);
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    border: 1px solid transparent;
    text-decoration: none;
}

.btn-action:active {
    transform: scale(0.98);
}

/* Primary action button */
.btn-action-primary {
    background-color: var(--accent-primary);
    color: var(--text-primary);
    border-color: var(--accent-primary);
}

.btn-action-primary:hover {
    background-color: var(--accent-secondary);
    border-color: var(--accent-secondary);
}

/* Danger action button */
.btn-action-danger {
    background-color: transparent;
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-action-danger:hover {
    background-color: rgba(var(--danger-color-rgb), 0.1);
}

/* Secondary action button */
.btn-action-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-action-secondary:hover {
    background-color: var(--bg-quaternary);
}

/* Small action button variant */
.btn-action-sm {
    padding: var(--button-padding-sm);
    font-size: 0.8rem;
}

/* Large action button variant */
.btn-action-lg {
    padding: var(--button-padding-lg);
    font-size: 1rem;
}

/* Notification close button pattern */
.btn-notification-close {
    background: none;
    border: none;
    color: var(--text-dimmed);
    cursor: pointer;
    font-size: 1rem;
    line-height: 1;
    padding: 0;
    margin-left: 8px;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.btn-notification-close:hover {
    opacity: 1;
}

/* Delete button pattern with hover state */
.btn-delete {
    background: none;
    border: none;
    color: var(--text-dimmed);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-delete:hover {
    color: var(--danger-color);
    background-color: rgba(var(--danger-color-rgb), 0.1);
}

.btn-delete:active {
    transform: scale(0.95);
}

/* Toggle button pattern */
.btn-toggle {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-dimmed);
    cursor: pointer;
    transition: var(--transition-fast);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-toggle:hover {
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

.btn-toggle:active {
    transform: scale(0.95);
}

/* Floating action button pattern */
.btn-floating {
    position: fixed;
    background-color: var(--accent-primary);
    color: var(--text-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--card-shadow);
    transition: var(--transition-fast);
    border: none;
    z-index: 1000;
}

.btn-floating:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Disabled state for all button patterns */
.btn-action:disabled,
.btn-icon:disabled,
.btn-close:disabled,
.btn-toggle:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Focus states for accessibility */
.btn-action:focus-visible,
.btn-icon:focus-visible,
.btn-close:focus-visible,
.btn-toggle:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}
