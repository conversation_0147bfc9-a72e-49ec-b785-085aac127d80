/**
 * Module segment-chat.js - Segment-specific chat functionality
 *
 * This module handles text selection in bot messages and provides
 * a dedicated chat interface for discussing specific text segments.
 */

class SegmentChat {
  constructor() {
    this.isInitialized = false;
    this.activeSegmentChats = new Map();
    this.currentSelection = null;
    this.contextMenu = null;
    this.modal = null;
    this.currentSegmentChatId = null;
    this.csrfToken = null;
  }

  /**
   * Initialize the segment chat functionality
   */
  initialize() {
    if (this.isInitialized) return;

    this.csrfToken = this.getCSRFToken();
    this.createContextMenu();
    this.createModal();
    this.attachEventListeners();
    this.isInitialized = true;

    console.log('SegmentChat initialized');
  }

  /**
   * Get CSRF token for API requests
   */
  getCSRFToken() {
    const token = document.querySelector('[name=csrfmiddlewaretoken]');
    return token ? token.value : '';
  }

  /**
   * Create the context menu for text selection
   */
  createContextMenu() {
    if (document.getElementById('segment-context-menu')) return;

    this.contextMenu = document.createElement('div');
    this.contextMenu.id = 'segment-context-menu';
    this.contextMenu.className = 'segment-context-menu';
    this.contextMenu.innerHTML = `
      <button id="segment-discuss-btn">
        <i class='bx bx-chat'></i>
        Discuss this segment
      </button>
    `;

    document.body.appendChild(this.contextMenu);

    // Add click handler for the discuss button
    const discussBtn = this.contextMenu.querySelector('#segment-discuss-btn');
    discussBtn.addEventListener('click', () => this.startSegmentChat());
  }

  /**
   * Create the segment chat modal
   */
  createModal() {
    if (document.getElementById('segment-chat-modal')) return;

    this.modal = document.createElement('div');
    this.modal.id = 'segment-chat-modal';
    this.modal.className = 'segment-chat-modal';
    this.modal.innerHTML = `
      <div class="segment-chat-content">
        <div class="segment-chat-header">
          <h3 class="segment-chat-title">Segment Discussion</h3>
          <button class="segment-chat-close">&times;</button>
        </div>
        <div class="segment-context">
          <div class="segment-context-label">Discussing this text:</div>
          <div class="segment-context-text"></div>
        </div>
        <div class="segment-chat-messages" id="segment-chat-messages">
          <!-- Messages will be inserted here -->
        </div>
        <div class="segment-chat-input">
          <div class="segment-input-container">
            <textarea
              id="segment-input-field"
              class="segment-input-field"
              placeholder="Ask about this text segment..."
              rows="1"
            ></textarea>
            <button id="segment-send-btn" class="segment-send-btn">Send</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(this.modal);

    // Add event listeners for modal
    const closeBtn = this.modal.querySelector('.segment-chat-close');
    closeBtn.addEventListener('click', () => this.closeSegmentChat());

    const sendBtn = this.modal.querySelector('#segment-send-btn');
    sendBtn.addEventListener('click', () => this.sendSegmentMessage());

    const inputField = this.modal.querySelector('#segment-input-field');
    inputField.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendSegmentMessage();
      }
    });

    // Auto-resize textarea
    inputField.addEventListener('input', () => {
      inputField.style.height = 'auto';
      inputField.style.height = Math.min(inputField.scrollHeight, 120) + 'px';
    });

    // Close modal when clicking outside
    this.modal.addEventListener('click', (e) => {
      if (e.target === this.modal) {
        this.closeSegmentChat();
      }
    });
  }

  /**
   * Attach event listeners for text selection
   */
  attachEventListeners() {
    // Listen for text selection in bot messages
    document.addEventListener('mouseup', (e) => this.handleTextSelection(e));
    document.addEventListener('keyup', (e) => this.handleTextSelection(e));

    // Hide context menu when clicking elsewhere
    document.addEventListener('mousedown', (e) => {
      if (!this.contextMenu.contains(e.target) && !this.isSelectionEvent(e)) {
        this.cleanupSelectionState();
      }
    });

    // Prevent context menu from interfering with selection
    document.addEventListener('contextmenu', (e) => {
      const selection = window.getSelection();
      if (selection.rangeCount > 0 && !selection.isCollapsed) {
        const range = selection.getRangeAt(0);
        const botMessage = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
          ? range.commonAncestorContainer.parentElement.closest('.bot-message')
          : range.commonAncestorContainer.closest('.bot-message');

        if (botMessage) {
          e.preventDefault(); // Prevent browser context menu for bot messages with selection
        }
      }
    });
  }

  /**
   * Handle text selection in bot messages
   */
  handleTextSelection(e) {
    // Small delay to ensure selection is complete
    setTimeout(() => {
      const selection = window.getSelection();

      if (selection.rangeCount === 0 || selection.isCollapsed) {
        this.hideContextMenu();
        return;
      }

      const selectedText = selection.toString().trim();
      if (selectedText.length < 10) { // Minimum text length for segment chat
        this.hideContextMenu();
        return;
      }

      // Check if selection is within a bot message
      const range = selection.getRangeAt(0);
      const botMessage = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
        ? range.commonAncestorContainer.parentElement.closest('.bot-message')
        : range.commonAncestorContainer.closest('.bot-message');

      if (!botMessage) {
        this.hideContextMenu();
        return;
      }

      // Get the message ID from the bot message element
      const messageId = botMessage.dataset.messageId;
      if (!messageId) {
        this.hideContextMenu();
        return;
      }

      // Store current selection info
      this.currentSelection = {
        text: selectedText,
        messageId: messageId,
        range: range.cloneRange()
      };

      // Show context menu automatically near the selection
      this.showContextMenuForSelection();
    }, 10);
  }

  /**
   * Check if the event is part of a text selection process
   */
  isSelectionEvent(e) {
    const selection = window.getSelection();
    return selection.rangeCount > 0 && !selection.isCollapsed;
  }

  /**
   * Clear the current text selection
   */
  clearSelection() {
    if (window.getSelection) {
      window.getSelection().removeAllRanges();
    }
    this.currentSelection = null;
  }

  /**
   * Clear text selection with smooth visual transition
   */
  clearSelectionSmooth() {
    if (!this.currentSelection) return;

    // Find the bot message element to add clearing class
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const botMessage = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
        ? range.commonAncestorContainer.parentElement.closest('.bot-message')
        : range.commonAncestorContainer.closest('.bot-message');

      if (botMessage) {
        const messageContent = botMessage.querySelector('.message-content');
        if (messageContent) {
          // Add clearing class for smooth transition
          messageContent.classList.add('clearing-selection');

          // Remove selection after brief delay
          setTimeout(() => {
            this.clearSelection();
            messageContent.classList.remove('clearing-selection');
          }, 150);
          return;
        }
      }
    }

    // Fallback to immediate clearing if we can't find the message element
    this.clearSelection();
  }

  /**
   * Clean up all selection-related visual state smoothly
   */
  cleanupSelectionState() {
    // Start smooth text selection clearing immediately
    this.clearSelectionSmooth();

    // Add fade-out class to context menu for smooth transition
    if (this.contextMenu && this.contextMenu.classList.contains('active')) {
      this.contextMenu.classList.add('fade-out');

      // Hide context menu after fade animation
      setTimeout(() => {
        this.hideContextMenu();
        this.contextMenu.classList.remove('fade-out');
      }, 200);
    } else {
      // If context menu isn't active, hide it immediately
      this.hideContextMenu();
    }
  }

  /**
   * Force immediate cleanup of all visual states (for error recovery)
   */
  forceCleanupState() {
    this.hideContextMenu();
    this.clearSelection();
    if (this.contextMenu) {
      this.contextMenu.classList.remove('fade-out');
    }
  }

  /**
   * Show context menu for the current selection
   */
  showContextMenuForSelection() {
    if (!this.contextMenu || !this.currentSelection) return;

    const rect = this.currentSelection.range.getBoundingClientRect();
    const menuWidth = 200; // Approximate width of context menu
    const menuHeight = 40; // Approximate height of context menu

    let left = rect.left + (rect.width / 2) - (menuWidth / 2);
    let top = rect.bottom + 10;

    // Adjust position if menu would go off screen
    if (left + menuWidth > window.innerWidth) {
      left = window.innerWidth - menuWidth - 10;
    }
    if (left < 10) {
      left = 10;
    }
    if (top + menuHeight > window.innerHeight) {
      top = rect.top - menuHeight - 10;
    }

    this.contextMenu.style.left = left + 'px';
    this.contextMenu.style.top = top + 'px';
    this.contextMenu.classList.add('active');
  }

  /**
   * Show context menu at the specified position (legacy method for compatibility)
   */
  showContextMenu(e) {
    this.showContextMenuForSelection();
  }

  /**
   * Hide the context menu
   */
  hideContextMenu() {
    if (this.contextMenu) {
      this.contextMenu.classList.remove('active');
    }
  }

  /**
   * Start a new segment chat
   */
  async startSegmentChat() {
    if (!this.currentSelection) return;

    // Smoothly hide context menu and clear selection before opening modal
    this.cleanupSelectionState();

    try {
      // Create new segment chat
      const response = await fetch('/chat/api/segment/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.csrfToken,
        },
        body: JSON.stringify({
          action: 'create_segment',
          parent_message_id: this.currentSelection.messageId,
          selected_text: this.currentSelection.text
        })
      });

      if (!response.ok) throw new Error('Failed to create segment chat');

      const data = await response.json();

      if (data.status === 'success') {
        this.currentSegmentChatId = data.segment_chat_id;
        // Small delay to ensure cleanup animation completes before opening modal
        setTimeout(() => {
          this.openSegmentChatModal(data);
        }, 150);
      } else {
        throw new Error(data.error || 'Unknown error');
      }

    } catch (error) {
      console.error('Error starting segment chat:', error);
      this.showNotification('Failed to start segment chat', 'error');
      // Restore context menu if there was an error
      this.showContextMenuForSelection();
    }
  }

  /**
   * Open the segment chat modal
   */
  openSegmentChatModal(segmentChatData) {
    if (!this.modal) return;

    // Update modal content
    const title = this.modal.querySelector('.segment-chat-title');
    const contextText = this.modal.querySelector('.segment-context-text');
    const messagesContainer = this.modal.querySelector('#segment-chat-messages');

    title.textContent = segmentChatData.title || 'Segment Discussion';
    contextText.textContent = segmentChatData.selected_text;
    messagesContainer.innerHTML = '';

    // Load existing messages if any
    if (segmentChatData.messages && segmentChatData.messages.length > 0) {
      this.renderSegmentMessages(segmentChatData.messages);
    }

    // Show modal
    this.modal.classList.add('active');

    // Focus input field
    setTimeout(() => {
      const inputField = this.modal.querySelector('#segment-input-field');
      if (inputField) inputField.focus();
    }, 300);
  }

  /**
   * Close the segment chat modal
   */
  closeSegmentChat() {
    if (this.modal) {
      this.modal.classList.remove('active');
    }
    this.currentSegmentChatId = null;

    // Ensure complete cleanup of all visual states
    this.forceCleanupState();
  }

  /**
   * Send a message in the segment chat
   */
  async sendSegmentMessage() {
    const inputField = this.modal.querySelector('#segment-input-field');
    const sendBtn = this.modal.querySelector('#segment-send-btn');
    const message = inputField.value.trim();

    if (!message || !this.currentSegmentChatId) return;

    // Disable input while sending
    inputField.disabled = true;
    sendBtn.disabled = true;

    // Add user message to UI immediately
    this.addSegmentMessage(message, 'user');
    inputField.value = '';
    inputField.style.height = 'auto';

    // Show loading indicator
    this.showSegmentLoading();

    try {
      const response = await fetch('/chat/api/segment/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.csrfToken,
        },
        body: JSON.stringify({
          action: 'send_message',
          segment_chat_id: this.currentSegmentChatId,
          message: message
        })
      });

      if (!response.ok) throw new Error('Failed to send message');

      const data = await response.json();

      if (data.status === 'success') {
        // Remove loading indicator
        this.hideSegmentLoading();

        // Add bot response
        this.addSegmentMessage(data.response, 'bot');
      } else {
        throw new Error(data.error || 'Unknown error');
      }

    } catch (error) {
      console.error('Error sending segment message:', error);
      this.hideSegmentLoading();
      this.showNotification('Failed to send message', 'error');
    } finally {
      // Re-enable input
      inputField.disabled = false;
      sendBtn.disabled = false;
      inputField.focus();
    }
  }

  /**
   * Add a message to the segment chat UI
   */
  addSegmentMessage(content, sender) {
    const messagesContainer = this.modal.querySelector('#segment-chat-messages');

    const messageElement = document.createElement('div');
    messageElement.className = `segment-message ${sender}`;

    const contentElement = document.createElement('div');
    contentElement.className = 'segment-message-content';
    contentElement.textContent = content;

    messageElement.appendChild(contentElement);
    messagesContainer.appendChild(messageElement);

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  /**
   * Show loading indicator in segment chat
   */
  showSegmentLoading() {
    const messagesContainer = this.modal.querySelector('#segment-chat-messages');

    const loadingElement = document.createElement('div');
    loadingElement.className = 'segment-loading';
    loadingElement.id = 'segment-loading-indicator';
    loadingElement.textContent = 'Thinking...';

    messagesContainer.appendChild(loadingElement);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  /**
   * Hide loading indicator in segment chat
   */
  hideSegmentLoading() {
    const loadingElement = this.modal.querySelector('#segment-loading-indicator');
    if (loadingElement) {
      loadingElement.remove();
    }
  }

  /**
   * Render segment messages
   */
  renderSegmentMessages(messages) {
    const messagesContainer = this.modal.querySelector('#segment-chat-messages');
    messagesContainer.innerHTML = '';

    messages.forEach(msg => {
      this.addSegmentMessage(msg.content, msg.sender);
    });
  }

  /**
   * Show notification
   */
  showNotification(message, type = 'info') {
    // Use existing notification system if available
    if (window.showNotification) {
      window.showNotification(message, type);
    } else {
      console.log(`${type.toUpperCase()}: ${message}`);
    }
  }
}

// Create and export segment chat instance
const segmentChat = new SegmentChat();

// Make segment chat available globally
window.segmentChat = segmentChat;

/**
 * Initialize the segment chat functionality
 */
export function initializeSegmentChat() {
  segmentChat.initialize();
}

export default segmentChat;
