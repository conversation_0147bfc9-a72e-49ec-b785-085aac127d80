/* Model Indicator Styles */
.model-indicator {
    cursor: pointer;
    padding: 4px 12px;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-primary);
    background-color: transparent;
    border: none;
    transition: all 0.2s ease;
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 100;
    border: 1px solid var(--border-color);
}

.model-indicator:hover {
    color: var(--accent-primary);
}

/* Provider-specific indicators */
.model-indicator.grok {
    color: var(--accent-secondary);
}

.model-indicator.groq {
    color: var(--success-color);
}

.model-indicator.gemini {
    color: var(--accent-primary);
}

.model-indicator.multi_agent {
    color: var(--warning-color);
}

.model-indicator.mistral {
    color: #ff7000;
}

/* Light theme adjustments */
.light-theme .model-indicator {
    color: var(--text-primary);
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
    font-weight: 500;
}

.light-theme .model-indicator:hover {
    color: var(--accent-primary);
}

.light-theme .model-indicator.grok {
    color: var(--accent-secondary);
}

.light-theme .model-indicator.groq {
    color: var(--success-color);
}

.light-theme .model-indicator.gemini {
    color: var(--accent-primary);
}

.light-theme .model-indicator.multi_agent {
    color: var(--warning-color);
}

.light-theme .model-indicator.mistral {
    color: var(--warning-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .model-indicator {
        font-size: 0.7rem;
        padding: 3px 10px;
    }
}

/* Modal styles */
.model-selector-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--bg-primary-rgb), 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    backdrop-filter: blur(5px);
}

.model-selector-modal.active {
    opacity: 1;
    visibility: visible;
}

.model-selector-content {
    background-color: var(--bg-secondary);
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.model-selector-modal.active .model-selector-content {
    transform: translateY(0);
}

.model-selector-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.model-selector-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.close-model-selector {
    background: none;
    border: none;
    color: var(--text-dimmed);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    transition: color 0.2s ease;
}

.close-model-selector:hover {
    color: var(--text-primary);
}

.model-selector-body {
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
}

/* Tabs styles */
.model-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-tertiary);
}

.model-tab {
    padding: 12px 20px;
    cursor: pointer;
    color: var(--text-dimmed);
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
}

.model-tab:hover {
    color: var(--text-primary);
    background-color: rgba(var(--accent-primary-rgb), 0.1);
}

.model-tab.model-tab.active {
    color: var(--text-primary) !important;
    background-color: var(--bg-secondary);
}

.model-tab.model-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--accent-primary);
}

/* Model list styles */
.model-list {
    padding: 20px;
    overflow-y: auto;
    max-height: 60vh;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
}

.model-card {
    background-color: var(--bg-tertiary);
    border-radius: 8px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.model-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow);
    border-color: var(--accent-primary);
}

.model-card.model-card.active {
    border-color: var(--accent-primary);
    background-color: rgba(var(--accent-primary-rgb), 0.1);
    color: var(--text-primary) !important;
}

.model-info {
    margin-bottom: 16px;
}

.model-name {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.model-description {
    margin: 0 0 12px 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.model-capabilities {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 10px;
}

.capability-tag {
    background-color: rgba(var(--accent-primary-rgb), 0.2);
    color: var(--text-primary);
    font-size: 0.75rem;
    padding: 3px 8px;
    border-radius: 12px;
    display: inline-block;
}

.select-model-btn {
    background-color: var(--bg-quaternary);
    color: var(--text-primary);
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    align-self: flex-end;
    font-weight: 500;
}

.select-model-btn:hover {
    background-color: var(--accent-primary);
}

.select-model-btn.select-model-btn.active {
    background-color: var(--accent-primary);
    color: var(--text-primary) !important;
    cursor: default;
}

.select-model-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Notification styles */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.notification {
    background-color: var(--bg-secondary);
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: var(--card-shadow);
    border-left: 4px solid var(--accent-primary);
    animation: slideIn 0.3s ease forwards;
    transform: translateX(100%);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.error {
    border-left-color: var(--danger-color);
}

.notification.closing {
    animation: slideOut 0.3s ease forwards;
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notification-message {
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-right: 10px;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-dimmed);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Light theme adjustments */
.light-theme .model-selector-content {
    background-color: var(--bg-secondary);
    box-shadow: var(--card-shadow);
}

.light-theme .model-tabs {
    background-color: var(--bg-tertiary);
}

.light-theme .model-tab {
    color: var(--text-dimmed);
}

.light-theme .model-tab:hover {
    background-color: rgba(var(--accent-primary-rgb), 0.1);
}

.light-theme .model-tab.model-tab.active {
    background-color: var(--bg-secondary);
    color: var(--text-primary) !important;
}

.light-theme .model-card {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
}

.light-theme .model-card:hover {
    border-color: var(--accent-primary);
    box-shadow: var(--card-shadow);
}

.light-theme .model-card.model-card.active {
    background-color: rgba(var(--accent-primary-rgb), 0.05);
    border-color: var(--accent-primary);
    color: var(--text-primary) !important;
}

.light-theme .capability-tag {
    background-color: rgba(var(--accent-primary-rgb), 0.1);
    color: var(--text-primary);
}

.light-theme .select-model-btn {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.light-theme .select-model-btn:hover,
.light-theme .select-model-btn.select-model-btn.active {
    background-color: var(--accent-primary);
    color: var(--bg-primary) !important;
}

.light-theme .notification {
    background-color: var(--bg-primary);
    box-shadow: var(--card-shadow);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .model-list {
        grid-template-columns: 1fr;
    }

    .model-selector-content {
        width: 95%;
        max-height: 90vh;
    }
}
