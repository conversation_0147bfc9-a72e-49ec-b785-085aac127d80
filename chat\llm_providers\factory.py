"""
Factory para crear instancias de proveedores de LLM.
"""
from django.conf import settings
import logging
from typing import Dict, Type

from .base import BaseLLMProvider
from .grok_provider import GrokProvider
from .groq_provider import GroqProvider
from .gemini_provider import GeminiProvider
from .multi_agent_provider import MultiAgentProvider
from .mistral_provider import MistralProvider

logger = logging.getLogger(__name__)

# Registro de proveedores disponibles
PROVIDERS: Dict[str, Type[BaseLLMProvider]] = {
    'grok': GrokProvider,
    'groq': GroqProvider,
    'gemini': GeminiProvider,
    'multi_agent': MultiAgentProvider,
    'mistral': MistralProvider,
}

def get_llm_provider(context_or_request=None, provider_name=None, model_name=None) -> BaseLLMProvider:
    """
    Obtiene una instancia del proveedor de LLM basado en el contexto o parámetros.

    Args:
        context_or_request: Contexto LLM o solicitud HTTP (opcional)
        provider_name: Nombre del proveedor a usar (opcional, anula el contexto)
        model_name: Nombre del modelo a usar (opcional, anula el contexto)

    Returns:
        Instancia de un proveedor de LLM.

    Raises:
        ValueError: Si el proveedor configurado no está disponible.
    """
    # Importar aquí para evitar importación circular
    from django.http import HttpRequest
    from .context import LLMRequestContext

    # Crear contexto a partir de los parámetros
    if isinstance(context_or_request, LLMRequestContext):
        context = context_or_request
    elif isinstance(context_or_request, HttpRequest):
        # Si es una solicitud HTTP, usar el contexto de la solicitud o crear uno nuevo
        context = getattr(context_or_request, 'llm_context', None)
        if not context:
            context = LLMRequestContext(context_or_request, provider_name, model_name)
    elif isinstance(context_or_request, str):
        # Para compatibilidad con versiones anteriores, si es un string, tratarlo como provider_name
        context = LLMRequestContext(provider_name=context_or_request or provider_name, model_name=model_name)
    else:
        # Crear un nuevo contexto con los parámetros proporcionados
        context = LLMRequestContext(provider_name=provider_name, model_name=model_name)

    # Obtener el nombre del proveedor del contexto
    provider_name = context.provider_name

    logger.info(f"get_llm_provider - Solicitando proveedor: {provider_name}")

    if provider_name not in PROVIDERS:
        available_providers = ', '.join(PROVIDERS.keys())
        logger.error(f"Proveedor LLM '{provider_name}' no disponible. Opciones: {available_providers}")
        # Fallback a Grok si está disponible, de lo contrario usar el primero disponible
        provider_name = 'grok' if 'grok' in PROVIDERS else next(iter(PROVIDERS))
        logger.info(f"Usando proveedor de fallback: {provider_name}")

    # Crear instancia del proveedor
    provider_class = PROVIDERS[provider_name]
    provider = provider_class()

    # Si se especificó un modelo, configurarlo
    if context.model_name and hasattr(provider, 'set_model'):
        provider.set_model(context.model_name)

    return provider
