document.addEventListener("DOMContentLoaded", function () {
    const micIcon = document.querySelector('.input-icon-right');

    // Si el icono del micrófono no existe, salir de la función
    if (!micIcon) {
        console.warn('Micrófono no encontrado en la página');
        return;
    }
    let isRecording = false;
    let recognition;

    // Add CSS for blinking animation
    const style = document.createElement('style');
    style.textContent = `
        .active {
            color: #ff0000; /* or any color you prefer */
        }
        .blinking {
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    `;
    document.head.appendChild(style);

    // Add at the beginning of the file, after the DOMContentLoaded event
    const showStatus = (message, isError = false) => {
        const existingStatus = document.getElementById('stt-status');
        if (existingStatus) existingStatus.remove();
        
        const statusElement = document.createElement('div');
        statusElement.id = 'stt-status';
        statusElement.textContent = message;
        statusElement.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: ${isError ? 'rgba(255, 0, 0, 0.7)' : 'rgba(0, 128, 0, 0.7)'};
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        `;
        document.body.appendChild(statusElement);
        
        if (!isError) {
            setTimeout(() => statusElement.remove(), 3000);
        }
    };

    // Comprobar compatibilidad con distintos navegadores
    try {
        if ('webkitSpeechRecognition' in window) {
            recognition = new webkitSpeechRecognition();
            console.log('Using webkitSpeechRecognition');
        } else if ('SpeechRecognition' in window) {
            recognition = new SpeechRecognition();
            console.log('Using SpeechRecognition');
        } else {
            throw new Error("Speech recognition not supported in this browser.");
        }
    } catch (error) {
        console.error('Speech recognition initialization error:', error);
        showStatus('Error: Speech recognition not available', true);
        return;
    }

    // Configuración del reconocimiento de voz
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'es-ES';
    recognition.maxAlternatives = 1;

    // Add timeout management
    let timeoutId = null;
    const RECORDING_TIMEOUT = 30000; // 30 seconds timeout (adjust as needed)
    const SILENCE_TIMEOUT = 3000; // 3 seconds of silence to auto-stop

    let lastResultTimestamp = Date.now();

    recognition.onstart = function () {
        isRecording = true;
        micIcon.classList.add('active', 'blinking');
        // Clear any existing timeout
        if (timeoutId) clearTimeout(timeoutId);
        // Set new timeout
        timeoutId = setTimeout(() => {
            if (isRecording) recognition.stop();
        }, RECORDING_TIMEOUT);

        // Mostrar indicador visual de que está grabando
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'recording-status';
        statusIndicator.textContent = 'Grabando...';
        statusIndicator.style.cssText = 'position: fixed; bottom: 20px; right: 20px; background-color: rgba(255, 0, 0, 0.7); color: white; padding: 10px; border-radius: 5px; z-index: 1000;';
        document.body.appendChild(statusIndicator);
    };

    recognition.onend = function () {
        isRecording = false;
        micIcon.classList.remove('active', 'blinking');
        if (timeoutId) clearTimeout(timeoutId);
        
        // Eliminar indicador visual
        const statusIndicator = document.getElementById('recording-status');
        if (statusIndicator) {
            statusIndicator.remove();
        }
    };

    recognition.onresult = function (event) {
        lastResultTimestamp = Date.now();
        
        // Restart silence detection timer
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            // Verificar si ha pasado suficiente tiempo sin nuevos resultados
            if (Date.now() - lastResultTimestamp > SILENCE_TIMEOUT && isRecording) {
                console.log("Silencio detectado, deteniendo grabación");
                recognition.stop();
            }
        }, SILENCE_TIMEOUT);

        const transcript = Array.from(event.results)
            .map(result => result[0].transcript)
            .join('');

        // Actualizar el campo de entrada
        const userInput = document.getElementById('user-input');
        if (userInput) {
            userInput.value = transcript;
            
            // Opcional: Activar el botón de envío automáticamente al terminar
            // if (!isRecording && transcript.trim().length > 0) {
            //     const sendButton = document.querySelector('button[type="submit"]');
            //     if (sendButton) sendButton.click();
            // }
        }
    };

    recognition.onerror = function (event) {
        console.error('Speech recognition error:', event.error);
        isRecording = false;
        micIcon.classList.remove('active', 'blinking');
        
        let errorMessage = 'Error en el reconocimiento de voz: ';
        switch (event.error) {
            case 'network':
                errorMessage += 'Error de red';
                break;
            case 'no-speech':
                errorMessage += 'No se detectó audio';
                break;
            case 'audio-capture':
                errorMessage += 'Error al capturar audio';
                break;
            case 'not-allowed':
                errorMessage += 'Permiso de micrófono denegado';
                break;
            default:
                errorMessage += event.error;
        }
        
        showStatus(errorMessage, true);
    };

    // Add audiostart event handler
    recognition.onaudiostart = function() {
        console.log('Audio capturing started');
        showStatus('Capturando audio...');
    };

    // Gestión adicional para detección de silencios
    recognition.onspeechend = function() {
        console.log("Posible fin del discurso detectado");
        // No detenemos aquí para permitir pausas naturales en el habla
    };

    // Agregar manejo de clic en el icono del micrófono
    micIcon.addEventListener('click', function () {
        if (isRecording) {
            recognition.stop();        } else {
            // Limpiar el campo de entrada al iniciar una nueva grabación
            const userInput = document.getElementById('user-input');
            if (userInput && userInput.value.trim() !== '') {
                // Use custom confirmation dialog if available, otherwise fallback to browser confirm
                if (window.ChatBot && window.ChatBot.showConfirmationDialog) {
                    window.ChatBot.showConfirmationDialog('¿Deseas borrar el texto actual antes de grabar?', {
                        confirmText: 'Sí, borrar',
                        cancelText: 'No, mantener'
                    }).then((confirmed) => {
                        if (confirmed) {
                            userInput.value = '';
                        }
                        recognition.start();
                    });
                } else {
                    // Fallback to browser confirm
                    if (confirm('¿Deseas borrar el texto actual antes de grabar?')) {
                        userInput.value = '';
                    }
                    recognition.start();
                }
            } else {
                recognition.start();
            }
        }
    });

    // Añadir tooltip para mejor UX
    micIcon.title = "Haz clic para iniciar/detener grabación de voz";
});
