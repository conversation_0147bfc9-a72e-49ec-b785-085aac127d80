/* notifications.css - Styles for notification elements */

/* Base notification */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(var(--bg-tertiary-rgb), 0.9);
  color: var(--text-primary);
  padding: 10px 15px;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  z-index: 1000;
  animation: fadeInOut 2s ease-in-out;
  border-left: 4px solid var(--accent-primary);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: var(--transition-theme);
}

/* Light theme notification */
.light-theme .notification {
  background: rgba(var(--bg-tertiary-rgb), 0.8);
  color: var(--text-primary);
  border-left: 4px solid var(--accent-primary);
  box-shadow: var(--card-shadow);
}

/* Notification animation */
@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(-20px); }
  10% { opacity: 1; transform: translateY(0); }
  90% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-20px); }
}

/* Notification container */
#notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
  transition: var(--transition-theme);
}

/* Notification content */
.notification-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--transition-theme);
}

.notification-message {
  color: var(--text-primary);
  font-size: 0.85rem;
  font-weight: 500;
  transition: var(--transition-theme);
}

/* Responsive styles */
@media (max-width: 768px) {
  .notification {
    top: 10px;
    right: 10px;
    padding: 8px 12px;
    font-size: 0.9rem;
  }
}
