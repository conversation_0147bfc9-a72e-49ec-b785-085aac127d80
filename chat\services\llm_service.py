"""
Servicio para operaciones con modelos de lenguaje.
"""
import logging
from typing import Dict, Union, Optional
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from django.http import HttpRequest

from ..llm_providers.factory import get_llm_provider
from ..llm_providers.context import LLMRequestContext

logger = logging.getLogger(__name__)

def query_llm(request_or_context, user_input: str, memory_instance: ConversationBufferWindowMemory,
              request_type: str = "standard_query", image_path: Optional[str] = None) -> Union[str, Dict]:
    """
    Consulta el modelo de lenguaje configurado.

    Args:
        request_or_context: Solicitud HTTP o contexto LLM
        user_input: Entrada del usuario
        memory_instance: Instancia de memoria de conversación
        request_type: Tipo de solicitud ("standard_query", "generate_test", "multimodal_query")
        image_path: Ruta a la imagen para consultas multimodales

    Returns:
        str | dict: Respuesta del modelo como texto o JSON para exámenes
    """
    try:

        # Obtener el proveedor LLM basado en el contexto
        if isinstance(request_or_context, HttpRequest):
            # Si es una solicitud HTTP, usar el contexto de la solicitud
            context = getattr(request_or_context, 'llm_context', None)
            if not context:
                context = LLMRequestContext(request_or_context)
        else:
            # Si ya es un contexto, usarlo directamente
            context = request_or_context if isinstance(request_or_context, LLMRequestContext) else LLMRequestContext()

        # Obtener el proveedor
        provider = get_llm_provider(context)

        # Procesar según el tipo de solicitud
        if request_type == "generate_test":
            # Lógica para generar exámenes
            formatted_input = provider.format_exam_query(user_input)

            # Crear una copia temporal de la memoria para no afectar la conversación principal
            temp_memory = ConversationBufferWindowMemory(k=10, memory_key="chat_history", return_messages=True)

            # Copiar los mensajes de la memoria original a la temporal
            if hasattr(memory_instance, 'chat_memory') and hasattr(memory_instance.chat_memory, 'messages'):
                for message in memory_instance.chat_memory.messages:
                    temp_memory.chat_memory.add_message(message)

            # Consultar el LLM con el prompt de examen
            response_text = provider.query(formatted_input, temp_memory)

            # Actualizar la memoria original con la consulta del usuario
            memory_instance.chat_memory.add_user_message(user_input)
            memory_instance.chat_memory.add_ai_message("He generado un examen interactivo basado en tu solicitud.")

            # Intentar parsear la respuesta como JSON
            import json
            try:
                # Intentar encontrar un objeto JSON en la respuesta
                import re
                json_match = re.search(r'\{[\s\S]*\}', response_text)

                if json_match:
                    json_str = json_match.group(0)
                    exam_data = json.loads(json_str)

                    # Verificar que tenga la estructura esperada
                    if 'title' in exam_data and 'questions' in exam_data and isinstance(exam_data['questions'], list):
                        logger.info(f"Examen JSON válido detectado: {exam_data['title']}")

                        # Devolver el examen en formato interactivo
                        return {
                            "response_type": "interactive_test",
                            "data": exam_data
                        }
                    else:
                        logger.warning(f"JSON detectado pero no tiene estructura de examen: {exam_data}")
                else:
                    logger.warning("No se encontró JSON en la respuesta")
            except Exception as e:
                logger.error(f"Error al parsear JSON del examen: {e}")

            # Si no se pudo parsear como JSON o no tiene la estructura esperada, devolver como texto
            return {
                "response_type": "text",
                "data": response_text
            }

        elif request_type == "multimodal_query" and image_path:
            # Verificar si el proveedor soporta consultas multimodales
            if hasattr(provider, 'query_with_image'):
                response_text = provider.query_with_image(user_input, image_path, memory_instance)
                return {
                    "response_type": "text",
                    "data": response_text
                }
            else:
                # Fallback a Gemini si el proveedor actual no soporta imágenes
                gemini_context = LLMRequestContext(provider_name='gemini')
                gemini_provider = get_llm_provider(gemini_context)

                # Consultar con imagen
                response_text = gemini_provider.query_with_image(user_input, image_path, memory_instance)

                return {
                    "response_type": "text",
                    "data": response_text
                }

        # Consulta estándar
        response = provider.query(user_input, memory_instance)

        # Verificar si la respuesta ya es un diccionario con tipo de respuesta
        if isinstance(response, dict) and 'response_type' in response and 'data' in response:
            return response

        # Si no, asumir que es texto
        return {
            "response_type": "text",
            "data": response
        }
    except Exception as e:
        logger.error(f"Error al consultar el LLM: {e}")
        return {
            "response_type": "text",
            "data": f"Error: No se pudo procesar tu solicitud. {str(e)}"
        }

def get_available_models() -> Dict[str, Dict]:
    """
    Obtiene todos los modelos disponibles agrupados por proveedor.

    Returns:
        Dict: Diccionario con los modelos disponibles agrupados por proveedor.
    """
    from ..llm_providers.factory import PROVIDERS

    # Models to exclude from the model selector (but keep in provider files)
    EXCLUDED_MODELS = {
        'grok': ['grok-2-vision-1212', 'grok-2-image-1212']
    }

    result = {}

    for provider_id, provider_class in PROVIDERS.items():
        # Obtener información del proveedor
        # Replace underscores with spaces and capitalize each word
        provider_name = ' '.join(word.capitalize() for word in provider_id.split('_'))
        provider_info = {
            'name': provider_name,
            'models': []
        }

        # Obtener modelos disponibles si el proveedor implementa el método
        if hasattr(provider_class, 'get_available_models'):
            available_models = provider_class.get_available_models()

            # Convertir el diccionario de modelos a una lista
            for model_id, model_info in available_models.items():
                # Skip excluded models for this provider
                if provider_id in EXCLUDED_MODELS and model_id in EXCLUDED_MODELS[provider_id]:
                    continue

                provider_info['models'].append({
                    'id': model_id,
                    'name': model_id,
                    'description': model_info.get('description', ''),
                    'capabilities': model_info.get('capabilities', []),
                    'max_tokens': model_info.get('max_tokens')
                })

        result[provider_id] = provider_info

    return result

def switch_model(request, provider_name: str, model_name: str) -> Dict:
    """
    Cambia el modelo activo.

    Args:
        request: Solicitud HTTP
        provider_name: Nombre del proveedor
        model_name: Nombre del modelo

    Returns:
        Dict: Información del modelo cambiado
    """
    from ..llm_providers.factory import PROVIDERS

    # Verificar que el proveedor exista
    if provider_name not in PROVIDERS:
        raise ValueError(f"Proveedor {provider_name} no disponible")

    # Crear contexto con el nuevo proveedor y modelo
    context = LLMRequestContext(provider_name=provider_name, model_name=model_name)

    # Guardar en la sesión
    context.save_to_session(request)

    # Obtener información del nuevo modelo
    provider = get_llm_provider(context)
    model_info = provider.get_model_info()

    return model_info
