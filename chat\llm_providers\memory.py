"""
Utilidades para la gestión de memoria de conversación.
"""
import json
import logging
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langchain_core.messages import HumanMessage, AIMessage, BaseMessage

logger = logging.getLogger(__name__)

def serialize_memory(memory_instance: ConversationBufferWindowMemory) -> str:
    """
    Convierte la memoria en un string JSON.

    Args:
        memory_instance: Instancia de ConversationBufferWindowMemory.
    Returns:
        str: Representación JSON de los mensajes en la memoria.
    """
    messages = memory_instance.chat_memory.messages
    serialized_messages = []

    for msg in messages:
        if isinstance(msg, HumanMessage):
            serialized_messages.append({"type": "human", "content": msg.content})
        elif isinstance(msg, AIMessage):
            serialized_messages.append({"type": "ai", "content": msg.content})
        elif isinstance(msg, BaseMessage):
            # Fallback para otros tipos de mensajes
            if hasattr(msg, 'type') and msg.type in ['human', 'ai']:
                serialized_messages.append({"type": msg.type, "content": msg.content})

    return json.dumps(serialized_messages)

def deserialize_memory(serialized_data: str, window_size: int = 10) -> ConversationBufferWindowMemory:
    """
    Reconstruye la memoria desde un string JSON.

    Args:
        serialized_data (str): Datos serializados de la memoria.
        window_size (int): Tamaño de la ventana de memoria.
    Returns:
        ConversationBufferWindowMemory: Instancia de memoria reconstruida o vacía si no hay datos.
    """
    memory_instance = ConversationBufferWindowMemory(
        k=window_size,
        memory_key="chat_history",
        return_messages=True
    )

    if not serialized_data:
        return memory_instance

    try:
        messages_data = json.loads(serialized_data)
        for msg_data in messages_data:
            if "type" in msg_data and "content" in msg_data:
                if msg_data["type"] == "human":
                    memory_instance.chat_memory.add_user_message(msg_data["content"])
                elif msg_data["type"] == "ai":
                    memory_instance.chat_memory.add_ai_message(msg_data["content"])
    except json.JSONDecodeError as e:
        logger.warning(f"Error al deserializar memoria: {e}. Usando memoria vacía.")
    except Exception as e:
        logger.error(f"Error inesperado al deserializar memoria: {e}")

    return memory_instance
