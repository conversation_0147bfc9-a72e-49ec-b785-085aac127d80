/**
 * Module right-sidebar.js - Right sidebar functionality
 *
 * This module handles the functionality for the right sidebar
 * that contains notebook and notes icons.
 */

// Import any required modules
import { initializeNotebook } from './notebook.js';
import { initializeNotes } from './notes.js';

/**
 * Initialize the right sidebar functionality
 */
export function initializeRightSidebar() {
  const notebookBtn = document.getElementById('notebook-btn');
  const notesBtn = document.getElementById('notes-btn');

  // The notebook button click handler is now managed by the notebook module
  // We don't need to add a click handler here as it's handled in notebook.js

  // The notes button click handler is now managed by the notes module
  // We don't need to add a click handler here as it's handled in notes.js
}
