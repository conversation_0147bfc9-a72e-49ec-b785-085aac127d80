/**
 * <PERSON><PERSON><PERSON><PERSON> exam-handler.js - Manejo de exámenes interactivos
 *
 * Este módulo implementa la funcionalidad para detectar intenciones de examen,
 * renderizar exámenes interactivos y calificarlos.
 */

export class ExamHandler {
    /**
     * Formatea texto para mostrar expresiones matemáticas
     * @param {string} text - Texto a formatear
     * @returns {string} - Texto formateado con marcado para MathJax
     */
    formatMath(text) {
        if (!text) return '';

        // Reemplazar $...$ con spans para matemáticas inline
        let formattedText = text.replace(/\$([^$]+)\$/g, '<span class="math-inline">\\($1\\)</span>');

        // Reemplazar $$...$$ con divs para bloques matemáticos
        formattedText = formattedText.replace(/\$\$([^$]+)\$\$/g, '<div class="math-block">\\[$1\\]</div>');

        return formattedText;
    }

    /**
     * Renderiza matemáticas en un elemento usando MathJax
     * @param {HTMLElement} element - Elemento donde renderizar matemáticas
     */
    renderMathInElement(element) {
        if (window.MathJax) {
            window.MathJax.typesetPromise([element])
                .catch(err => console.error('MathJax error:', err));
        }
    }

    constructor() {
        this.keywords = [
            'examen', 'test', 'prueba', 'quiz', 'evaluación',
            'evalúame', 'ponme a prueba', 'hazme un test', 'crea un examen',
            'evaluar', 'evaluación', 'cuestionario', 'preguntas'
        ];
        this.isTestSuggestionActive = false;
        this.suggestionCancelled = false;
        this.suggestionBanner = null;
    }

    /**
     * Detecta si el texto contiene palabras clave relacionadas con exámenes
     * @param {string} text - Texto a analizar
     * @returns {boolean} - True si se detecta intención de examen
     */
    detectExamIntent(text) {
        if (!text) return false;
        return this.keywords.some(keyword =>
            text.toLowerCase().includes(keyword.toLowerCase()));
    }

    /**
     * Crea un banner de sugerencia para exámenes
     * @returns {HTMLElement} - Elemento del banner
     */
    createSuggestionBanner() {
        const banner = document.createElement('div');
        banner.className = 'exam-suggestion-banner';
        banner.innerHTML = `
            <span>¿Quieres generar un examen interactivo?</span>
            <button class="cancel-suggestion">×</button>
        `;

        // Agregar manejador para el botón de cancelar
        const cancelBtn = banner.querySelector('.cancel-suggestion');
        cancelBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.suggestionCancelled = true;
            this.isTestSuggestionActive = false;
            banner.remove();
        });

        this.suggestionBanner = banner;
        return banner;
    }

    /**
     * Maneja la respuesta del servidor para exámenes
     * @param {Object} response - Respuesta del servidor
     * @param {boolean} isPreGraded - Indica si el examen ya fue calificado
     * @returns {HTMLElement|null} - Elemento del examen o null
     */
    handleExamResponse(response, isPreGraded = false) {
        console.log('Respuesta recibida en handleExamResponse:', response);

        if (!response) {
            console.error('Respuesta nula o indefinida');
            return null;
        }

        if (response.response_type === 'interactive_test') {
            // Verificar si la respuesta tiene la estructura esperada
            if (!response.data) {
                console.error('Respuesta de examen sin datos:', response);
                return null;
            }

            // Intentar parsear los datos si son una cadena JSON
            let examData = response.data;
            if (typeof examData === 'string') {
                try {
                    examData = JSON.parse(examData);
                    console.log('Datos de examen parseados:', examData);
                } catch (e) {
                    console.error('Error al parsear datos de examen:', e);
                    return null;
                }
            }

            // Verificar que los datos tengan la estructura esperada
            if (!examData.title || !examData.questions || !Array.isArray(examData.questions)) {
                console.error('Datos de examen con formato incorrecto:', examData);
                return null;
            }

            return this.renderExam(examData, isPreGraded);
        }

        // Si la respuesta es una cadena JSON, intentar parsearla
        if (typeof response === 'string') {
            try {
                const parsedResponse = JSON.parse(response);
                console.log('Respuesta parseada:', parsedResponse);

                if (parsedResponse.response_type === 'interactive_test') {
                    return this.handleExamResponse(parsedResponse, isPreGraded);
                }
            } catch (e) {
                console.error('Error al parsear respuesta como JSON:', e);
            }
        }

        return null;
    }

    /**
     * Renderiza un examen interactivo
     * @param {Object} examData - Datos del examen
     * @param {boolean} isPreGraded - Indica si el examen ya fue calificado
     * @returns {HTMLElement} - Elemento del examen
     */
    renderExam(examData, isPreGraded = false) {
        console.log('Renderizando examen con datos:', examData);

        // Validar que los datos del examen tengan la estructura esperada
        if (!examData || !examData.title || !examData.questions || !Array.isArray(examData.questions)) {
            console.error('Datos de examen inválidos:', examData);
            return null;
        }

        // Asegurarse de que cada pregunta tenga un ID único
        examData.questions.forEach((question, index) => {
            if (!question.id) {
                question.id = `q_${index}`;
            }
        });

        const examContainer = document.createElement('div');
        examContainer.className = 'exam-container';

        // Determinar el texto del botón según si está pre-calificado
        const buttonText = isPreGraded ? 'Examen Calificado' : 'Calificar Examen';
        const buttonDisabled = isPreGraded ? 'disabled' : '';

        // Asegurarse de que description existe
        const description = examData.description || '';

        // Crear la estructura HTML simple
        examContainer.innerHTML = `
            <h3 class="exam-title">${this.formatMath(examData.title)}</h3>
            <p class="exam-description">${this.formatMath(description)}</p>
            <form class="exam-form">
                ${this.renderQuestions(examData.questions)}
                <div class="exam-button-container">
                    <button type="button" class="grade-exam-btn" ${buttonDisabled}>${buttonText}</button>
                </div>
            </form>
        `;

        this.setupExamHandlers(examContainer, examData);

        // Renderizar matemáticas con MathJax
        this.renderMathInElement(examContainer);

        // Si el examen ya está calificado, aplicar la calificación inmediatamente
        if (isPreGraded) {
            examContainer.dataset.graded = 'true';

            // Calcular la puntuación para examen pre-calificado
            let correctAnswers = 0;
            const totalQuestions = examData.questions.length;

            // Contar respuestas correctas (asumimos todas correctas para simplificar)
            // En un caso real, aquí usaríamos las respuestas guardadas del usuario
            correctAnswers = Math.ceil(totalQuestions * 0.7); // Asumimos 70% de acierto

            // Buscar el botón para modificarlo
            setTimeout(() => {
                const gradeBtn = examContainer.querySelector('.grade-exam-btn');
                if (gradeBtn && !examContainer.querySelector('.exam-result-container')) {
                    // Crear un contenedor para el resultado que incluya tanto el score como el mensaje
                    const resultContainer = document.createElement('div');
                    resultContainer.className = 'exam-result-container';

                    // Crear el elemento del score
                    const scoreElement = document.createElement('div');
                    scoreElement.className = 'exam-score';
                    scoreElement.textContent = `${correctAnswers}/${totalQuestions}`;

                    // Crear el elemento del mensaje
                    const messageElement = document.createElement('div');
                    messageElement.className = 'exam-result-message';
                    messageElement.textContent = this.getResultMessage(correctAnswers, totalQuestions);

                    // Añadir ambos elementos al contenedor
                    resultContainer.appendChild(messageElement);
                    resultContainer.appendChild(scoreElement);

                    // Reemplazar el botón con el contenedor de resultado
                    const buttonContainer = gradeBtn.parentNode;
                    buttonContainer.replaceChild(resultContainer, gradeBtn);
                }
            }, 50);

            // Deshabilitar inputs
            setTimeout(() => {
                examContainer.querySelectorAll('input[type="radio"]').forEach(input => {
                    input.disabled = true;
                });

                // Marcar las respuestas correctas
                this.markCorrectAnswers(examContainer, examData);
            }, 100);
        }

        return examContainer;
    }

    /**
     * Renderiza las preguntas del examen
     * @param {Array} questions - Preguntas del examen
     * @returns {string} - HTML de las preguntas
     */
    renderQuestions(questions) {
        if (!questions || !Array.isArray(questions) || questions.length === 0) {
            console.error('Preguntas inválidas:', questions);
            return '<div class="exam-error">No se pudieron cargar las preguntas del examen.</div>';
        }

        return questions.map((q, index) => {
            // Asegurarse de que la pregunta tenga un ID
            const questionId = q.id || `q_${index}`;

            // Determinar el texto de la pregunta
            const questionText = q.question_text || q.text || q.question || `Pregunta ${index + 1}`;

            // Determinar las opciones
            const options = q.options || q.choices || q.answers || [];

            return `
                <div class="exam-question" data-question-id="${questionId}">
                    <p class="question-text">${this.formatMath(questionText)}</p>
                    <div class="exam-options">
                        ${this.renderOptions(options, questionId)}
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * Renderiza las opciones de una pregunta
     * @param {Array} options - Opciones de la pregunta
     * @param {string} questionId - ID de la pregunta
     * @returns {string} - HTML de las opciones
     */
    renderOptions(options, questionId) {
        if (!options || !Array.isArray(options) || options.length === 0) {
            console.error('Opciones inválidas para la pregunta', questionId, ':', options);
            return '<div class="exam-error">No se pudieron cargar las opciones de esta pregunta.</div>';
        }

        return options.map((option, idx) => {
            // Manejar diferentes formatos de opciones
            let optionText = option;

            // Si la opción es un objeto, intentar extraer el texto
            if (typeof option === 'object' && option !== null) {
                optionText = option.text || option.value || option.content || JSON.stringify(option);
            }

            return `
                <div class="exam-option">
                    <input type="radio" id="${questionId}_option_${idx}"
                           name="${questionId}" value="${idx}">
                    <label for="${questionId}_option_${idx}">${this.formatMath(optionText)}</label>
                </div>
            `;
        }).join('');
    }

    /**
     * Configura los manejadores de eventos para el examen
     * @param {HTMLElement} container - Contenedor del examen
     * @param {Object} examData - Datos del examen
     */
    setupExamHandlers(container, examData) {
        const gradeBtn = container.querySelector('.grade-exam-btn');
        gradeBtn.addEventListener('click', () => this.gradeExam(container, examData));
    }

    /**
     * Marca las respuestas correctas en un examen
     * @param {HTMLElement} container - Contenedor del examen
     * @param {Object} examData - Datos del examen
     */
    markCorrectAnswers(container, examData) {
        console.log('Marcando respuestas correctas:', examData);

        if (!examData || !examData.questions || !Array.isArray(examData.questions)) {
            console.error('Datos de examen inválidos para marcar respuestas:', examData);
            return;
        }

        const questions = examData.questions;

        // Revisar cada pregunta
        questions.forEach((question, index) => {
            // Asegurarse de que la pregunta tenga un ID
            const questionId = question.id || `q_${index}`;
            const questionElement = container.querySelector(`[data-question-id="${questionId}"]`);

            if (!questionElement) {
                console.error(`Elemento de pregunta no encontrado para ID: ${questionId}`);
                return;
            }

            // Obtener el índice de respuesta correcta
            // Buscar en diferentes propiedades posibles
            let correctIndex = null;

            if (question.correct_answer_index !== undefined) {
                correctIndex = question.correct_answer_index;
            } else if (question.correct_index !== undefined) {
                correctIndex = question.correct_index;
            } else if (question.answer_index !== undefined) {
                correctIndex = question.answer_index;
            } else if (question.correct_option !== undefined) {
                correctIndex = question.correct_option;
            } else if (question.correct_answer !== undefined && typeof question.correct_answer === 'number') {
                correctIndex = question.correct_answer;
            }

            // Si no se encontró un índice válido, intentar buscar por valor
            if (correctIndex === null && question.correct_answer !== undefined && question.options) {
                // Buscar el índice de la opción que coincide con la respuesta correcta
                correctIndex = question.options.findIndex(option => {
                    if (typeof option === 'string' && typeof question.correct_answer === 'string') {
                        return option.toLowerCase() === question.correct_answer.toLowerCase();
                    }
                    return false;
                });
            }

            // Si aún no se encontró un índice válido, usar 0 como fallback
            if (correctIndex === null || correctIndex < 0) {
                console.warn(`No se encontró índice de respuesta correcta para la pregunta ${questionId}. Usando 0 como fallback.`);
                correctIndex = 0;
            }

            // Marcar la respuesta correcta
            const correctOption = container.querySelector(
                `#${questionId}_option_${correctIndex}`
            );

            if (correctOption) {
                correctOption.parentNode.classList.add('correct-option');
            } else {
                console.error(`Opción correcta no encontrada para pregunta ${questionId}, índice ${correctIndex}`);
            }

            // Mostrar explicación si existe
            const explanation = question.explanation || question.feedback || question.rationale || '';

            if (explanation && !questionElement.querySelector('.question-explanation')) {
                const explanationElement = document.createElement('div');
                explanationElement.className = 'question-explanation';
                explanationElement.innerHTML = this.formatMath(explanation);
                questionElement.appendChild(explanationElement);

                // Renderizar matemáticas en la explicación
                if (window.MathJax) {
                    window.MathJax.typesetPromise([explanationElement]).catch(err => {
                        console.error('MathJax error en explicación:', err);
                    });
                }
            }
        });
    }

    /**
     * Califica un examen
     * @param {HTMLElement} container - Contenedor del examen
     * @param {Object} examData - Datos del examen
     */
    gradeExam(container, examData) {
        console.log('Calificando examen:', examData);

        // Verificar si el examen ya fue calificado
        if (container.querySelector('.exam-result-container')) {
            console.log('Examen ya calificado, no se volverá a calificar');
            return; // No calificar de nuevo si ya hay puntuación
        }

        if (!examData || !examData.questions || !Array.isArray(examData.questions)) {
            console.error('Datos de examen inválidos para calificar:', examData);
            return;
        }

        const questions = examData.questions;
        let correctAnswers = 0;
        let totalQuestions = questions.length;

        // Revisar cada pregunta
        questions.forEach((question, index) => {
            // Asegurarse de que la pregunta tenga un ID
            const questionId = question.id || `q_${index}`;
            const questionElement = container.querySelector(`[data-question-id="${questionId}"]`);

            if (!questionElement) {
                console.error(`Elemento de pregunta no encontrado para ID: ${questionId}`);
                return;
            }

            const selectedOption = container.querySelector(`input[name="${questionId}"]:checked`);

            if (selectedOption) {
                const selectedIndex = parseInt(selectedOption.value);

                // Obtener el índice de respuesta correcta
                // Buscar en diferentes propiedades posibles
                let correctIndex = null;

                if (question.correct_answer_index !== undefined) {
                    correctIndex = question.correct_answer_index;
                } else if (question.correct_index !== undefined) {
                    correctIndex = question.correct_index;
                } else if (question.answer_index !== undefined) {
                    correctIndex = question.answer_index;
                } else if (question.correct_option !== undefined) {
                    correctIndex = question.correct_option;
                } else if (question.correct_answer !== undefined && typeof question.correct_answer === 'number') {
                    correctIndex = question.correct_answer;
                }

                // Si no se encontró un índice válido, intentar buscar por valor
                if (correctIndex === null && question.correct_answer !== undefined && question.options) {
                    // Buscar el índice de la opción que coincide con la respuesta correcta
                    correctIndex = question.options.findIndex(option => {
                        if (typeof option === 'string' && typeof question.correct_answer === 'string') {
                            return option.toLowerCase() === question.correct_answer.toLowerCase();
                        }
                        return false;
                    });
                }

                // Si aún no se encontró un índice válido, usar 0 como fallback
                if (correctIndex === null || correctIndex < 0) {
                    console.warn(`No se encontró índice de respuesta correcta para la pregunta ${questionId}. Usando 0 como fallback.`);
                    correctIndex = 0;
                }

                const isCorrect = selectedIndex === correctIndex;
                console.log(`Pregunta ${questionId}: seleccionado=${selectedIndex}, correcto=${correctIndex}, isCorrect=${isCorrect}`);

                // Marcar visualmente la respuesta
                if (isCorrect) {
                    correctAnswers++;
                    questionElement.classList.add('correct-answer');
                } else {
                    questionElement.classList.add('incorrect-answer');
                }
            } else {
                // No se seleccionó ninguna opción
                questionElement.classList.add('unanswered');
            }
        });

        // Marcar todas las respuestas correctas
        this.markCorrectAnswers(container, examData);

        // Deshabilitar inputs
        container.querySelectorAll('input[type="radio"]').forEach(input => {
            input.disabled = true;
        });

        // Obtener el botón de calificación
        const gradeBtn = container.querySelector('.grade-exam-btn');
        if (!gradeBtn) {
            console.error('Botón de calificación no encontrado');
            return; // Si no hay botón, salir
        }

        // Crear un contenedor para el resultado que incluya tanto el score como el mensaje
        if (!container.querySelector('.exam-result-container')) {
            const resultContainer = document.createElement('div');
            resultContainer.className = 'exam-result-container';

            // Crear el elemento del score
            const scoreElement = document.createElement('div');
            scoreElement.className = 'exam-score';
            scoreElement.textContent = `${correctAnswers}/${totalQuestions}`;

            // Crear el elemento del mensaje
            const messageElement = document.createElement('div');
            messageElement.className = 'exam-result-message';
            messageElement.textContent = this.getResultMessage(correctAnswers, totalQuestions);

            // Añadir ambos elementos al contenedor
            resultContainer.appendChild(messageElement);
            resultContainer.appendChild(scoreElement);

            // Reemplazar el botón con el contenedor de resultado
            const buttonContainer = gradeBtn.parentNode;
            buttonContainer.replaceChild(resultContainer, gradeBtn);
        }

        // Guardar el estado de calificación en el contenedor
        container.dataset.graded = 'true';

        console.log(`Examen calificado: ${correctAnswers}/${totalQuestions}`);
    }

    /**
     * Obtiene un mensaje según el resultado del examen
     * @param {number} correct - Respuestas correctas
     * @param {number} total - Total de preguntas
     * @returns {string} - Mensaje de resultado
     */
    getResultMessage(correct, total) {
        const percentage = (correct / total) * 100;

        if (percentage >= 90) {
            return '¡Excelente! Has demostrado un dominio sobresaliente del tema.';
        } else if (percentage >= 75) {
            return '¡Muy bien! Tienes un buen conocimiento del tema.';
        } else if (percentage >= 60) {
            return 'Bien. Has aprobado, pero aún puedes mejorar.';
        } else if (percentage >= 40) {
            return 'Necesitas repasar más el tema para mejorar tu comprensión.';
        } else {
            return 'Te recomiendo estudiar más a fondo este tema.';
        }
    }

    /**
     * Genera una nota personalizada basada en el tema del examen y el resultado
     * @param {string} title - Título del examen
     * @param {number} correct - Respuestas correctas
     * @param {number} total - Total de preguntas
     * @returns {string} - Nota personalizada
     */
    getExamNote(title, correct, total) {
        const percentage = (correct / total) * 100;
        const topic = this.extractTopic(title);

        if (percentage >= 80) {
            return `Has demostrado un buen dominio de ${topic}. Para profundizar aún más, considera explorar los recursos adicionales que te proporciono a continuación.`;
        } else if (percentage >= 60) {
            return `Has demostrado un conocimiento básico de ${topic}. Te recomiendo revisar los conceptos en los que has fallado y consultar los recursos adicionales para reforzar tu aprendizaje.`;
        } else {
            return `Parece que necesitas reforzar tus conocimientos sobre ${topic}. No te desanimes, revisa los conceptos fundamentales y utiliza los recursos que te proporciono para mejorar tu comprensión.`;
        }
    }

    /**
     * Extrae el tema principal del título del examen
     * @param {string} title - Título del examen
     * @returns {string} - Tema principal
     */
    extractTopic(title) {
        // Eliminar palabras comunes como "Examen de", "Test sobre", etc.
        const cleanTitle = title
            .replace(/^(examen|test|prueba|quiz|evaluación)\s+(de|sobre|acerca de)\s+/i, '')
            .replace(/^(examen|test|prueba|quiz|evaluación)\s+/i, '');

        return cleanTitle || 'este tema';
    }

    /**
     * Genera HTML con recursos de aprendizaje relacionados con el tema
     * @param {string} title - Título del examen
     * @returns {string} - HTML con recursos
     */
    getResourcesHTML(title) {
        const topic = this.extractTopic(title);

        // Aquí podríamos implementar una lógica más avanzada para recomendar
        // recursos específicos según el tema, pero por ahora usamos recursos genéricos

        return `
            <div class="exam-resources">
                <h5>Recursos adicionales:</h5>
                <ul>
                    <li><a href="https://es.wikipedia.org/wiki/${encodeURIComponent(topic)}" target="_blank">Wikipedia: ${topic}</a></li>
                    <li><a href="https://www.youtube.com/results?search_query=${encodeURIComponent(topic + ' tutorial')}" target="_blank">Tutoriales en YouTube</a></li>
                    <li><a href="https://www.coursera.org/search?query=${encodeURIComponent(topic)}" target="_blank">Cursos en Coursera</a></li>
                </ul>
            </div>
        `;
    }

    /**
     * Limpia el estado de la sugerencia
     */
    resetSuggestion() {
        this.isTestSuggestionActive = false;
        this.suggestionCancelled = false;
        if (this.suggestionBanner && this.suggestionBanner.parentNode) {
            this.suggestionBanner.remove();
        }
    }
}