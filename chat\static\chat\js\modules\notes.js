/**
 * Module notes.js - Notes functionality
 *
 * This module handles the notes feature that allows users to create
 * and edit rich text notes.
 */

// Notes class to handle all notes functionality
class Notes {
  constructor() {
    this.notes = [];
    this.activeNoteId = null;
    this.isInitialized = false;
    this.storageKey = 'chat_notes_data';
    this.quill = null;
    this.currentChatContent = null;
    this.confirmationDialog = null;
    this.draggedNote = null;
  }

  /**
   * Initialize the notes functionality
   */
  initialize() {
    if (this.isInitialized) return;

    // Load saved notes from localStorage
    this.loadSavedNotes();

    // Create the notes modal
    this.createNotesModal();

    // Set up notes button click handler
    const notesBtn = document.getElementById('notes-btn');
    if (notesBtn) {
      notesBtn.addEventListener('click', () => this.openNotes());
    }

    this.isInitialized = true;
    console.log('Notes initialized');
  }

  /**
   * Load saved notes from localStorage
   */
  loadSavedNotes() {
    try {
      const savedNotes = localStorage.getItem(this.storageKey);
      if (savedNotes) {
        this.notes = JSON.parse(savedNotes);
        console.log('Loaded notes from localStorage:', this.notes.length);
      }
    } catch (error) {
      console.error('Error loading notes from localStorage:', error);
      this.notes = [];
    }

    // We don't create a default note anymore - the editor will be disabled until the user creates one
  }

  /**
   * Save notes to localStorage
   */
  saveNotes() {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.notes));
    } catch (error) {
      console.error('Error saving notes to localStorage:', error);
      this.showNotification('Error saving notes', 'error');
    }
  }

  /**
   * Create the notes modal
   */
  createNotesModal() {
    // Create modal element
    const modal = document.createElement('div');
    modal.id = 'notes-modal';
    modal.className = 'notes-modal';
    modal.innerHTML = `
      <div class="notes-content">
        <div class="notes-header">
          <h3 style="flex:1; text-align:center; margin-left:0; margin-right:0;">Notes</h3>
          <div class="notes-actions">
            <button class="close-notes"></button>
          </div>
        </div>
        <div class="notes-container">
          <div class="notes-sidebar" id="notes-sidebar">
            <div class="notes-list" id="notes-list">
              <!-- Notes list will be inserted here -->
            </div>
            <div class="sidebar-footer">
              <button class="new-note-btn" title="Create a new note">
                <i class="fa-solid fa-plus"></i>
              </button>
            </div>
          </div>
          <button class="toggle-sidebar-btn" title="Toggle sidebar">
            <i class="fa-solid fa-chevron-left"></i>
          </button>
          <div class="notes-editor-container">
            <div class="notes-toolbar" id="notes-toolbar">
              <!-- Toolbar will be inserted by Quill -->
            </div>
            <div class="notes-editor" id="notes-editor">
              <!-- Editor will be inserted by Quill -->
            </div>
          </div>
          <div class="notes-preview-container" id="notes-preview-container">
            <div class="notes-preview-content" id="notes-preview-content">
              <div class="notes-preview-placeholder">
                <i class="fa-solid fa-comments"></i>
                <p>The current chat conversation will be displayed here for reference while taking notes.</p>
              </div>
            </div>
          </div>
          <button class="toggle-preview-btn" title="Toggle chat preview">
            <i class="fa-solid fa-chevron-right"></i>
          </button>
        </div>
      </div>
    `;

    // Add to DOM
    document.body.appendChild(modal);

    // Set up event listeners
    const closeBtn = modal.querySelector('.close-notes');
    closeBtn.addEventListener('click', () => this.closeNotes());

    // Close when clicking outside content
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.closeNotes();
      }
    });

    // New note button
    const newNoteBtn = modal.querySelector('.new-note-btn');
    newNoteBtn.addEventListener('click', () => this.createNewNote());

    // Toggle sidebar button
    const toggleSidebarBtn = modal.querySelector('.toggle-sidebar-btn');
    toggleSidebarBtn.addEventListener('click', () => {
      const sidebar = modal.querySelector('.notes-sidebar');
      sidebar.classList.toggle('collapsed');
    });

    // Toggle preview button
    const togglePreviewBtn = modal.querySelector('.toggle-preview-btn');
    togglePreviewBtn.addEventListener('click', () => {
      const previewContainer = modal.querySelector('.notes-preview-container');
      previewContainer.classList.toggle('collapsed');
    });

    // Initialize Quill editor
    this.initializeQuillEditor();
  }

  /**
   * Initialize Quill rich text editor
   */
  initializeQuillEditor() {
    // Check if Quill is available
    if (!window.Quill) {
      console.error('Quill is not available. Please include Quill.js in your project.');
      return;
    }

    // Configure Quill toolbar with a more minimal set of options
    const toolbarOptions = [
      // Text formatting
      ['bold', 'italic', 'underline'],

      // Lists
      [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'list': 'check' }],

      // Indentation and alignment
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'align': [] }],

      // Copy button
      ['copy'],

    ];

    // Set up Quill toolbar
    const toolbar = document.getElementById('notes-toolbar');
    toolbar.innerHTML = '';

    // Create toolbar container
    const toolbarContainer = document.createElement('div');
    toolbarContainer.id = 'quill-toolbar';
    toolbarContainer.className = 'floating-toolbar';
    toolbar.appendChild(toolbarContainer);

    // Disable spellcheck on the editor element
    const editorElement = document.getElementById('notes-editor');
    editorElement.setAttribute('spellcheck', 'false');

    // Initialize Quill with toolbar
    this.quill = new Quill('#notes-editor', {
      modules: {
        toolbar: {
          container: toolbarOptions,
          handlers: {
            'copy': () => this.copyNoteContent()
          }
        }
      },
      placeholder: 'Write your notes here...',
      theme: 'snow'
    });

    // Set up change handler to save content
    this.quill.on('text-change', () => {
      if (this.activeNoteId) {
        this.saveActiveNoteContent();
      }
    });

    // Add custom button icon after Quill initialization
    this.addCustomToolbarButtons();

    // Check if we need to disable the editor (no notes)
    this.updateEditorState();

    // Ensure the toolbar is properly positioned when the window is resized
    window.addEventListener('resize', this.adjustToolbarPosition.bind(this));

    // Position the toolbar within the editor container
    const editorContainer = document.querySelector('.notes-editor-container');
    const quillToolbar = document.querySelector('.ql-toolbar.ql-snow');
    const notesToolbar = document.querySelector('.notes-toolbar');

    if (editorContainer && quillToolbar && notesToolbar) {
      // Move the toolbar into the notes-toolbar div for proper positioning
      notesToolbar.appendChild(quillToolbar);
    }

    // Initial adjustment
    setTimeout(() => this.adjustToolbarPosition(), 100);
  }

  /**
   * Adjust the toolbar position based on editor width
   */
  adjustToolbarPosition() {
    const toolbar = document.querySelector('.ql-toolbar.ql-snow');
    if (!toolbar) return;

    // Make sure the toolbar doesn't overflow the editor container
    const editorContainer = document.querySelector('.notes-editor-container');
    if (editorContainer) {
      const containerWidth = editorContainer.offsetWidth;
      const toolbarWidth = toolbar.offsetWidth;

      // If toolbar is wider than container, adjust its width
      if (toolbarWidth > containerWidth - 40) {
        toolbar.style.width = (containerWidth - 40) + 'px';
        toolbar.style.minWidth = 'auto';
      } else {
        // Reset width to fit-content for proper centering
        toolbar.style.width = 'fit-content';
      }

      // Ensure the toolbar is within the editor container
      toolbar.style.maxWidth = (containerWidth - 40) + 'px';

      // Make sure the toolbar is properly centered
      toolbar.style.marginLeft = 'auto';
      toolbar.style.marginRight = 'auto';
    }
  }

  /**
   * Add custom icons to toolbar buttons
   */
  addCustomToolbarButtons() {
    // Find the copy button and replace its content with the word "copy" (small)
    const copyButton = document.querySelector('.ql-copy');
    if (copyButton) {
      copyButton.innerHTML = '<span style="font-size: 0.8em; font-family: inherit;">copy</span>';
      copyButton.title = 'Copy note content';
    }
  }

  /**
   * Copy the current note content to clipboard with formatting preserved
   */
  copyNoteContent() {
    if (!this.quill || !this.activeNoteId) {
      this.showNotification('No note content to copy', 'error');
      return;
    }

    try {
      // Get both HTML and plain text content from Quill
      const htmlContent = this.quill.root.innerHTML;
      const textContent = this.quill.getText();

      // Check if content is empty
      if (!textContent.trim()) {
        this.showNotification('No content to copy', 'error');
        return;
      }

      // Use the modern Clipboard API to write multiple formats
      const clipboardItems = [];

      // Create clipboard item with both HTML and plain text
      const clipboardItem = new ClipboardItem({
        'text/html': new Blob([htmlContent], { type: 'text/html' }),
        'text/plain': new Blob([textContent], { type: 'text/plain' })
      });

      clipboardItems.push(clipboardItem);

      // Write to clipboard
      navigator.clipboard.write(clipboardItems)
        .then(() => {
          this.showNotification('Note content copied with formatting');

          // Visual feedback on the copy button
          const copyButton = document.querySelector('.ql-copy');
          if (copyButton) {
            copyButton.innerHTML = '<span style="font-size: 0.8em; font-family: inherit;">copied</span>';
            setTimeout(() => {
              copyButton.innerHTML = '<span style="font-size: 0.8em; font-family: inherit;">copy</span>';
            }, 2000);
          }
        })
        .catch(err => {
          console.warn('Failed to copy with formatting, falling back to plain text:', err);
          // Fallback to plain text only
          this.copyPlainText(textContent);
        });

    } catch (err) {
      console.error('Error in copyNoteContent:', err);
      // Fallback to simple text copy
      const textContent = this.quill.getText();
      this.copyPlainText(textContent);
    }
  }

  /**
   * Fallback method to copy plain text only
   * @param {string} textContent - The plain text content to copy
   */
  copyPlainText(textContent) {
    navigator.clipboard.writeText(textContent)
      .then(() => {
        this.showNotification('Note content copied (plain text)');

        // Visual feedback on the copy button
        const copyButton = document.querySelector('.ql-copy');
        if (copyButton) {
          copyButton.innerHTML = '<span style="font-size: 0.8em; font-family: inherit;">copied</span>';
          setTimeout(() => {
            copyButton.innerHTML = '<span style="font-size: 0.8em; font-family: inherit;">copy</span>';
          }, 2000);
        }
      })
      .catch(err => {
        console.error('Failed to copy note content:', err);
        this.showNotification('Failed to copy note content', 'error');
      });
  }

  /**
   * Update the editor state based on whether there are notes
   */
  updateEditorState() {
    const editorContainer = document.querySelector('.notes-editor-container');
    if (!editorContainer) return;

    if (this.notes.length === 0) {
      // Disable the editor
      editorContainer.classList.add('disabled');
      if (this.quill) {
        this.quill.disable();
        console.log('Editor disabled - no notes available');

        // Clear any existing content
        this.quill.setContents([]);

        // Adjust toolbar opacity for disabled state
        const toolbar = document.querySelector('.ql-toolbar.ql-snow');
        if (toolbar) {
          toolbar.style.opacity = '0.5';
          toolbar.style.pointerEvents = 'none';
        }
      }
    } else {
      // Enable the editor
      editorContainer.classList.remove('disabled');
      if (this.quill) {
        this.quill.enable();
        console.log('Editor enabled - notes available');

        // Set default font size
        setTimeout(() => {
          this.quill.format('size', 'normal');
        }, 50);

        // Restore toolbar
        const toolbar = document.querySelector('.ql-toolbar.ql-snow');
        if (toolbar) {
          toolbar.style.opacity = '';
          toolbar.style.pointerEvents = '';
        }
      }
    }

    // Adjust toolbar position
    setTimeout(() => this.adjustToolbarPosition(), 100);
  }

  /**
   * Open the notes modal and display notes
   */
  openNotes() {
    const modal = document.getElementById('notes-modal');
    if (!modal) return;

    // Render notes list
    this.renderNotesList();

    // Update editor state based on whether there are notes
    this.updateEditorState();

    // Load the first note or active note if there are any
    if (this.notes.length > 0) {
      const noteToLoad = this.activeNoteId ?
        this.notes.find(note => note.id === this.activeNoteId) :
        this.notes[0];

      if (noteToLoad) {
        this.loadNote(noteToLoad.id);
      }
    }

    // Load current chat content for preview
    this.loadCurrentChatContent();

    // Show modal
    modal.classList.add('active');

    // Adjust toolbar position after modal is visible
    setTimeout(() => {
      this.adjustToolbarPosition();
    }, 300);
  }

  /**
   * Load the current chat content for preview
   */
  loadCurrentChatContent() {
    const previewContent = document.getElementById('notes-preview-content');
    if (!previewContent) return;

    // Get the chat content from the DOM
    const chatBoxContent = document.getElementById('chat-box-content');
    if (!chatBoxContent) {
      previewContent.innerHTML = `
        <div class="notes-preview-placeholder">
          <i class="fa-solid fa-comments"></i>
          <p>No chat content available.</p>
        </div>
      `;
      return;
    }

    // Clone the chat content to avoid modifying the original
    const chatClone = chatBoxContent.cloneNode(true);

    // Remove any unnecessary elements from the clone
    const botMessages = chatClone.querySelectorAll('.bot-message');
    const userMessages = chatClone.querySelectorAll('.user-message');

    // Remove footers from bot messages
    botMessages.forEach(message => {
      const footer = message.querySelector('.message-footer');
      if (footer) {
        footer.remove();
      }
    });

    if (botMessages.length === 0 && userMessages.length === 0) {
      previewContent.innerHTML = `
        <div class="notes-preview-placeholder">
          <i class="fa-solid fa-comments"></i>
          <p>Start a conversation to see the chat content here.</p>
        </div>
      `;
      return;
    }

    // Clear the preview content
    previewContent.innerHTML = '';

    // Add the cloned content to the preview
    previewContent.appendChild(chatClone);

    // Store the current chat content
    this.currentChatContent = chatClone;
  }

  /**
   * Close the notes modal
   */
  closeNotes() {
    const modal = document.getElementById('notes-modal');
    if (modal) {
      // Save current note before closing
      this.saveActiveNoteContent();

      // Remove any confirmation dialog
      this.removeConfirmationDialog();

      modal.classList.remove('active');
    }
  }

  /**
   * Render the list of notes in the sidebar
   */
  renderNotesList() {
    const notesList = document.getElementById('notes-list');
    if (!notesList) return;

    // Clear existing content
    notesList.innerHTML = '';

    // Sort notes by last modified
    const sortedNotes = [...this.notes].sort((a, b) => {
      return new Date(b.lastModified) - new Date(a.lastModified);
    });

    // Add each note to the list
    sortedNotes.forEach(note => {
      const noteItem = document.createElement('div');
      noteItem.className = 'note-item';
      // Pinned functionality removed
      noteItem.setAttribute('data-note-id', note.id);
      if (note.id === this.activeNoteId) {
        noteItem.classList.add('active');
      }      noteItem.innerHTML = `
        <div class="note-title">${note.title}</div>
        <div class="note-actions">
          <button class="note-rename-btn" title="Rename">
            <i class='bx bx-edit-alt'></i>
          </button>
          <button class="note-delete-btn" title="Delete">
            <i class="fa-solid fa-delete-left"></i>
          </button>
        </div>
      `;

      // Add click handler to load note
      noteItem.addEventListener('click', (e) => {
        // Only load if not clicking on buttons or drag handle
        if (!e.target.closest('.note-actions') && !e.target.closest('.note-drag-handle')) {
          this.loadNote(note.id);
        }
      });

      // Pin button handler removed

      // Add rename button handler
      const renameBtn = noteItem.querySelector('.note-rename-btn');
      renameBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.showRenameNoteDialog(note.id);
      });

      // Add delete button handler
      const deleteBtn = noteItem.querySelector('.note-delete-btn');
      deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        e.preventDefault();
        this.showDeleteNoteDialog(note.id);
      });      notesList.appendChild(noteItem);
    });
  }

  // Pin functionality removed

  /**
   * Load a note into the editor
   * @param {string} noteId - ID of the note to load
   */
  loadNote(noteId) {
    // Save current note before loading another
    this.saveActiveNoteContent();

    // Find the note
    const note = this.notes.find(n => n.id === noteId);
    if (!note) return;

    // Set as active note
    this.activeNoteId = noteId;

    // Load content into editor
    if (this.quill) {
      this.quill.setContents(note.content);

      // Make sure the editor is enabled
      this.quill.enable();

      // Set default font size if not already set
      // This ensures consistent font size for all notes
      if (!this.quill.getFormat().size) {
        this.quill.format('size', 'normal');
      }

      // Focus the editor
      this.quill.focus();
    }

    // Update UI to show active note
    this.renderNotesList();
  }

  /**
   * Save the content of the active note
   */
  saveActiveNoteContent() {
    if (!this.activeNoteId || !this.quill) return;

    // Find the active note
    const noteIndex = this.notes.findIndex(n => n.id === this.activeNoteId);
    if (noteIndex === -1) return;

    // Update the note content
    this.notes[noteIndex].content = this.quill.getContents();
    this.notes[noteIndex].lastModified = new Date().toISOString();

    // Save to localStorage
    this.saveNotes();
  }

  /**
   * Create a new note
   * @param {string} title - Optional title for the new note
   */
  createNewNote(title = 'Untitled Note') {
    // Save current note before creating a new one
    this.saveActiveNoteContent();

    // Create new note object
    const newNote = {
      id: 'note-' + Date.now().toString(),
      title: title,
      content: null,
      created: new Date().toISOString(),
      lastModified: new Date().toISOString()
    };

    // Add to notes array
    this.notes.push(newNote);

    // Save to localStorage
    this.saveNotes();

    // Set as active note
    this.activeNoteId = newNote.id;

    // Update editor state to ensure it's enabled
    this.updateEditorState();

    // Update UI
    this.renderNotesList();

    // Clear editor and set focus
    if (this.quill) {
      this.quill.setContents([]);
      this.quill.enable();

      // Remove welcome state class if it exists
      const editorElement = document.querySelector('.notes-editor .ql-editor');
      if (editorElement) {
        editorElement.classList.remove('welcome-state');
      }

      // Set default font size for new note
      this.quill.format('size', 'normal');

      // Focus the editor after a short delay to ensure the UI is ready
      setTimeout(() => {
        this.quill.focus();
      }, 100);
    }

    // Show notification
    this.showNotification('New note created');
  }

  /**
   * Show dialog to rename a note
   * @param {string} noteId - ID of the note to rename
   */
  showRenameNoteDialog(noteId) {
    // Find the note
    const note = this.notes.find(n => n.id === noteId);
    if (!note) return;

    // Create an inline rename input
    const noteItem = document.querySelector(`.note-item[data-note-id="${noteId}"]`);
    if (!noteItem) return;

    const titleElement = noteItem.querySelector('.note-title');
    if (!titleElement) return;

    // Replace the title with an input field
    const currentTitle = note.title;
    titleElement.innerHTML = `
      <input type="text" class="note-rename-input" value="${currentTitle}" maxlength="30">
    `;

    const inputField = titleElement.querySelector('.note-rename-input');
    inputField.focus();
    inputField.select();

    // Handle input field events
    const handleRename = () => {
      const newTitle = inputField.value.trim() || 'Untitled Note';

      // Update note title
      note.title = newTitle;
      note.lastModified = new Date().toISOString();

      // Save to localStorage
      this.saveNotes();

      // Update UI
      this.renderNotesList();

      // Show notification
      this.showNotification('Note renamed');
    };

    inputField.addEventListener('blur', handleRename);
    inputField.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleRename();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        this.renderNotesList(); // Cancel and revert
      }
    });
  }
  /**
   * Show dialog to confirm note deletion
   * @param {string} noteId - ID of the note to delete
   */
  showDeleteNoteDialog(noteId) {
    // Find the note
    const note = this.notes.find(n => n.id === noteId);
    if (!note) return;

    // Remove any existing confirmation dialog
    this.removeConfirmationDialog();

    // Create confirmation dialog
    const dialog = document.createElement('div');
    dialog.className = 'notes-confirmation-dialog';
    dialog.innerHTML = `
      <div class="notes-confirmation-content">
        <h4>Delete Note</h4>
        <p>Are you sure you want to delete "${note.title}"?</p>
        <div class="notes-confirmation-actions">
          <button class="cancel-btn">Cancel</button>
          <button class="delete-btn">Delete</button>
        </div>
      </div>
    `;

    // Add to DOM
    document.body.appendChild(dialog);
    this.confirmationDialog = dialog;

    // Set up event listeners
    const cancelBtn = dialog.querySelector('.cancel-btn');
    const deleteBtn = dialog.querySelector('.delete-btn');

    const closeDialog = () => {
      dialog.classList.remove('active');
      setTimeout(() => {
        if (document.body.contains(dialog)) {
          document.body.removeChild(dialog);
        }
        this.confirmationDialog = null;
      }, 300);
    };

    cancelBtn.addEventListener('click', closeDialog);

    deleteBtn.addEventListener('click', () => {
      // Delete the note
      this.notes = this.notes.filter(n => n.id !== noteId);

      // If we deleted the active note, clear the editor
      if (this.activeNoteId === noteId) {
        this.activeNoteId = this.notes.length > 0 ? this.notes[0].id : null;
        if (this.activeNoteId) {
          this.loadNote(this.activeNoteId);
        } else {
          if (this.quill) {
            this.quill.setContents([]);
            // Make sure the editor is disabled if no notes
            this.quill.disable();
          }
        }
      }

      // Save to localStorage
      this.saveNotes();

      // Update UI
      this.renderNotesList();

      // Update editor state (enable/disable based on whether there are notes)
      this.updateEditorState();

      // Show notification
      if (this.notes.length === 0) {
        this.showNotification('All notes deleted. Create a new note to start editing.');

        // Add a welcome message to the disabled editor
        if (this.quill) {
          this.quill.setContents([
            { insert: 'Create a new note to start editing.\n' }
          ]);
        }
      } else {
        this.showNotification('Note deleted');
      }

      // Close dialog
      closeDialog();
    });

    // Close when clicking outside content
    dialog.addEventListener('click', (e) => {
      if (e.target === dialog) {
        closeDialog();
      }
    });

    // Show dialog with animation
    setTimeout(() => {
      dialog.classList.add('active');
    }, 10);
  }

  /**
   * Remove the confirmation dialog
   */
  removeConfirmationDialog() {
    if (this.confirmationDialog) {
      document.body.removeChild(this.confirmationDialog);
      this.confirmationDialog = null;
    }
  }
  /**
   * Show a notification message
   * @param {string} message - Message to display
   * @param {string} type - Type of notification (default: 'info')
   */
  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1500;
      background-color: var(--bg-tertiary);
      color: var(--text-primary);
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border: 1px solid var(--border-color);
      font-size: 0.9rem;
      font-weight: 500;
      max-width: 300px;
      opacity: 0;
      transform: translateY(-10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10px);
    `;
    
    // Set type-specific colors
    if (type === 'error') {
      notification.style.backgroundColor = 'rgba(var(--danger-color-rgb), 0.1)';
      notification.style.borderColor = 'var(--danger-color)';
      notification.style.color = 'var(--danger-color)';
    } else if (type === 'success') {
      notification.style.backgroundColor = 'rgba(var(--success-color-rgb), 0.1)';
      notification.style.borderColor = 'var(--success-color)';
      notification.style.color = 'var(--success-color)';
    }
    
    notification.textContent = message;

    // Add to DOM
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateY(0)';
    }, 10);

    // Remove after delay
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateY(-10px)';
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }
}

// Create and export notes instance
const notes = new Notes();

// Make notes available globally
window.notes = notes;

/**
 * Initialize the notes functionality
 */
export function initializeNotes() {
  // Load Quill.js dynamically if not already loaded
  if (!window.Quill) {
    // Load Quill CSS
    const quillCss = document.createElement('link');
    quillCss.rel = 'stylesheet';
    quillCss.href = 'https://cdn.quilljs.com/1.3.6/quill.snow.css';
    document.head.appendChild(quillCss);

    // Load Quill JS
    const quillScript = document.createElement('script');
    quillScript.src = 'https://cdn.quilljs.com/1.3.6/quill.min.js';
    quillScript.onload = () => {
      notes.initialize();
    };
    document.head.appendChild(quillScript);
  } else {
    notes.initialize();
  }
}

