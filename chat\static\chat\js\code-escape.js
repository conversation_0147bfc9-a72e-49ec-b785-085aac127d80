/**
 * Script específico para manejar el escape de código HTML en bloques de código
 * Este script se ejecuta después de que se carga la página y se encarga de
 * escapar el HTML en los bloques de código que contienen etiquetas HTML
 */

document.addEventListener('DOMContentLoaded', function() {
  // Función para escapar HTML
  function escapeHtml(text) {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  // Función para procesar bloques de código
  function processCodeBlocks() {
    document.querySelectorAll('pre code').forEach(function(codeElement) {
      const content = codeElement.textContent;
      
      // Detectar si el código contiene etiquetas HTML o códigos de color hex
      const hasHtmlTags = /<[a-z][\s\S]*>/i.test(content);
      const hasSpanTags = content.includes('<span');
      const hasColorHexCodes = (
        content.includes('#569CD6') || // Azul
        content.includes('#C586C0') || // Morado
        content.includes('#4EC9B0') || // Verde azulado
        content.includes('#CE9178') || // Naranja
        content.includes('#B5CEA8') || // Verde claro
        content.includes('#DCDCAA')    // Amarillo
      );
      
      // Si el código contiene etiquetas HTML con estilos de color, escapar todo el HTML
      if (hasSpanTags || (hasHtmlTags && hasColorHexCodes)) {
        // Escapar el HTML
        const escapedContent = escapeHtml(content);
        codeElement.innerHTML = escapedContent;
        
        // Forzar JavaScript si contiene códigos de color hex
        if (hasColorHexCodes) {
          codeElement.className = 'language-javascript';
        }
      }
    });
  }

  // Procesar bloques de código al cargar la página
  processCodeBlocks();
  
  // Configurar un MutationObserver para procesar nuevos bloques de código
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Procesar bloques de código en el nuevo contenido
        setTimeout(processCodeBlocks, 100);
      }
    });
  });
  
  // Observar cambios en el chat-box-content
  const chatBoxContent = document.getElementById('chat-box-content');
  if (chatBoxContent) {
    observer.observe(chatBoxContent, { childList: true, subtree: true });
  }
  
  // Procesar bloques de código periódicamente
  setInterval(processCodeBlocks, 2000);
});
