/**
 * <PERSON><PERSON><PERSON><PERSON> chat-nav.js - Navegación del chat
 *
 * Este módulo proporciona funcionalidad para navegar rápidamente
 * entre el inicio y el final del chat.
 */

/**
 * Inicializa los botones de navegación del chat
 */
export function initializeChatNavigation() {
  const goToTopBtn = document.getElementById('go-to-top-btn');
  const goToBottomBtn = document.getElementById('go-to-bottom-btn');
  const chatNavButtons = document.querySelector('.chat-nav-buttons');

  if (!goToTopBtn || !goToBottomBtn || !chatNavButtons) return;

  // Configurar evento para ir al inicio del chat
  goToTopBtn.addEventListener('click', () => {
    scrollToFirstMessage();
  });

  // Configurar evento para ir al final del chat
  goToBottomBtn.addEventListener('click', () => {
    scrollToLastMessage();
  });

  // Observar cambios en el DOM para mostrar/ocultar los botones
  const observer = new MutationObserver(() => {
    updateButtonsVisibility(chatNavButtons);
  });

  // Iniciar observación en el contenedor de mensajes
  const chatBoxContent = document.getElementById('chat-box-content');
  if (chatBoxContent) {
    observer.observe(chatBoxContent, {
      childList: true,
      subtree: true
    });
  }

  // Verificar inicialmente si hay mensajes
  updateButtonsVisibility(chatNavButtons);
}

/**
 * Hace scroll al inicio de la página
 */
function scrollToFirstMessage() {
  // Ir al inicio de la página
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
}

/**
 * Hace scroll al final de la página
 */
function scrollToLastMessage() {
  try {
    // Get the last message for more precise scrolling
    const lastMessage = document.querySelector('.chat-message:last-child');
    const chatBox = document.querySelector('.chat-box');

    if (lastMessage && chatBox) {
      // Calculate precise position accounting for bottom padding
      const chatBoxPaddingBottom = parseInt(getComputedStyle(chatBox).paddingBottom) || 120;
      const targetPosition = lastMessage.offsetTop + lastMessage.offsetHeight - window.innerHeight + chatBoxPaddingBottom - 20;

      window.scrollTo({
        top: Math.max(0, targetPosition),
        behavior: 'smooth'
      });
    } else {
      // Fallback: scroll to document height minus buffer to prevent extra space
      const documentHeight = document.documentElement.scrollHeight;
      const windowHeight = window.innerHeight;
      const targetPosition = documentHeight - windowHeight;

      window.scrollTo({
        top: Math.max(0, targetPosition),
        behavior: 'smooth'
      });
    }
  } catch (error) {
    console.error('Error scrolling to last message:', error);
    // Simple fallback
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: 'smooth'
    });
  }
}

/**
 * Actualiza la visibilidad de los botones de navegación
 * @param {HTMLElement} buttonsContainer - Contenedor de los botones
 */
function updateButtonsVisibility(buttonsContainer) {
  const messages = document.querySelectorAll('.chat-message');

  // Mostrar los botones solo si hay mensajes
  if (messages.length > 0) {
    buttonsContainer.classList.add('visible');
  } else {
    buttonsContainer.classList.remove('visible');
  }
}
