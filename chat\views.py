"""
Vistas para la aplicación de chat.
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt, ensure_csrf_cookie
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth import login, authenticate, logout
from django.contrib import messages
import json
import os
import logging
from django.conf import settings
from .models import Chat, Message, Memory, SegmentChat, SegmentMessage, SegmentMemory
from .llm import serialize_memory, deserialize_memory
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langchain.schema import HumanMessage, AIMessage

logger = logging.getLogger(__name__)

@login_required
@require_http_methods(["DELETE"])
def delete_chat(request, chat_id):
    """
    Elimina un chat del usuario.
    """
    try:
        chat = Chat.objects.get(chat_id=chat_id, user=request.user)
        chat.delete()
        return JsonResponse({"status": "success", "message": "Chat eliminado correctamente"})
    except Exception as e:
        return JsonResponse({"status": "error", "message": str(e)}, status=500)

@login_required
@require_http_methods(["DELETE"])
def delete_all_chats(request):
    """
    Elimina todos los chats del usuario.
    """
    try:
        # Obtener todos los chats del usuario
        user_chats = Chat.objects.filter(user=request.user)
        chat_count = user_chats.count()

        # Eliminar todos los chats (esto también eliminará mensajes y memorias por CASCADE)
        user_chats.delete()

        return JsonResponse({
            "status": "success",
            "message": f"Se eliminaron {chat_count} chats correctamente",
            "deleted_count": chat_count
        })
    except Exception as e:
        logger.error(f"Error eliminando todos los chats del usuario {request.user.id}: {str(e)}")
        return JsonResponse({"status": "error", "message": str(e)}, status=500)

@login_required
@ensure_csrf_cookie
def chat_view(request):
    # Obtener chats del usuario
    user_chats = Chat.objects.filter(user=request.user).order_by('-created_at')

    # Inicializar la sesión con el proveedor y modelo predeterminados si no existen
    if 'active_llm_provider' not in request.session:
        request.session['active_llm_provider'] = settings.ACTIVE_LLM_PROVIDER

    # Inicializar el contexto LLM para esta solicitud
    from .llm_providers.context import LLMRequestContext
    context = LLMRequestContext(request)

    # Si no hay modelo en la sesión, obtener información del modelo actual
    if 'active_model' not in request.session:
        # Obtener el proveedor y la información del modelo
        from .llm_providers.factory import get_llm_provider
        provider = get_llm_provider(context)
        model_info = provider.get_model_info()

        # Guardar en la sesión
        request.session['active_model'] = model_info['name']

        # Si es Gemini, guardar el modelo específico
        if context.provider_name == 'gemini':
            request.session['active_gemini_model'] = settings.ACTIVE_GEMINI_MODEL

    # Usar el modelo de la sesión
    model_name = request.session.get('active_model')
    provider_name = request.session.get('active_llm_provider')

    # Get username for display
    username = request.user.username

    logger.info(f"Vista de chat cargada con proveedor: {provider_name}, modelo: {model_name}")

    return render(request, "chat/chat.html", {
        'model_name': model_name,
        'active_provider': provider_name,
        'user_chats': user_chats,
        'username': username
    })

@method_decorator(csrf_exempt, name='dispatch')
class ChatAPIView(View):
    def get_or_create_chat(self, request, chat_id):
        if not chat_id:
            chat = Chat.objects.create(user=request.user, title="New Chat")
            memory_instance = self.initialize_memory()
        else:
            chat = get_object_or_404(Chat, chat_id=chat_id, user=request.user)
            memory_obj = chat.memories.last()
            serialized_memory = memory_obj.memory_data if memory_obj else ""
            memory_instance = deserialize_memory(serialized_memory)
        return chat, memory_instance

    def initialize_memory(self):
        """Crea una nueva instancia de memoria vacía."""
        return ConversationBufferWindowMemory(k=10, memory_key="chat_history", return_messages=True)

    def post(self, request):
        try:
            data = json.loads(request.body)
            message = data.get('message', '')
            chat_id = data.get('chat_id')
            request_type = data.get('request_type', 'standard_query')

            # Obtener o crear chat
            chat, memory_instance = self.get_or_create_chat(request, chat_id)

            # Usar el servicio LLM para consultar el modelo
            from .services.llm_service import query_llm
            response = query_llm(request, message, memory_instance, request_type)

            # Guardar mensaje y memoria
            user_message = Message.objects.create(chat=chat, sender='user', content=message)
            bot_message = Message.objects.create(chat=chat, sender='bot',
                                 content=json.dumps(response) if isinstance(response, dict) else response)

            serialized_memory = serialize_memory(memory_instance)
            Memory.objects.create(chat=chat, memory_data=serialized_memory)

            if not chat_id:
                # Get the count of user's chats to create a sequential number
                chat_count = Chat.objects.filter(user=request.user).count()
                # Format: "Chat 1" (where 1 is the sequential number)
                title = f"Chat {chat_count}"
                chat.title = title
                chat.save()

            return JsonResponse({
                "response": response,
                "chat_id": str(chat.chat_id),
                "title": chat.title,
                "user_message_id": user_message.id,
                "bot_message_id": bot_message.id
            })
        except Exception as e:
            logger.error(f"Error en ChatAPIView: {e}")
            return JsonResponse({"error": str(e)}, status=500)

@login_required
def load_chat_messages(request, chat_id):
    try:
        chat = Chat.objects.get(chat_id=chat_id, user=request.user)
        messages = chat.messages.all().values('id', 'sender', 'content', 'timestamp')
        return JsonResponse({"messages": list(messages)})
    except Chat.DoesNotExist:
        return JsonResponse({"error": "Chat no encontrado"}, status=404)

@login_required
@require_http_methods(["POST"])
def reset_chat_memory(request, chat_id):
    chat = get_object_or_404(Chat, chat_id=chat_id, user=request.user)
    chat.memories.all().delete()
    return JsonResponse({"status": "success", "message": "Memoria reiniciada"})

@login_required
@require_http_methods(["POST"])
def update_chat_title(request, chat_id):
    try:
        data = json.loads(request.body)
        new_title = data.get('title')

        if not new_title:
            return JsonResponse({"status": "error", "message": "Título no proporcionado"}, status=400)

        chat = get_object_or_404(Chat, chat_id=chat_id, user=request.user)
        chat.title = new_title
        chat.save()

        return JsonResponse({"status": "success", "message": "Título actualizado correctamente"})
    except Exception as e:
        return JsonResponse({"status": "error", "message": str(e)}, status=500)

@login_required
def get_available_models(request):
    """
    Obtiene la lista de modelos disponibles.
    """
    try:
        # Usar el servicio para obtener los modelos disponibles
        from .services.llm_service import get_available_models
        models = get_available_models()

        # Usar el proveedor y modelo de la sesión
        active_provider = request.session.get('active_llm_provider', settings.ACTIVE_LLM_PROVIDER)
        active_model = request.session.get('active_model')

        return JsonResponse({
            'status': 'success',
            'models': models,
            'active_provider': active_provider,
            'active_model': active_model
        })
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

@login_required
@require_http_methods(["POST"])
def switch_model(request):
    """
    Cambia el modelo activo.
    """
    try:
        data = json.loads(request.body)
        provider = data.get('provider')
        model = data.get('model')

        # Usar el servicio para cambiar el modelo
        from .services.llm_service import switch_model as service_switch_model

        # Cambiar el modelo y obtener información
        model_info = service_switch_model(request, provider, model)

        # Registrar información para depuración
        logger.info(f"Modelo cambiado a: {provider} / {model}")
        logger.info(f"Información del modelo: {model_info}")

        return JsonResponse({
            'status': 'success',
            'message': f'Modelo cambiado a {model_info["name"]}',
            'model_info': model_info
        })
    except ValueError as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class MultimodalChatAPIView(View):
    def get_or_create_chat(self, request, chat_id):
        if not chat_id:
            chat = Chat.objects.create(user=request.user, title="New Chat")
            memory_instance = self.initialize_memory()
        else:
            chat = get_object_or_404(Chat, chat_id=chat_id, user=request.user)
            memory_obj = chat.memories.last()
            serialized_memory = memory_obj.memory_data if memory_obj else ""
            memory_instance = deserialize_memory(serialized_memory)
        return chat, memory_instance

    def initialize_memory(self):
        """Crea una nueva instancia de memoria vacía."""
        return ConversationBufferWindowMemory(k=10, memory_key="chat_history", return_messages=True)

    def post(self, request):
        # No necesitamos declarar global llm_provider aquí porque no usamos la variable global
        # Solo usamos una instancia local llamada gemini_provider

        try:
            # Obtener datos del formulario
            message = request.POST.get('message', '')
            chat_id = request.POST.get('chat_id')

            # Verificar si hay un archivo adjunto
            if 'file' not in request.FILES:
                return JsonResponse({"error": "No se proporcionó ningún archivo"}, status=400)

            # Obtener el archivo
            uploaded_file = request.FILES['file']

            # Verificar tipo de archivo (solo imágenes)
            if not uploaded_file.content_type.startswith('image/'):
                return JsonResponse({"error": "El archivo debe ser una imagen"}, status=400)

            # Guardar temporalmente el archivo
            import os

            # Crear directorio temporal si no existe
            temp_dir = os.path.join(settings.BASE_DIR, 'temp_uploads')
            os.makedirs(temp_dir, exist_ok=True)

            # Guardar archivo con nombre único
            file_path = os.path.join(temp_dir, uploaded_file.name)
            with open(file_path, 'wb+') as destination:
                for chunk in uploaded_file.chunks():
                    destination.write(chunk)

            # Obtener o crear chat
            chat, memory_instance = self.get_or_create_chat(request, chat_id)

            # Usar el servicio LLM para consultar el modelo con la imagen
            from .services.llm_service import query_llm
            response = query_llm(request, message, memory_instance, "multimodal_query", file_path)

            # Guardar mensaje y memoria
            user_message = Message.objects.create(chat=chat, sender='user', content=f"{message} [Imagen adjunta]")
            bot_message = Message.objects.create(chat=chat, sender='bot',
                                 content=json.dumps(response) if isinstance(response, dict) else response)

            serialized_memory = serialize_memory(memory_instance)
            Memory.objects.create(chat=chat, memory_data=serialized_memory)

            # Eliminar el archivo temporal
            try:
                os.remove(file_path)
            except Exception as e:
                logger.warning(f"No se pudo eliminar el archivo temporal: {e}")

            if not chat_id:
                # Crear título para el chat nuevo
                chat_count = Chat.objects.filter(user=request.user).count()
                title = f"Chat {chat_count}"
                chat.title = title
                chat.save()

            return JsonResponse({
                "response": response,
                "chat_id": str(chat.chat_id),
                "title": chat.title,
                "user_message_id": user_message.id,
                "bot_message_id": bot_message.id
            })
        except Exception as e:
            logger.error(f"Error en MultimodalChatAPIView: {e}")
            return JsonResponse({"error": str(e)}, status=500)

@login_required
@require_http_methods(["DELETE"])
def delete_message(request, message_id):
    """
    Elimina un mensaje específico y, si es un mensaje del bot, también elimina el mensaje del usuario que lo precedió.
    Actualiza la memoria de langchain para reflejar los cambios.
    """
    try:
        # Obtener el mensaje asegurando que pertenece al usuario actual
        message = get_object_or_404(Message, id=message_id, chat__user=request.user)
        chat = message.chat

        # Guardar información del mensaje para actualizar la memoria
        is_bot_message = message.sender == 'bot'
        message_content = message.content
        user_message_to_delete = None
        user_message_content = None

        # Si es un mensaje del bot, buscar el mensaje del usuario que lo precedió
        if is_bot_message:
            # Obtener todos los mensajes del chat ordenados por timestamp
            chat_messages = chat.messages.all().order_by('timestamp')

            # Crear una lista de mensajes para encontrar el par usuario-bot
            message_list = list(chat_messages)
            message_index = None

            # Encontrar el índice del mensaje actual en la lista
            for i, chat_msg in enumerate(message_list):
                if chat_msg.id == message.id:
                    message_index = i
                    break

            # Si encontramos el mensaje y no es el primer mensaje del chat
            if message_index is not None and message_index > 0:
                # Verificar si el mensaje anterior es de un usuario
                previous_message = message_list[message_index - 1]
                if previous_message.sender == 'user':
                    user_message_to_delete = previous_message
                    user_message_content = previous_message.content

                    # Registrar información para depuración
                    logger.info(f"Encontrado mensaje de usuario para eliminar: ID {user_message_to_delete.id}")

        # Eliminar el mensaje del bot de la base de datos
        message.delete()

        # Si encontramos un mensaje de usuario que precedió al mensaje del bot, eliminarlo también
        if user_message_to_delete:
            user_message_to_delete.delete()

        # Actualizar la memoria de langchain si existe
        memory_obj = chat.memories.last()
        if memory_obj:
            # Deserializar la memoria actual
            memory_instance = deserialize_memory(memory_obj.memory_data)

            # Filtrar los mensajes para eliminar los mensajes borrados
            updated_messages = []
            skip_next_ai_message = False

            # Primero, identificar los mensajes a eliminar
            for i, msg in enumerate(memory_instance.chat_memory.messages):
                # Verificar si es un mensaje de bot o usuario
                is_ai_message = isinstance(msg, AIMessage)

                # Si es un mensaje de usuario y coincide con el que queremos eliminar
                if not is_ai_message and user_message_content and user_message_content in msg.content:
                    # Marcar para omitir este mensaje y el siguiente mensaje del bot
                    skip_next_ai_message = True
                    logger.info(f"Omitiendo mensaje de usuario en memoria: {msg.content[:50]}...")
                    continue

                # Si es un mensaje del bot y el anterior era un mensaje de usuario que omitimos
                if is_ai_message and skip_next_ai_message:
                    skip_next_ai_message = False
                    logger.info(f"Omitiendo mensaje del bot en memoria: {msg.content[:50]}...")
                    continue

                # Si es un mensaje del bot y coincide con el que queremos eliminar
                if is_ai_message and is_bot_message and message_content in msg.content:
                    # Si el mensaje anterior era de usuario, marcarlo para omitir
                    if i > 0 and not isinstance(memory_instance.chat_memory.messages[i-1], AIMessage):
                        # Eliminar el mensaje de usuario anterior si ya lo habíamos añadido
                        if len(updated_messages) > 0 and not isinstance(updated_messages[-1], AIMessage):
                            updated_messages.pop()
                            logger.info("Eliminando mensaje de usuario previo de la lista actualizada")
                    logger.info(f"Omitiendo mensaje del bot en memoria: {msg.content[:50]}...")
                    continue

                # Mantener este mensaje en la memoria actualizada
                updated_messages.append(msg)

            # Actualizar la memoria con los mensajes filtrados
            memory_instance.chat_memory.messages = updated_messages

            # Serializar y guardar la memoria actualizada
            serialized_memory = serialize_memory(memory_instance)
            Memory.objects.create(chat=chat, memory_data=serialized_memory)

        # Verificar si el chat está vacío después de eliminar los mensajes
        remaining_messages = chat.messages.count()
        chat_deleted = False

        # Si no quedan mensajes, eliminar el chat
        if remaining_messages == 0:
            chat_id = str(chat.chat_id)  # Guardar el ID antes de eliminar
            chat.delete()
            chat_deleted = True
            logger.info(f"Chat {chat_id} eliminado por no tener mensajes")

        # Devolver información sobre los mensajes eliminados
        response_data = {
            "status": "success",
            "message": "Mensaje eliminado correctamente",
            "deleted_bot_message": True
        }

        if user_message_to_delete:
            response_data["deleted_user_message"] = True
            response_data["user_message_id"] = user_message_to_delete.id

        if chat_deleted:
            response_data["chat_deleted"] = True
            response_data["chat_id"] = chat_id

        return JsonResponse(response_data)
    except Exception as e:
        return JsonResponse({"status": "error", "message": str(e)}, status=500)


# ===== AUTHENTICATION VIEWS =====

def register_view(request):
    """Vista para el registro de un nuevo usuario."""
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)  # Autenticar usuario después del registro
            return redirect('chat')  # Redirige al chat tras el registro exitoso
        else:
            messages.error(request, "Registro inválido. Verifica los datos ingresados.")
    else:
        form = UserCreationForm()
    return render(request, 'chat/register.html', {'form': form})


def login_view(request):
    """Vista para el inicio de sesión."""
    if request.method == 'POST':
        form = AuthenticationForm(data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)
            return redirect('chat')  # Redirige al chat tras inicio de sesión
        else:
            messages.error(request, "Credenciales incorrectas. Intenta nuevamente.")
    else:
        form = AuthenticationForm()
    return render(request, 'chat/login.html', {'form': form})


def logout_view(request):
    """Vista para cerrar sesión."""
    logout(request)
    return redirect('home')  # Después de cerrar sesión, redirige a home


def home_view(request):
    """Vista para la página de inicio."""
    return render(request, 'chat/home.html')


def test_inline_code_view(request):
    """Vista de prueba para el estilo de código inline."""
    return render(request, 'chat/test_inline_code.html')


# ===== SEGMENT CHAT API =====

@method_decorator(csrf_exempt, name='dispatch')
class SegmentChatAPIView(View):
    """API view for segment-specific chat functionality"""

    def get_or_create_segment_chat(self, request, segment_chat_id, parent_message_id, selected_text):
        """Get existing segment chat or create a new one"""
        if not segment_chat_id:
            # Create new segment chat
            parent_message = get_object_or_404(Message, id=parent_message_id, chat__user=request.user)
            segment_chat = SegmentChat.objects.create(
                user=request.user,
                parent_message=parent_message,
                selected_text=selected_text,
                title=f"Discussion: {selected_text[:50]}..."
            )
            memory_instance = self.initialize_segment_memory(selected_text)
        else:
            # Get existing segment chat
            segment_chat = get_object_or_404(SegmentChat, segment_chat_id=segment_chat_id, user=request.user)
            memory_obj = segment_chat.memories.last()
            serialized_memory = memory_obj.memory_data if memory_obj else ""
            memory_instance = deserialize_memory(serialized_memory)

        return segment_chat, memory_instance

    def initialize_segment_memory(self, selected_text):
        """Initialize memory for segment chat with context"""
        memory_instance = ConversationBufferWindowMemory(k=10, memory_key="chat_history", return_messages=True)

        # Add the selected text as initial context
        context_message = f"Let's discuss this specific text segment: \"{selected_text}\""
        memory_instance.chat_memory.add_ai_message(
            "I'm ready to discuss this specific text segment with you. What would you like to know or explore about it?"
        )

        return memory_instance

    def post(self, request):
        """Handle segment chat messages"""
        try:
            data = json.loads(request.body)
            message = data.get('message', '')
            segment_chat_id = data.get('segment_chat_id')
            parent_message_id = data.get('parent_message_id')
            selected_text = data.get('selected_text', '')
            action = data.get('action', 'send_message')  # 'send_message', 'create_segment', 'get_messages'

            if action == 'create_segment':
                # Create new segment chat
                segment_chat, memory_instance = self.get_or_create_segment_chat(
                    request, None, parent_message_id, selected_text
                )

                return JsonResponse({
                    "status": "success",
                    "segment_chat_id": str(segment_chat.segment_chat_id),
                    "title": segment_chat.title,
                    "selected_text": segment_chat.selected_text,
                    "messages": []
                })

            elif action == 'send_message':
                # Send message in segment chat
                segment_chat, memory_instance = self.get_or_create_segment_chat(
                    request, segment_chat_id, parent_message_id, selected_text
                )

                # Use LLM service to query the model with segment context
                from .services.llm_service import query_llm

                # Add context about the segment to the message
                contextual_message = f"Regarding the text segment: \"{segment_chat.selected_text}\"\n\nUser question: {message}"

                response = query_llm(request, contextual_message, memory_instance, 'standard_query')

                # Save messages
                user_message = SegmentMessage.objects.create(
                    segment_chat=segment_chat,
                    sender='user',
                    content=message
                )
                bot_message = SegmentMessage.objects.create(
                    segment_chat=segment_chat,
                    sender='bot',
                    content=json.dumps(response) if isinstance(response, dict) else response
                )

                # Save memory
                serialized_memory = serialize_memory(memory_instance)
                SegmentMemory.objects.create(segment_chat=segment_chat, memory_data=serialized_memory)

                return JsonResponse({
                    "status": "success",
                    "response": response,
                    "segment_chat_id": str(segment_chat.segment_chat_id),
                    "user_message_id": user_message.id,
                    "bot_message_id": bot_message.id
                })

            elif action == 'get_messages':
                # Get messages for existing segment chat
                segment_chat = get_object_or_404(SegmentChat, segment_chat_id=segment_chat_id, user=request.user)
                messages_data = []

                for msg in segment_chat.messages.all().order_by('timestamp'):
                    messages_data.append({
                        'id': msg.id,
                        'sender': msg.sender,
                        'content': msg.content,
                        'timestamp': msg.timestamp.isoformat()
                    })

                return JsonResponse({
                    "status": "success",
                    "segment_chat_id": str(segment_chat.segment_chat_id),
                    "title": segment_chat.title,
                    "selected_text": segment_chat.selected_text,
                    "messages": messages_data
                })

            else:
                return JsonResponse({"error": "Invalid action"}, status=400)

        except Exception as e:
            logger.error(f"Error in SegmentChatAPIView: {e}")
            return JsonResponse({"error": str(e)}, status=500)

    def get(self, request):
        """Get list of active segment chats for user"""
        try:
            segment_chats = SegmentChat.objects.filter(
                user=request.user,
                is_active=True
            ).order_by('-created_at')

            segment_chats_data = []
            for sc in segment_chats:
                segment_chats_data.append({
                    'segment_chat_id': str(sc.segment_chat_id),
                    'title': sc.title,
                    'selected_text': sc.selected_text,
                    'created_at': sc.created_at.isoformat(),
                    'message_count': sc.messages.count()
                })

            return JsonResponse({
                "status": "success",
                "segment_chats": segment_chats_data
            })

        except Exception as e:
            logger.error(f"Error getting segment chats: {e}")
            return JsonResponse({"error": str(e)}, status=500)