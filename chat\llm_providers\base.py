"""
Clase base para proveedores de LLM.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from langchain.chains.conversation.memory import ConversationBufferWindowMemory

class BaseLLMProvider(ABC):
    """Clase base abstracta para proveedores de LLM."""

    @property
    @abstractmethod
    def model_name(self) -> str:
        """Nombre del modelo utilizado."""
        pass

    def set_model(self, model_name: str) -> None:
        """
        Configura el modelo a utilizar.

        Args:
            model_name: Nombre del modelo a utilizar.

        Returns:
            None

        Note:
            Esta implementación base no hace nada.
            Las subclases deben sobrescribir este método si soportan cambio de modelo.
        """
        pass

    @abstractmethod
    def query(self, user_input: str, memory_instance: ConversationBufferWindowMemory) -> str:
        """
        Consulta el modelo con la entrada del usuario y la memoria de conversación.

        Args:
            user_input: Texto de entrada del usuario.
            memory_instance: Instancia de memoria de conversación.

        Returns:
            Respuesta del modelo como texto.
        """
        pass

    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """
        Obtiene información sobre el modelo.

        Returns:
            Diccionario con información del modelo.
        """
        pass

def get_exam_prompt(topic: str) -> str:
    """Generate a prompt for exam creation."""
    prompt = f"Genera un examen interactivo sobre {topic}."
    prompt += r"""

Responde ÚNICAMENTE con un JSON válido que siga exactamente esta estructura, sin texto adicional antes o después:
{
    "title": "Título del examen sobre el tema solicitado",
    "description": "Breve descripción del examen",
    "questions": [
        {
            "id": "q1",
            "question_text": "Pregunta 1 sobre el tema",
            "options": ["Opción 1", "Opción 2", "Opción 3", "Opción 4"],
            "correct_answer_index": 0,
            "explanation": "Explicación de por qué la opción 1 es correcta"
        },
        {
            "id": "q2",
            "question_text": "Pregunta 2 sobre el tema",
            "options": ["Opción 1", "Opción 2", "Opción 3", "Opción 4"],
            "correct_answer_index": 1,
            "explanation": "Explicación de por qué la opción 2 es correcta"
        }
    ]
}

IMPORTANTE:
1. Genera las preguntas con sus respectivas opciones y explicaciones.
2. Asegúrate de que el JSON sea válido y esté bien formateado.
3. No incluyas comillas simples dentro de las cadenas de texto, solo comillas dobles para el JSON.
4. Para expresiones matemáticas, usa la notación LaTeX:
   - Para expresiones en línea, usa $expresión$ (por ejemplo: $x^2$)
   - Para expresiones en bloque, usa $$expresión$$ (por ejemplo: $$\sum_{i=1}^{n} i = \frac{n(n+1)}{2}$$)
5. No incluyas texto adicional antes o después del JSON.
6. Asegúrate de que cada pregunta tenga un ID único (q1, q2, q3, etc.).
7. El índice de la respuesta correcta debe ser un número entre 0 y 3 (correspondiente a la posición en el array de opciones).

Recuerda: Responde SOLO con el JSON, sin texto adicional.
"""
    return prompt


def format_exam_query(user_input: str) -> str:
    """Format the user input for exam generation."""
    return f"{get_exam_prompt(user_input)}"
