/**
 * Module theme-preloader.js - Pre-loads theme assets for smoother transitions
 *
 * This module implements:
 * - Pre-loading of theme-specific assets
 * - Caching of theme styles
 * - Smoother transitions between themes
 */

class ThemePreloader {
  constructor() {
    this.isInitialized = false;
    this.themeCache = {
      'light-theme': null,
      'dark-theme': null
    };
    this.THEMES = {
      LIGHT: 'light-theme',
      DARK: 'dark-theme'
    };
  }

  /**
   * Initialize the theme preloader
   */
  initialize() {
    if (this.isInitialized) return;

    // Pre-load both themes
    this.preloadThemes();

    // Listen for theme changes to update cache
    document.addEventListener('themeChanged', (e) => {
      const theme = e.detail.theme;
      this.updateThemeCache(theme);
    });

    this.isInitialized = true;
    console.log('Theme preloader initialized');
  }

  /**
   * Pre-load both themes
   */
  preloadThemes() {
    // Create hidden elements with both themes to force browser to load and cache styles
    const lightPreloader = document.createElement('div');
    lightPreloader.className = 'theme-preloader light-theme';
    lightPreloader.style.position = 'absolute';
    lightPreloader.style.width = '1px';
    lightPreloader.style.height = '1px';
    lightPreloader.style.opacity = '0.01';
    lightPreloader.style.pointerEvents = 'none';
    lightPreloader.style.left = '-9999px';
    lightPreloader.style.top = '-9999px';
    lightPreloader.innerHTML = `
      <div class="bg-primary"></div>
      <div class="bg-secondary"></div>
      <div class="bg-tertiary"></div>
      <div class="text-primary"></div>
      <div class="accent-primary"></div>
    `;

    const darkPreloader = document.createElement('div');
    darkPreloader.className = 'theme-preloader dark-theme';
    darkPreloader.style.position = 'absolute';
    darkPreloader.style.width = '1px';
    darkPreloader.style.height = '1px';
    darkPreloader.style.opacity = '0.01';
    darkPreloader.style.pointerEvents = 'none';
    darkPreloader.style.left = '-9999px';
    darkPreloader.style.top = '-9999px';
    darkPreloader.innerHTML = `
      <div class="bg-primary"></div>
      <div class="bg-secondary"></div>
      <div class="bg-tertiary"></div>
      <div class="text-primary"></div>
      <div class="accent-primary"></div>
    `;

    // Add to DOM
    document.body.appendChild(lightPreloader);
    document.body.appendChild(darkPreloader);

    // Cache computed styles
    this.cacheThemeStyles(this.THEMES.LIGHT, lightPreloader);
    this.cacheThemeStyles(this.THEMES.DARK, darkPreloader);

    // Remove after a short delay to ensure styles are loaded
    setTimeout(() => {
      if (lightPreloader.parentNode) document.body.removeChild(lightPreloader);
      if (darkPreloader.parentNode) document.body.removeChild(darkPreloader);
    }, 1000);
  }

  /**
   * Cache computed styles for a theme
   * @param {string} theme - Theme name
   * @param {HTMLElement} element - Element with the theme applied
   */
  cacheThemeStyles(theme, element) {
    // Create a cache object for the theme
    this.themeCache[theme] = {
      bgPrimary: getComputedStyle(element.querySelector('.bg-primary')).backgroundColor,
      bgSecondary: getComputedStyle(element.querySelector('.bg-secondary')).backgroundColor,
      bgTertiary: getComputedStyle(element.querySelector('.bg-tertiary')).backgroundColor,
      textPrimary: getComputedStyle(element.querySelector('.text-primary')).color,
      accentPrimary: getComputedStyle(element.querySelector('.accent-primary')).backgroundColor
    };

    console.log(`Cached styles for ${theme}:`, this.themeCache[theme]);
  }

  /**
   * Update the theme cache for a specific theme
   * @param {string} theme - Theme name
   */
  updateThemeCache(theme) {
    // Create a temporary element with the theme
    const tempElement = document.createElement('div');
    tempElement.className = `theme-preloader ${theme}`;
    tempElement.style.position = 'absolute';
    tempElement.style.width = '1px';
    tempElement.style.height = '1px';
    tempElement.style.opacity = '0';
    tempElement.style.pointerEvents = 'none';
    tempElement.innerHTML = `
      <div class="bg-primary"></div>
      <div class="bg-secondary"></div>
      <div class="bg-tertiary"></div>
      <div class="text-primary"></div>
      <div class="accent-primary"></div>
    `;

    // Add to DOM
    document.body.appendChild(tempElement);

    // Cache computed styles
    this.cacheThemeStyles(theme, tempElement);

    // Remove element
    document.body.removeChild(tempElement);
  }

  /**
   * Get cached styles for a theme
   * @param {string} theme - Theme name
   * @returns {Object|null} - Cached styles or null if not cached
   */
  getThemeStyles(theme) {
    return this.themeCache[theme];
  }
}

// Create and export instance
const themePreloader = new ThemePreloader();

/**
 * Initialize the theme preloader
 */
export function initializeThemePreloader() {
  themePreloader.initialize();
  return themePreloader;
}
