/**
 * Módu<PERSON> effects.js - Efectos visuales
 *
 * Este módulo contiene funciones para efectos visuales y animaciones.
 */

import { debounce, isMobileDevice } from './utils.js';
import { setupCodeObserver } from './code-handler.js';

/**
 * Inicializa todos los efectos visuales.
 */
export function initializeEffects() {
  // Inicializar partículas de fondo
  initializeParticles();

  // Inicializar observador de código
  setupCodeObserver();

  // Inicializar animaciones de scroll
  initializeScrollAnimations();

  // Inicializar efectos de hover
  initializeHoverEffects();

  // Inicializar redimensionamiento automático de textarea
  initializeAutoResize();
}

/**
 * Inicializa las partículas de fondo.
 */
function initializeParticles() {
  const particlesContainer = document.getElementById('particles-container');
  if (!particlesContainer) return;

  // No mostrar partículas en dispositivos móviles para mejorar rendimiento
  if (isMobileDevice()) {
    particlesContainer.style.display = 'none';
    return;
  }

  // Limpiar partículas existentes
  particlesContainer.innerHTML = '';

  // Crear partículas
  const particleCount = 15; // Reducido para mejor rendimiento
  const colors = [
    'rgba(126, 231, 135, 0.5)',
    'rgba(88, 166, 255, 0.5)',
    'rgba(180, 180, 180, 0.3)'
  ];

  for (let i = 0; i < particleCount; i++) {
    const size = Math.random() * 5 + 2;
    const colorIndex = Math.floor(Math.random() * 4) + 1; // Use 1-4 for CSS variables
    const left = Math.random() * 100;
    const top = Math.random() * 100;
    const animationDuration = (Math.random() * 20 + 10) + 's';
    const animationDelay = (Math.random() * 5) + 's';

    const particle = document.createElement('div');
    particle.className = 'particle';

    // Apply theme-aware styling using CSS custom properties
    particle.style.width = size + 'px';
    particle.style.height = size + 'px';
    particle.style.backgroundColor = `var(--particle-color-${colorIndex})`;
    particle.style.left = left + 'vw';
    particle.style.top = top + 'vh';
    particle.style.animationDuration = animationDuration;
    particle.style.animationDelay = animationDelay;
    particle.style.opacity = '0.2';

    particlesContainer.appendChild(particle);
  }
}

/**
 * Inicializa las animaciones de scroll.
 */
function initializeScrollAnimations() {
  // Crear observador de intersección
  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('in-view');
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });

  // Observar mensajes
  function observeMessages() {
    document.querySelectorAll('.chat-message:not(.in-view)').forEach(message => {
      observer.observe(message);
    });
  }

  // Observar mensajes iniciales
  observeMessages();

  // Configurar observador para nuevos mensajes
  const chatBoxContent = document.getElementById('chat-box-content');
  if (chatBoxContent) {
    const messageObserver = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Esperar un poco para que se complete la renderización
          setTimeout(observeMessages, 100);
        }
      });
    });

    messageObserver.observe(chatBoxContent, {
      childList: true
    });
  }
}

/**
 * Inicializa los efectos de hover.
 */
function initializeHoverEffects() {
  // Agregar efecto de hover a los botones
  document.querySelectorAll('.action-button, .copy-code-button').forEach(button => {
    button.addEventListener('mouseenter', () => {
      button.classList.add('hover');
    });

    button.addEventListener('mouseleave', () => {
      button.classList.remove('hover');
    });
  });
}

/**
 * Inicializa el redimensionamiento automático de textarea.
 */
function initializeAutoResize() {
  const textarea = document.getElementById('user-input');
  if (!textarea) return;

  // Función para ajustar altura
  const adjustHeight = () => {
    textarea.style.height = 'auto';
    textarea.style.height = (textarea.scrollHeight) + 'px';
  };

  // Aplicar al cargar y al cambiar contenido
  textarea.addEventListener('input', adjustHeight);
  window.addEventListener('resize', debounce(adjustHeight, 200));

  // Ajustar altura inicial
  setTimeout(adjustHeight, 100);
}
