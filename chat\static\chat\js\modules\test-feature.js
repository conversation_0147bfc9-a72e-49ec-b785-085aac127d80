/**
 * <PERSON><PERSON><PERSON><PERSON> test-feature.js - Detección y manejo de características de test
 *
 * Este módulo implementa:
 * - Detección de palabras clave relacionadas con tests en el input
 * - Resaltado visual de palabras clave detectadas
 * - Botón de activación de modo test
 */

// Importar ExamHandler para acceder a sus métodos
import { ExamHandler } from './exam-handler.js';

// Clase principal para la funcionalidad de test
export class TestFeature {
    constructor() {
        // Palabras clave que activan la detección de test
        this.testKeywords = [
            "create test", "make quiz", "generate questions",
            "crear test", "hacer examen", "generar preguntas",
            "crear examen", "hacer quiz", "generar cuestionario",
            "crear cuestionario", "hacer preguntas", "test sobre",
            "examen sobre", "quiz sobre", "cuestionario sobre"
        ];

        this.testModeActive = false;
        this.detectedKeyword = null;
        this.highlightContainer = null;
        this.examHandler = new ExamHandler();
    }

    /**
     * Inicializa la funcionalidad de detección de test
     */
    initialize() {
        const chatInput = document.getElementById('user-input');
        if (!chatInput) {
            console.error('Chat input element not found');
            return;
        }

        // Detectar palabras clave en el input
        chatInput.addEventListener('input', this.handleInputChange.bind(this));

        // Manejar clic en el botón de toggle o en la palabra clave resaltada
        document.addEventListener('click', this.handleToggleClick.bind(this));

        // Limpiar resaltado cuando se envía el mensaje
        const chatForm = document.querySelector('.chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', this.handleFormSubmit.bind(this));
        }
    }

    /**
     * Maneja cambios en el input para detectar palabras clave
     * @param {Event} event - Evento de input
     */
    handleInputChange(event) {
        const inputElement = event.target;
        const inputText = inputElement.value.toLowerCase();

        // Buscar palabras clave en el texto
        const keyword = this.testKeywords.find(kw =>
            inputText.includes(kw.toLowerCase())
        );

        if (keyword && !this.testModeActive) {
            // Se encontró una palabra clave y el modo test no está activo
            this.detectedKeyword = keyword;
            this.highlightKeyword(inputElement, keyword);
        } else if (!keyword && this.detectedKeyword) {
            // Ya no hay palabra clave, eliminar resaltado
            this.removeHighlight();
            this.detectedKeyword = null;
        }
    }

    /**
     * Resalta la palabra clave detectada y añade botón de toggle
     * @param {HTMLElement} inputElement - Elemento de input
     * @param {string} keyword - Palabra clave detectada
     */
    highlightKeyword(inputElement, keyword) {
        // Eliminar resaltado existente si lo hay
        this.removeHighlight();

        // Crear contenedor para el resaltado
        this.highlightContainer = document.createElement('div');
        this.highlightContainer.className = 'keyword-highlight-container';

        // Crear contenido con la palabra clave resaltada
        const keywordRegex = new RegExp(`(${keyword})`, 'gi');
        const inputValue = inputElement.value;

        // Encontrar la posición de la palabra clave
        const keywordMatch = keywordRegex.exec(inputValue);
        if (!keywordMatch) return;

        const keywordIndex = keywordMatch.index;
        const keywordText = keywordMatch[0];

        // Crear el contenido HTML con la palabra clave resaltada
        const highlightedContent = document.createElement('div');
        highlightedContent.className = 'highlighted-content';
        highlightedContent.innerHTML =
            this.escapeHtml(inputValue.substring(0, keywordIndex)) +
            `<span class="test-keyword-highlight">${this.escapeHtml(keywordText)}</span>` +
            this.escapeHtml(inputValue.substring(keywordIndex + keywordText.length));

        // Añadir botón de toggle
        const toggleBtn = document.createElement('button');
        toggleBtn.id = 'test-toggle';
        toggleBtn.className = 'test-toggle-btn';
        toggleBtn.innerHTML = '<i class="fa-solid fa-flask"></i>';
        toggleBtn.title = 'Activar modo test';

        // Posicionar el contenedor sobre el input
        const inputRect = inputElement.getBoundingClientRect();
        this.highlightContainer.style.position = 'absolute';
        this.highlightContainer.style.top = `${inputRect.top}px`;
        this.highlightContainer.style.left = `${inputRect.left}px`;
        this.highlightContainer.style.width = `${inputRect.width}px`;
        this.highlightContainer.style.height = `${inputRect.height}px`;
        this.highlightContainer.style.pointerEvents = 'none';

        // Añadir contenido y botón al contenedor
        this.highlightContainer.appendChild(highlightedContent);
        this.highlightContainer.appendChild(toggleBtn);

        // El botón debe ser clickeable
        toggleBtn.style.pointerEvents = 'auto';

        // Añadir al DOM
        document.body.appendChild(this.highlightContainer);
    }

    /**
     * Elimina el resaltado de la palabra clave
     */
    removeHighlight() {
        if (this.highlightContainer && this.highlightContainer.parentNode) {
            this.highlightContainer.parentNode.removeChild(this.highlightContainer);
            this.highlightContainer = null;
        }
    }

    /**
     * Maneja el clic en el botón de toggle o en la palabra clave resaltada
     * @param {Event} event - Evento de clic
     */
    handleToggleClick(event) {
        if (event.target.matches('.test-keyword-highlight') ||
            event.target.matches('#test-toggle') ||
            event.target.closest('#test-toggle') ||
            (event.target.tagName === 'I' && event.target.parentNode.matches('#test-toggle'))) {
            this.toggleTestMode();
        }
    }

    /**
     * Maneja el envío del formulario para limpiar el resaltado
     */
    handleFormSubmit() {
        this.removeHighlight();
    }

    /**
     * Activa o desactiva el modo test
     */
    toggleTestMode() {
        this.testModeActive = !this.testModeActive;
        const chatInput = document.getElementById('user-input');

        if (this.testModeActive) {
            // Activar modo test
            chatInput.classList.add('test-mode-active');
            this.showNotification('Modo test activado (solo para el próximo mensaje)');

            // Actualizar el estado en el ExamHandler
            this.examHandler.isTestSuggestionActive = true;
            this.examHandler.suggestionCancelled = false;

            // Añadir un indicador visual de que el modo test está activo solo para un mensaje
            const inputContainer = document.querySelector('.input-container');
            if (inputContainer && !document.querySelector('.test-mode-indicator')) {
                const indicator = document.createElement('div');
                indicator.className = 'test-mode-indicator';
                indicator.innerHTML = '<i class="fa-solid fa-flask"></i> Modo test activo (solo para el próximo mensaje)';
                inputContainer.parentNode.insertBefore(indicator, inputContainer);
            }
        } else {
            // Desactivar modo test
            chatInput.classList.remove('test-mode-active');
            this.removeHighlight();
            this.showNotification('Modo test desactivado');

            // Actualizar el estado en el ExamHandler
            this.examHandler.isTestSuggestionActive = false;

            // Eliminar el indicador visual
            const indicator = document.querySelector('.test-mode-indicator');
            if (indicator) {
                indicator.remove();
            }
        }
    }

    /**
     * Muestra una notificación temporal
     * @param {string} message - Mensaje a mostrar
     */
    showNotification(message) {
        // Eliminar notificaciones existentes
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => {
            notification.remove();
        });

        // Crear nueva notificación
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;

        // Añadir al DOM
        document.body.appendChild(notification);

        // Eliminar después de 2 segundos
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 2000);
    }

    /**
     * Escapa caracteres HTML para prevenir XSS
     * @param {string} text - Texto a escapar
     * @returns {string} - Texto escapado
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Verifica si el modo test está activo
     * @returns {boolean} - True si el modo test está activo
     */
    isTestModeActive() {
        return this.testModeActive;
    }

    /**
     * Reinicia el estado del modo test
     */
    reset() {
        this.testModeActive = false;
        this.detectedKeyword = null;
        this.removeHighlight();

        const chatInput = document.getElementById('user-input');
        if (chatInput) {
            chatInput.classList.remove('test-mode-active');
        }

        // Eliminar el indicador visual
        const indicator = document.querySelector('.test-mode-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
}

// Exportar una instancia única para uso en toda la aplicación
export const testFeature = new TestFeature();