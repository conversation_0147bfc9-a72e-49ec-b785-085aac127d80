/* scroll-nav.css - Styles for the scroll navigation button */

.scroll-nav-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 45px;
    height: 45px;
    background-color: var(--accent-primary);
    color: var(--text-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 9999;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    opacity: 0.9;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.scroll-nav-button:hover {
    opacity: 1;
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.scroll-nav-button i {
    font-size: 1.2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .scroll-nav-button {
        bottom: 20px;
        right: 20px;
        width: 36px;
        height: 36px;
    }
}
