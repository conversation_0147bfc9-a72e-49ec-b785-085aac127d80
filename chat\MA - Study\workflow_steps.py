# workflow_steps.py
import logging
import re
import json
import time
import concurrent.futures
from typing import Dict, List, Tuple, Optional, Any

import tqdm # Import the module itself

# Local imports
from config import ROLE_CONFIG, MAX_CONCURRENT_WORKERS, DEBUG_MODE
from prompts import (
    CLASSIFIER_SYSTEM_PROMPT, SIMPLE_QUERY_SYSTEM_PROMPT,
    PLANNER_SYSTEM_PROMPT_TEMPLATE, WORKER_SYSTEM_PROMPT_TEMPLATE
)
from llm_interface import call_llm, LLMError, ProviderClientError
from tool_handler import identify_tool_and_params, execute_tool, process_tool_result


# --- Helper Functions ---
def clean_subtask_result(result: str, task: str) -> str:
    """
    Helper function to clean subtask results by removing self-references
    and redundant content related to the task itself.

    Args:
        result: The raw result text from the worker
        task: The subtask prompt that was provided to the worker

    Returns:
        The cleaned result with self-references and redundant content removed
    """
    # Basic cleanup
    cleaned_result = result.strip()

    # Split into lines for filtering
    lines = cleaned_result.split('\n')
    filtered_lines = []
    skip_next_lines = 0

    for i, line in enumerate(lines):
        if skip_next_lines > 0:
            skip_next_lines -= 1
            continue

        # Skip lines that explicitly mention the subtask
        if re.search(r'(sub-?task|assigned task|your task|in this section|for this topic|in summary)', line, re.IGNORECASE):
            # If this is a heading, also skip the next line if it's empty (common pattern)
            if line.startswith('#') and i+1 < len(lines) and not lines[i+1].strip():
                skip_next_lines = 1
            continue

        # Skip lines that repeat the task verbatim or with minor variations
        task_words = set(task.lower().split())
        line_words = set(line.lower().split())
        word_overlap = len(task_words.intersection(line_words))

        # If the line has high overlap with the task and is short, skip it
        if word_overlap > min(3, len(task_words) * 0.7) and len(line.split()) < 15:
            continue

        filtered_lines.append(line)

    cleaned_result = '\n'.join(filtered_lines).strip()
    return cleaned_result


# --- Query Classification ---
def classify_query(query: str) -> Dict[str, Any]:
    """
    Uses an LLM to classify the query type.

    Returns:
        A dictionary containing 'query_type' ('simple', 'complex', 'tool')
        or an 'error' key if classification fails.
    """
    logging.info("Classifying query type...", extra={'is_step': True})
    role_cfg = ROLE_CONFIG['classifier']
    provider = role_cfg['provider']
    model = role_cfg['model']
    max_tokens = role_cfg['max_tokens']

    messages = [
        {"role": "system", "content": CLASSIFIER_SYSTEM_PROMPT},
        {"role": "user", "content": f"User Query: \"{query}\""}
    ]

    try:
        response = call_llm(
            provider, model, messages,
            temperature=0.1,  # Low temp for deterministic classification
            max_tokens=max_tokens,
            response_format={"type": "json_object"}
        )

        # Clean potential markdown ```json ... ``` before parsing
        cleaned_response = re.sub(r"^```json\s*|\s*```$", "", response, flags=re.MULTILINE | re.DOTALL).strip()

        result = json.loads(cleaned_response)

        if isinstance(result, dict) and "query_type" in result and result["query_type"] in ["simple", "complex", "tool"]:
            logging.info(f"Query classified as: {result['query_type']}", extra={'is_success': True})
            return result
        else:
            logging.error(f"Classification response malformed or invalid type: {response}")
            # Default to complex on structural error after successful LLM call
            return {"query_type": "complex", "error": "Malformed classification response"}

    except json.JSONDecodeError as e:
        logging.error(f"Failed to parse classification JSON: {e}. Response was: {response}")
        return {"query_type": "complex", "error": f"JSON parse error: {e}"}
    except (LLMError, ProviderClientError) as e:
        logging.error(f"Query classification LLM call failed: {e}")
        # Default to complex if the LLM call itself fails
        return {"query_type": "complex", "error": f"Classification failed: {e}"}
    except Exception as e:
        logging.error(f"Unexpected error during query classification: {e}", exc_info=DEBUG_MODE)
        return {"query_type": "complex", "error": f"Unexpected error: {e}"}


# --- Simple Query Handling ---
def handle_simple_query(query: str) -> str:
    """
    Handles a simple query using a direct LLM call.
    """
    logging.info("Handling simple query...", extra={'is_step': True})
    role_cfg = ROLE_CONFIG['simple_query']
    provider = role_cfg['provider']
    model = role_cfg['model']
    max_tokens = role_cfg['max_tokens']

    messages = [
        {"role": "system", "content": SIMPLE_QUERY_SYSTEM_PROMPT},
        {"role": "user", "content": query}
    ]

    try:
        response = call_llm(
            provider, model, messages,
            temperature=0.7, # Default temp from config could be used here
            max_tokens=max_tokens
        )
        logging.info("Simple query answered successfully.", extra={'is_success': True})
        return response
    except (LLMError, ProviderClientError) as e:
        logging.error(f"Simple query handling failed: {e}")
        return f"Error: Could not process the simple query.\nDetails: {e}"
    except Exception as e:
        logging.error(f"Unexpected error during simple query handling: {e}", exc_info=DEBUG_MODE)
        return f"Error: An unexpected issue occurred.\nDetails: {e}"


# --- Tool Path Handling ---
def handle_tool_query(query: str) -> str:
    """
    Orchestrates the tool identification, execution, and processing path.
    """
    logging.info("Handling tool query...", extra={'is_step': True})
    try:
        tool_info = identify_tool_and_params(query) # Uses placeholder logic

        if tool_info.get("error"):
             logging.error(f"Tool identification failed: {tool_info['error']}")
             # Fallback: Treat as complex query? Or return error?
             # For now, return an error message to the user
             return f"Error: Could not determine the required tool or parameters.\nDetails: {tool_info['error']}"

        if tool_info and tool_info.get("tool_name"):
            tool_output = execute_tool(tool_info) # Uses placeholder logic

            # Check if tool execution itself returned an error string
            if tool_output.startswith("Error:"):
                 logging.error(f"Tool execution failed: {tool_output}")
                 return tool_output # Return the error message from execution

            final_result = process_tool_result(tool_output) # Uses placeholder logic
            logging.info("Tool query processed successfully.", extra={'is_success': True})
            return final_result
        else:
            # Tool ID returned None or was invalid, but didn't raise an error itself
            logging.warning("Tool query classified, but no specific tool identified by placeholder logic. Treating as complex.")
            # Fallback to complex processing
            # Requires passing the query back to the main loop or calling complex path directly
            # For simplicity here, return a message indicating fallback needed
            # A more integrated approach would re-route in main.py
            # return "FALLBACK_TO_COMPLEX"
            # --- Or, handle complex directly ---
            return process_complex_block(query, "Overall Query Analysis (Tool Fallback)")


    except Exception as e:
        logging.error(f"Unexpected error during tool query handling: {e}", exc_info=DEBUG_MODE)
        return f"Error: An unexpected issue occurred while handling the tool request.\nDetails: {e}"


# --- Complex Query Path Functions ---

def extract_modules(query: str) -> Optional[Dict[str, str]]:
    """
    Parses a query for structured modules (like 'Módulo X: ...').
    Returns a dictionary {module_title: module_content} or None if no structure found.
    """
    logging.debug("Attempting to extract 'Módulo' structure from query...")
    modules: Dict[str, str] = {}
    # Regex to find "Modulo" or "Módulo" followed by numbers/letters and a colon (optional)
    # Handles potential leading/trailing whitespace and variations in capitalization
    module_header_pattern = re.compile(r"^\s*(?:M[oó]dulo)\s+[\w\d\s\-]+:?", re.IGNORECASE | re.MULTILINE)

    matches = list(module_header_pattern.finditer(query))
    if not matches:
        logging.info("No 'Módulo' structure found. Treating query as single block.")
        return None

    last_match_end = 0
    current_module_title = None

    for i, match in enumerate(matches):
        # Extract text between the end of the previous match (or start) and the current match
        if current_module_title:
            module_text = query[last_match_end:match.start()].strip()
            if module_text: # Only add if there's content
                 modules[current_module_title] = module_text

        # Get the current title and prepare for the next block
        current_module_title = match.group(0).strip().rstrip(':').strip()
        last_match_end = match.end()

        # Handle the last module (from the last match to the end of the string)
        if i == len(matches) - 1:
             module_text = query[last_match_end:].strip()
             if module_text and current_module_title:
                 modules[current_module_title] = module_text

    if modules:
        logging.info(f"Extracted {len(modules)} modules.", extra={'is_success': True})
        return modules
    else:
        # This case might occur if headers are found but no content between them
        logging.warning("Found 'Módulo' headers but failed to extract content sections.")
        return None


def plan_tasks(content_block: str, context_title: str) -> List[str]:
    """
    Uses the Planner LLM to break down content into sub-tasks.
    Returns a list of tasks or an empty list on failure.
    Filters out tasks that appear to be reasoning rather than actual tasks.
    """
    logging.info(f"Planning tasks for: '{context_title}'")
    role_cfg = ROLE_CONFIG['planner']
    provider = role_cfg['provider']
    model = role_cfg['model']
    max_tokens = role_cfg['max_tokens']

    # Limit content preview size for prompt
    content_preview = content_block[:1500] + ("..." if len(content_block) > 1500 else "")
    logging.debug(f"Content preview length: {len(content_preview)}")

    system_prompt = PLANNER_SYSTEM_PROMPT_TEMPLATE.format(
        context_title=context_title,
        content_block=content_preview # Use preview in prompt
    )
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Generate the sub-tasks for the content related to '{context_title}'."}
    ]

    try:
        start_time = time.time()
        response = call_llm(
            provider, model, messages,
            temperature=0.2, # Low temp for focused planning
            max_tokens=max_tokens
        )
        logging.info(f"[TIMING] Task planning for '{context_title}' took {time.time() - start_time:.2f}s", extra={'is_timing': True})

        # Enhanced task parsing: Handles numbered lists, bullet points, potential markdown variations
        raw_tasks = []
        lines = response.strip().split('\n')
        for line in lines:
            line = line.strip()
            # Try to strip common list markers (numbers, bullets)
            match = re.match(r"^\s*(?:\d+[.)]?|\*|-|\+)\s*(.*)", line)
            if match:
                task_text = match.group(1).strip()
                if task_text: # Ensure it's not just the marker
                    raw_tasks.append(task_text)
            elif line and not line.startswith(("```", "---", "===")): # Add non-marked lines if they seem like tasks
                 # Avoid adding lines that are likely code blocks or separators
                 # This is heuristic, might need refinement
                 if len(line.split()) > 2: # Simple check: likely a task if more than 2 words
                      raw_tasks.append(line)

        # Filter out tasks that appear to be reasoning rather than actual tasks
        filtered_tasks = []
        reasoning_patterns = [
            r"^(first|then|next|finally|lastly|now|okay|let|i|we|my|our)",  # Starting words indicating reasoning
            r"^(need|should|will|must|can|could|would|going|want|trying)",  # Modal verbs and similar
            r"(think|thought|consider|approach|start|begin|proceed|continue)",  # Process words
            r"(user|query|request|asked|wants|mentioned)",  # References to the user or request
            r"^(after that|before that|once|when)",  # Temporal reasoning phrases
            r"(makes sense|should cover|wraps it up|alright)",  # Concluding phrases
            r"(practicing by|creating and|using)",  # Action phrases that aren't direct tasks
        ]

        # Compile the patterns for efficiency
        compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in reasoning_patterns]

        for task in raw_tasks:
            # Check if the task matches any reasoning pattern
            is_reasoning = False
            for pattern in compiled_patterns:
                if pattern.search(task):
                    is_reasoning = True
                    logging.debug(f"Filtered out reasoning-like task: '{task}'")
                    break

            # Additional check: if task is too long (more than 12 words), it's likely reasoning
            if not is_reasoning and len(task.split()) > 12:
                is_reasoning = True
                logging.debug(f"Filtered out overly verbose task: '{task}'")

            if not is_reasoning:
                # Transform task to be more directive if it doesn't start with a verb
                if not re.match(r"^(explain|define|describe|compare|analyze|implement|create|demonstrate|show|list|outline|discuss)", task, re.IGNORECASE):
                    # Try to extract the main topic and make it a directive
                    topic_match = re.search(r"(data structures|arrays|collections|input/output|file handling|APIs|libraries|projects|debugging|IDEs|Java syntax|primitive data types|variable declaration)", task, re.IGNORECASE)
                    if topic_match:
                        topic = topic_match.group(1)
                        transformed_task = f"Explain {topic} in Java"
                        logging.debug(f"Transformed task: '{task}' -> '{transformed_task}'")
                        filtered_tasks.append(transformed_task)
                    else:
                        filtered_tasks.append(task)
                else:
                    filtered_tasks.append(task)

        if filtered_tasks:
            logging.info(f"Generated {len(filtered_tasks)} tasks for '{context_title}' (filtered from {len(raw_tasks)}).", extra={'is_success': True})
            return filtered_tasks
        else:
            # If all tasks were filtered out, use the original tasks as a fallback
            if raw_tasks:
                logging.warning(f"All tasks were filtered out as reasoning. Using original tasks as fallback.")
                return raw_tasks
            else:
                logging.error(f"Failed to parse tasks from planner response for '{context_title}'. Response: {response[:500]}...")
                return []

    except (LLMError, ProviderClientError) as e:
        logging.error(f"Planner LLM call failed for '{context_title}': {e}")
        return []
    except Exception as e:
        logging.error(f"Unexpected error during task planning for '{context_title}': {e}", exc_info=DEBUG_MODE)
        return []


def execute_subtask(subtask: str, context_title: str, full_content: str) -> Tuple[str, str]:
    """
    Uses a Worker LLM to execute a single sub-task IN DETAIL.
    Returns a tuple: (subtask, result_string). Result string starts with "Error:" on failure.
    """
    logging.debug(f"Executing subtask: '{subtask[:60]}...' (Context: '{context_title}')")
    role_cfg = ROLE_CONFIG['worker']
    provider = role_cfg['provider']
    model = role_cfg['model']
    max_tokens = role_cfg['max_tokens']

    # Provide a larger snippet to the worker, but still capped
    content_snippet = full_content[:5000] + ("..." if len(full_content) > 5000 else "")

    system_prompt = WORKER_SYSTEM_PROMPT_TEMPLATE.format(
        context_title=context_title,
        content_snippet=content_snippet,
        subtask=subtask
    )
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Provide a detailed explanation for this topic. Do not repeat the task in your answer."}
    ]

    try:
        start_time = time.time()
        result = call_llm(
            provider, model, messages,
            temperature=0.6, # Moderately creative for detailed explanation
            max_tokens=max_tokens
        )
        logging.debug(f"[TIMING] Subtask '{subtask[:30]}...' took {time.time() - start_time:.2f}s", extra={'is_timing': True})


        # Basic check if result seems empty or too short (might indicate model refusal/issue)
        if not result or len(result.strip()) < 20:
             logging.warning(f"Worker LLM returned unusually short/empty result for subtask '{subtask[:60]}...'. Result: '{result}'")
             # Optionally treat as error or return the short result
             # return (subtask, f"Error: Worker returned very short result.")


        logging.info(f"Completed subtask: '{subtask[:60]}...' ({len(result)} chars)") # Don't mark success here yet, check result content maybe
        return (subtask, result)

    except (LLMError, ProviderClientError) as e:
        logging.error(f"Worker LLM failed for subtask '{subtask[:60]}...': {e}")
        return (subtask, f"Error: Worker LLM call failed. Details: {e}")
    except Exception as e:
        logging.error(f"Unexpected error during subtask execution for '{subtask[:60]}...': {e}", exc_info=DEBUG_MODE)
        return (subtask, f"Error: Unexpected issue during execution. Details: {e}")


def synthesize_results(subtask_results_dict: Dict[str, str], context_title: str, original_content: str = None) -> str:
    """
    Creates a simple table of contents and appends all subtask answers in order.
    Note: original_content parameter is kept for backward compatibility but not used.
    """
    logging.info(f"Creating table of contents and organizing results for: '{context_title}'")

    if not subtask_results_dict:
        logging.warning(f"No sub-task results provided for '{context_title}'.")
        return f"## {context_title}\n\nError: No sub-task results were generated or provided."

    # Separate successful and failed tasks based on "Error:" prefix convention
    successful_results: Dict[str, str] = {}
    failed_tasks: Dict[str, str] = {}
    for task, result in subtask_results_dict.items():
        if result.startswith("Error:"):
            failed_tasks[task] = result
        else:
            successful_results[task] = result

    if not successful_results:
        logging.error(f"All sub-tasks failed for '{context_title}'. Cannot create index.")
        error_report = "\n\n".join([f"- Task: {task}\n  {result}" for task, result in failed_tasks.items()])
        return f"## {context_title}\n\n**All sub-tasks encountered errors.**\n\n{error_report}"

    logging.debug(f"Creating simple table of contents for {len(successful_results)} successful results")
    if failed_tasks:
        logging.warning(f"{len(failed_tasks)} sub-tasks failed and will be noted in the output for '{context_title}'.")

    # Create a simple table of contents
    final_result = f"# {context_title}\n\n## Table of Contents\n\n"
    for i, task in enumerate(successful_results.keys()):
        final_result += f"{i+1}. {task}\n"

    final_result += "\n\n---\n\n"

    # Add each subtask result without the subtask prompt
    for task, result in successful_results.items():
        # Clean the result to remove any self-references to the subtask
        cleaned_result = clean_subtask_result(result, task)

        # Add the content with just the task as a heading
        final_result += f"## {task}\n\n{cleaned_result}\n\n---\n\n"

    # Add report of failed tasks at the end if any occurred
    if failed_tasks:
        failed_tasks_report = "**Note:** The following sub-tasks could not be completed due to errors:\n\n"
        failed_tasks_report += "\n".join([f"- {task}" for task in failed_tasks])
        final_result += failed_tasks_report

    # DEBUG: Print the raw markdown output for inspection
    print("\n[DEBUG] Raw markdown output before sending to frontend:\n" + final_result)

    return final_result


def process_complex_block(content: str, title: str) -> str:
    """
    Manages the Plan -> Execute pipeline for a single complex block.
    Simply concatenates subtask answers in order without synthesizing.
    """
    logging.info(f"Processing Complex Block: {title}", extra={'is_header': True})
    block_start_time = time.time()

    if not content or not content.strip():
        logging.warning(f"Skipping complex block '{title}' due to empty content.")
        return f"\n## {title}\n\n*Skipped: Content was empty.*\n"

    # 1. Plan Tasks
    logging.info(f"Planning tasks for '{title}'...", extra={'is_step': True})
    tasks = plan_tasks(content, title)

    if not tasks:
        logging.error(f"Planning failed for '{title}'. Cannot proceed with execution.")
        log_timing = f"[TIMING] Block '{title}' (failed at planning) took {time.time() - block_start_time:.2f}s"
        logging.info(log_timing, extra={'is_timing': True})
        return f"\n## {title}\n\n*Error: Failed to generate sub-tasks for this section. Cannot proceed.*\n"

    # 2. Execute Sub-tasks in Parallel
    logging.info(f"Executing {len(tasks)} sub-tasks in parallel for '{title}'...", extra={'is_step': True})
    exec_start_time = time.time()
    sub_task_results: Dict[str, str] = {}
    # Use MAX_CONCURRENT_WORKERS from config
    max_workers = min(len(tasks), MAX_CONCURRENT_WORKERS)
    logging.debug(f"Using max_workers: {max_workers}")

    # Use tqdm for progress bar
    progress_desc = f"Executing Tasks ({title[:20]}..)"
    with tqdm.tqdm(total=len(tasks), desc=progress_desc, unit="task", leave=False, bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}{postfix}]') as progress_bar:
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_task = {
                executor.submit(execute_subtask, task, title, content): task
                for task in tasks
            }

            # Process results as they complete
            for future in concurrent.futures.as_completed(future_to_task):
                task_from_future = future_to_task[future]
                try:
                    subtask, result_text = future.result() # result() raises exceptions from the thread
                    sub_task_results[subtask] = result_text
                    if result_text.startswith("Error:"):
                         logging.warning(f"Sub-task '{task_from_future[:50]}...' failed during execution.")
                except Exception as exc:
                    # Catch exceptions raised *by the future itself* (e.g., thread issues, though less common)
                    # Exceptions *within* execute_subtask should be caught there and returned as "Error:..." string
                    logging.error(f'Sub-task "{task_from_future[:50]}..." generated exception during future.result(): {exc}', exc_info=DEBUG_MODE)
                    sub_task_results[task_from_future] = f"Error: Execution generated exception in thread - {exc}"
                finally:
                    progress_bar.update(1) # Update progress bar regardless of success/failure

    exec_time = time.time() - exec_start_time
    logging.info(f"[TIMING] Sub-task parallel execution for '{title}' took {exec_time:.2f}s", extra={'is_timing': True})

    # Ensure results are in the original task order
    ordered_results = {task: sub_task_results.get(task, f"Error: Result missing for task '{task}'") for task in tasks}

    successful_tasks = sum(1 for result in ordered_results.values() if not result.startswith("Error:"))
    failed_tasks_count = len(tasks) - successful_tasks
    logging.info(f"Execution summary for '{title}': {successful_tasks} succeeded, {failed_tasks_count} failed.")

    # 3. Create a simple table of contents and concatenate results directly
    logging.info(f"Creating table of contents and concatenating results for '{title}'...", extra={'is_step': True})

    # Create a simple table of contents
    final_response = f"# {title}\n\n## Table of Contents\n\n"
    for i, task in enumerate(tasks):
        final_response += f"{i+1}. {task}\n"

    final_response += "\n\n---\n\n"

    # Add each subtask result without the subtask prompt
    for task, result in ordered_results.items():
        # Clean the result to remove any self-references to the subtask
        cleaned_result = clean_subtask_result(result, task)

        # Skip if it's an error
        if cleaned_result.startswith("Error:"):
            final_response += f"## {task}\n\n{cleaned_result}\n\n---\n\n"
            continue

        # Add the content with just the task as a heading
        final_response += f"## {task}\n\n{cleaned_result}\n\n---\n\n"

    # Add report of failed tasks at the end if any occurred
    if failed_tasks_count > 0:
        failed_tasks_list = [task for task, result in ordered_results.items() if result.startswith("Error:")]
        failed_tasks_report = "**Note:** The following sub-tasks could not be completed due to errors:\n\n"
        failed_tasks_report += "\n".join([f"- {task}" for task in failed_tasks_list])
        final_response += failed_tasks_report

    total_block_time = time.time() - block_start_time
    logging.info(f"[TIMING] Total complex block processing for '{title}' took {total_block_time:.2f}s", extra={'is_timing': True})

    return final_response