# --- START OF FILE code_adapted.py ---

import os
import re
import sys
import concurrent.futures
import time
import json # For potential tool parameters
from openai import OpenAI # Used for Groq and xAI (both maintain OpenAI client compatibility)
from dotenv import load_dotenv
from colorama import init, Fore, Back, Style
import tqdm # Import the module itself

# Initialize colorama for cross-platform colored terminal output
init(autoreset=True)

# --- Configuration ---
load_dotenv() # Load environment variables from .env file

# --- Logging Configuration ---
DEBUG_MODE = os.environ.get("DEBUG_MODE", "false").lower() == "true"

def log_info(message):
    """Print an informational message with blue color"""
    print(f"{Fore.BLUE}[INFO] {message}{Style.RESET_ALL}")

def log_success(message):
    """Print a success message with green color"""
    print(f"{Fore.GREEN}[SUCCESS] {message}{Style.RESET_ALL}")

def log_warning(message):
    """Print a warning message with yellow color"""
    print(f"{Fore.YELLOW}[WARNING] {message}{Style.RESET_ALL}")

def log_error(message):
    """Print an error message with red color"""
    print(f"{Fore.RED}[ERROR] {message}{Style.RESET_ALL}")

def log_debug(message):
    """Print a debug message if debug mode is enabled"""
    if DEBUG_MODE:
        print(f"{Fore.CYAN}[DEBUG] {message}{Style.RESET_ALL}")

def log_step(step_description):
    """Print a step message"""
    print(f"{Fore.MAGENTA}>>> {step_description}{Style.RESET_ALL}")

def log_timing(operation, time_taken):
    """Print timing information"""
    print(f"{Fore.BLUE}[TIMING] {operation}: {time_taken:.2f}s{Style.RESET_ALL}")

def log_header(message):
    """Print a header message with background"""
    print(f"\n{Back.BLUE}{Fore.WHITE}{Style.BRIGHT} {message} {Style.RESET_ALL}\n")

# --- API Client Initialization ---
groq_client = None
grok_xai_client = None # Initialize xAI client variable

try:
    groq_api_key = os.environ.get("GROQ_API_KEY")
    if groq_api_key:
        groq_client = OpenAI(
            api_key=groq_api_key,
            base_url="https://api.groq.com/openai/v1"
        )
        log_success("Groq client initialized.")
    else:
        log_warning("GROQ_API_KEY not found in .env file. Groq models will be unavailable.")
except Exception as e:
    log_error(f"Error initializing Groq client: {e}")

try:
    xai_api_key = os.environ.get("XAI_API_KEY")
    if xai_api_key:
        # Use the standard OpenAI client structure for xAI Grok
        grok_xai_client = OpenAI(
            api_key=xai_api_key,
            base_url="https://api.x.ai/v1",
        )
        log_success("Grok (xAI) client initialized.")
    else:
        log_warning("XAI_API_KEY not found in .env file. Grok models will be unavailable.")
except Exception as e:
    log_error(f"Error initializing Grok (xAI) client: {e}")

# --- Model Selection ---
# Define models for each role in the flowchart
CLASSIFIER_PROVIDER = "groq" # Fast model for classification
CLASSIFIER_MODEL = "llama3-8b-8192"

PLANNER_PROVIDER = "groq"  # Groq for planning complex breakdowns
PLANNER_MODEL = "qwen-qwq-32b"

WORKER_PROVIDER = "grok"    # Grok model for detailed execution
WORKER_MODEL = "grok-3-mini-beta"

SYNTHESIZER_PROVIDER = "grok" # Grok for synthesis
SYNTHESIZER_MODEL = "grok-3-beta"

SIMPLE_QUERY_PROVIDER = "groq" # Fast model for simple answers
SIMPLE_QUERY_MODEL = "llama3-8b-8192"

TOOL_IDENTIFIER_PROVIDER = "groq" # Could be same as classifier
TOOL_IDENTIFIER_MODEL = "llama3-8b-8192"

# --- LLM Interaction Function ---
def call_llm(provider, model_name, messages, temperature=0.7, max_tokens=4000, response_format=None, attempt=1, max_attempts=3):
    """Unified function to call different LLM providers with retry logic."""
    client = None
    provider_name = provider.upper()

    if provider == "groq":
        client = groq_client
        provider_name = "Groq"
    elif provider == "grok":
        client = grok_xai_client
        provider_name = "Grok (xAI)"
    else:
        log_error(f"Attempted to use unsupported provider: {provider}")
        return f"Error: Unsupported provider '{provider}'."

    if not client:
        error_msg = f"Error: Attempted to call {provider_name} model '{model_name}', but the client is not initialized. Check API Key and initialization logs."
        log_error(error_msg)
        return f"Error: Client not available for provider '{provider}'."

    if attempt == 1:
        log_info(f"Calling {provider_name} model: {model_name} (Max Tokens: {max_tokens}, Temp: {temperature})")
    else:
        log_warning(f"Retrying {provider_name} model: {model_name} (Attempt {attempt}/{max_attempts})")

    log_debug(f"Messages ({len(messages)} items): {str(messages)[:500]}...")
    log_debug(f"Parameters: temperature={temperature}, max_tokens={max_tokens}" + (f", response_format={response_format}" if response_format else ""))

    start_time = time.time()
    try:
        # Prepare arguments, including response_format if specified
        request_args = {
            "model": model_name,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        if response_format:
             # Ensure response_format is a dictionary like {"type": "json_object"}
             if isinstance(response_format, dict) and "type" in response_format:
                 request_args["response_format"] = response_format
             else:
                 log_warning(f"Invalid response_format specified: {response_format}. Ignoring.")

        response = client.chat.completions.create(**request_args)

        finish_reason = getattr(response.choices[0], 'finish_reason', 'unknown')
        if finish_reason == 'length':
            log_warning(f"{provider_name} response may have been truncated due to max_tokens limit ({max_tokens}). Finish Reason: {finish_reason}")
        elif finish_reason not in ['stop', 'unknown', 'eos', 'tool_calls']: # Add 'tool_calls' if using OpenAI tools
             log_warning(f"{provider_name} response finished unexpectedly. Finish Reason: {finish_reason}")

        result = response.choices[0].message.content
        if result: # Ensure result is not None before stripping
             result = result.strip()
        else:
             # Handle cases where content might be None (e.g., tool use calls)
             log_warning(f"{provider_name} model {model_name} returned None content. Response obj: {response.choices[0].message}")
             result = "" # Return empty string or handle as needed

        log_timing(f"{provider_name} model {model_name} call", time.time() - start_time)
        return result

    except Exception as e:
        elapsed_time = time.time() - start_time
        log_error(f"Error calling {provider_name} model {model_name}: {str(e)}")
        log_timing(f"Failed {provider_name} model {model_name} call", elapsed_time)

        if attempt < max_attempts:
            retry_delay = 2 ** attempt
            log_info(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
            return call_llm(provider, model_name, messages, temperature, max_tokens, response_format, attempt + 1, max_attempts)
        else:
            log_error(f"Max retries reached for {provider_name} model {model_name}.")
            return f"Error: Failed to get response from {provider_name} ({model_name}) after {max_attempts} attempts. Last error: {str(e)}"


# --- Flowchart Step Implementations ---

def classify_query(query):
    """
    Corresponds to "Query Classification" step.
    Uses an LLM to determine the type of query (simple, complex, tool).
    """
    log_step("Query Classification")
    log_info("Classifying query type...")

    system_prompt = """You are a query classification expert. Analyze the user's query and determine its type based on complexity and potential need for external tools.

    Available query types:
    1.  **simple**: A straightforward question that can likely be answered directly with general knowledge (e.g., definitions, facts, simple explanations).
    2.  **complex**: A query requiring detailed explanation, multi-step reasoning, breakdown of topics, analysis, synthesis, or processing of substantial provided text (like study materials with 'Módulo' sections).
    3.  **tool**: A query that explicitly or implicitly requires an external tool like a web search (for current events, specific data), a calculator, code execution, or file access.

    INSTRUCTIONS:
    - Analyze the user query provided below.
    - Output ONLY a JSON object containing the classification.
    - The JSON object must have one key: "query_type", with the value being one of "simple", "complex", or "tool".

    Example 1:
    User Query: "What is the capital of France?"
    Output: {"query_type": "simple"}

    Example 2:
    User Query: "Explain the process of photosynthesis in detail, covering light-dependent and light-independent reactions."
    Output: {"query_type": "complex"}

    Example 3:
    User Query: "Módulo 1: Intro to Python\n- Variables\n- Data Types\nMódulo 2: Control Flow\n- If/Else\n- Loops\nGenerate detailed notes for these."
    Output: {"query_type": "complex"}

    Example 4:
    User Query: "What is the current weather in London?"
    Output: {"query_type": "tool"}

    Example 5:
    User Query: "Calculate 25 * (1.05)^10"
    Output: {"query_type": "tool"}

    Now, classify the following query:
    """
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"User Query: \"{query}\""}
    ]

    # Use a fast model and enforce JSON output
    response = call_llm(
        CLASSIFIER_PROVIDER,
        CLASSIFIER_MODEL,
        messages,
        temperature=0.1,
        max_tokens=100,
        response_format={"type": "json_object"} # Request JSON output
    )

    if response.startswith("Error:"):
        log_error(f"Query classification failed: {response}")
        return {"query_type": "complex", "error": "Classification failed"} # Default to complex on error

    try:
        # Clean potential markdown ```json ... ```
        cleaned_response = re.sub(r"^```json\s*|\s*```$", "", response, flags=re.MULTILINE).strip()
        result = json.loads(cleaned_response)
        if "query_type" in result and result["query_type"] in ["simple", "complex", "tool"]:
            log_success(f"Query classified as: {result['query_type']}")
            return result
        else:
            log_error(f"Classification response malformed: {response}")
            return {"query_type": "complex", "error": "Malformed classification"}
    except json.JSONDecodeError as e:
        log_error(f"Failed to parse classification JSON: {e}. Response was: {response}")
        return {"query_type": "complex", "error": "JSON parse error"}


def handle_simple_query(query):
    """
    Corresponds to "Handle Simple Query (Default Agent)" step.
    Uses a single LLM call to answer a straightforward question.
    """
    log_step("Handle Simple Query")
    log_info(f"Answering simple query directly...")

    system_prompt = "You are a helpful AI assistant. Provide a clear, concise, and accurate answer to the user's question."
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": query}
    ]

    response = call_llm(
        SIMPLE_QUERY_PROVIDER,
        SIMPLE_QUERY_MODEL,
        messages,
        temperature=0.7,
        max_tokens=1500 # Allow reasonable length for simple answers
    )

    # This function implicitly includes "Format Simple Response"
    if response.startswith("Error:"):
        log_error(f"Simple query handling failed: {response}")
        return f"Error: Could not process the simple query.\nDetails: {response}"
    else:
        log_success("Simple query answered.")
        return response


# --- Tool Path Functions (Placeholders) ---

def identify_tool_and_params(query):
    """
    Corresponds to "Identify Tool & Parameters (Tool Coordinator)".
    Placeholder: Determines which tool and extracts parameters.
    In a real implementation, this might use LLM function calling or regex.
    """
    log_step("Identify Tool & Parameters")
    log_warning("Tool identification is currently a PLACEHOLDER.")
    # Example: Simple check for keywords
    query_lower = query.lower()
    if "weather" in query_lower:
        # Basic location extraction (very naive)
        match = re.search(r"weather in ([\w\s]+)", query_lower)
        location = match.group(1).strip() if match else "unknown"
        log_info(f"Identified 'weather' tool needed for location: {location}")
        return {"tool_name": "weather", "parameters": {"location": location}}
    elif "calculate" in query_lower or re.search(r'\d+\s*[\+\-\*\/%^]', query):
         log_info("Identified 'calculator' tool needed.")
         return {"tool_name": "calculator", "parameters": {"expression": query}} # Pass full query for now
    else:
        log_info("No specific tool identified by placeholder logic.")
        return {"tool_name": None, "parameters": {}}

def execute_tool(tool_info):
    """
    Corresponds to "Execute Tool".
    Placeholder: Simulates executing the identified tool.
    """
    log_step("Execute Tool")
    tool_name = tool_info.get("tool_name")
    params = tool_info.get("parameters", {})
    log_warning(f"Tool execution for '{tool_name}' is currently a PLACEHOLDER.")

    if tool_name == "weather":
        location = params.get('location', 'unknown location')
        log_info(f"Simulating weather check for: {location}")
        # In reality, call a weather API here
        return f"Placeholder: The weather in {location} is simulated to be sunny and 20°C."
    elif tool_name == "calculator":
        expression = params.get('expression', 'invalid expression')
        log_info(f"Simulating calculation for: {expression}")
        # In reality, use a safe math evaluator like `eval` (with caution!) or a dedicated library
        try:
            # VERY UNSAFE - FOR DEMO ONLY - DO NOT USE IN PRODUCTION
            # result = eval(re.sub(r"[^0-9.+\-*/^() ]", "", expression)) # Basic sanitization attempt
            # Safer simulation:
            if "25 * (1.05)^10" in expression: result = 40.722 # Example precalc
            else: result = "Simulation cannot calculate this expression"
            return f"Placeholder: Calculation result for '{expression}' is: {result}"
        except Exception as e:
            return f"Placeholder: Error simulating calculation for '{expression}': {e}"
    else:
        return f"Placeholder: Tool '{tool_name}' is not implemented."

def process_tool_result(tool_output):
    """
    Corresponds to "Process Tool Result".
    Placeholder: Formats the raw tool output for the user.
    """
    log_step("Process Tool Result")
    log_info("Processing tool result (Placeholder Step)...")
    # In this placeholder setup, the output is already formatted text.
    # A real implementation might parse JSON, XML, etc.
    return tool_output # Pass through for now

# --- Complex Query Path Functions (Adapted from original code) ---

def extract_modules(query):
    """
    Parses a query for structured modules (like 'Módulo X: ...').
    Returns a dictionary {module_title: module_content} or None.
    (Used within the 'complex' path if structure is detected)
    """
    # log_info("Analyzing query for 'Módulo' structure...") # Called within process_complex_query now
    modules = {}
    current_module_title = None
    module_header_pattern = re.compile(r"^\s*M[oó]dulo\s+[\w\d]+.*?:?", re.IGNORECASE | re.MULTILINE)
    last_match_end = 0
    found_modules = False

    for match in module_header_pattern.finditer(query):
        found_modules = True
        if current_module_title:
            module_text = query[last_match_end:match.start()].strip()
            if module_text: modules[current_module_title] = module_text
        current_module_title = match.group(0).strip().rstrip(':').strip()
        last_match_end = match.end()

    if current_module_title:
        module_text = query[last_match_end:].strip()
        if module_text: modules[current_module_title] = module_text

    if modules:
        log_success(f"Extracted {len(modules)} modules from complex query.")
        return modules
    else:
        log_info("No 'Módulo' structure found in complex query. Treating as single block.")
        return None

def plan_tasks(content_block, context_title):
    """
    Corresponds to "Plan Task Decomposition (Planner Agent)".
    Uses the Planner LLM to break down content into sub-tasks.
    """
    log_info(f"Planning tasks for: '{context_title}'")
    content_preview = content_block[:200] + "..." if len(content_block) > 200 else content_block
    log_debug(f"Content preview: {content_preview}")

    system_prompt = f"""You are an expert academic planner AI. Break down the provided content for "{context_title}" into a list of logical, actionable sub-tasks (2-7) for detailed explanation. Base tasks *only* on the provided content. Phrase tasks clearly (e.g., "Explain X using details provided."). Output ONLY the numbered list of tasks, each on a new line.

Provided Content for "{context_title}":
------- CONTENT START -------
{content_block}
------- CONTENT END -------

Generate the task list now."""
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Generate the sub-tasks for the content related to '{context_title}'."}
    ]

    start_time = time.time()
    response = call_llm(PLANNER_PROVIDER, PLANNER_MODEL, messages, temperature=0.2, max_tokens=1500)

    if response.startswith("Error:"):
        log_error(f"Planner LLM failed for '{context_title}': {response}")
        return []

    tasks = []
    matches = re.findall(r"^\s*\d+[.)]?\s*(.*)", response, re.MULTILINE)
    if matches:
        tasks = [match.strip() for match in matches if match.strip()]
    else:
        potential_tasks = [line.strip() for line in response.split('\n') if line.strip()]
        tasks = [pt for pt in potential_tasks if re.match(r"^\s*(\d+|[*\-+•])\s*.", pt)]

    if tasks:
        log_success(f"Generated {len(tasks)} tasks for '{context_title}'.")
    else:
        log_error(f"Failed to parse tasks from planner response for '{context_title}'. Response: {response[:500]}...")
        return []

    log_timing(f"Task planning for '{context_title}'", time.time() - start_time)
    return tasks

def execute_subtask(subtask, context_title, full_content):
    """
    Corresponds to one task in "Execute Sub-Tasks in Parallel (Agent Pool)".
    Uses a Worker LLM to execute a single sub-task IN DETAIL.
    """
    log_debug(f"Executing subtask: '{subtask[:60]}...' (Context: '{context_title}')")
    content_snippet = full_content[:3000] + ("..." if len(full_content) > 3000 else "")

    system_prompt = f"""You are an expert AI assistant providing highly detailed explanations for sub-topics.

Overall Context: "{context_title}"
Relevant Content Snippet:
\"\"\"
{content_snippet}
\"\"\"
Your Assigned Sub-Task: "{subtask}"

INSTRUCTIONS:
- Focus EXCLUSIVELY on the sub-task: "{subtask}".
- Provide a comprehensive, in-depth explanation using the snippet and your general knowledge.
- Include definitions, steps, examples, justifications, nuances. Aim for academic depth.
- Structure logically using Markdown (headings, bold, lists).
- Begin directly. No filler. Ensure output is detailed and self-contained for this sub-task.
"""
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Provide a detailed explanation for the sub-task: {subtask}"}
    ]

    start_time = time.time()
    result = call_llm(WORKER_PROVIDER, WORKER_MODEL, messages, temperature=0.6, max_tokens=6000)

    if result.startswith("Error:"):
        log_error(f"Worker LLM failed for subtask '{subtask[:60]}...': {result}")
    else:
        log_success(f"Completed subtask: '{subtask[:60]}...' ({len(result)} chars)")

    log_timing(f"Subtask execution for '{subtask[:30]}...'", time.time() - start_time)
    return (subtask, result) # Return tuple

def synthesize_results(subtask_results_dict, context_title, original_content):
    """
    Corresponds to "Synthesize & Structure Results (Synthesizer Agent)".
    Combines worker results for a content block. Implicitly handles "Format Complex Response".
    """
    log_info(f"Synthesizing results for: '{context_title}'")

    if not subtask_results_dict:
        log_warning(f"No sub-task results provided for synthesis of '{context_title}'.")
        return f"## {context_title}\n\nError: No sub-task results were generated."

    successful_results = {task: result for task, result in subtask_results_dict.items() if not result.startswith("Error:")}
    failed_tasks = {task: result for task, result in subtask_results_dict.items() if result.startswith("Error:")}

    if not successful_results:
        log_error(f"All sub-tasks failed for '{context_title}'. Cannot synthesize.")
        error_report = "\n".join([f"- Task: {task}\n  Error: {result}" for task, result in failed_tasks.items()])
        return f"## {context_title}\n\n**Synthesis Failed: All sub-tasks encountered errors.**\n\n{error_report}"

    log_debug(f"Synthesizing {len(successful_results)} successful results ({sum(len(r) for r in successful_results.values())} chars)")
    if failed_tasks:
        log_warning(f"{len(failed_tasks)} sub-tasks failed and will be excluded from synthesis for '{context_title}'.")

    worker_results_text = ""
    for i, (task, result) in enumerate(successful_results.items()):
        worker_results_text += f"### Contribution for Sub-Task: {task}\n\n{result}\n\n---\n\n"

    content_preview = original_content[:500] + "..." if len(original_content) > 500 else original_content

    system_prompt = f"""You are an expert AI synthesizer and editor. Assemble a comprehensive, coherent explanation for "{context_title}" using detailed inputs from its sub-tasks.

Original Content Outline (Briefly): "{content_preview}"
Detailed Sub-Task Results:
------- RESULTS START -------
{worker_results_text}
------- RESULTS END -------

GOAL: Synthesize inputs into a single, well-organized document explaining "{context_title}".

INSTRUCTIONS:
1.  Start with a main heading: `# {context_title}`.
2.  Preserve core detail from *each* sub-task result. Integrate, don't over-summarize.
3.  Organize logically using sub-headings based on tasks/concepts. Ensure smooth flow.
4.  Combine related info, add brief transitions *only if needed*. Refine phrasing slightly for flow, keep detail.
5.  Eliminate obvious, direct repetition *across* inputs. Do not remove detail *within* an input.
6.  Use Markdown extensively (headings, lists, bold).
7.  Ensure output covers all successful sub-task concepts.
8.  Output must be a self-contained explanation of "{context_title}". No meta-commentary.

Synthesize the results now.
"""
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Synthesize the provided detailed results into a single, cohesive explanation for '{context_title}', ensuring all details are preserved and logically structured."}
    ]

    start_time = time.time()
    result = call_llm(SYNTHESIZER_PROVIDER, SYNTHESIZER_MODEL, messages, temperature=0.7, max_tokens=8000)

    if result.startswith("Error:"):
        log_error(f"Synthesizer LLM failed for '{context_title}': {result}")
        fallback_results_text = "\n\n---\n\n".join([f"### Sub-Task: {t}\n\n{r}" for t, r in successful_results.items()])
        if failed_tasks:
             fallback_results_text += "\n\n---\n\n**Failed Tasks:**\n" + "\n".join([f"- {t}: {r}" for t, r in failed_tasks.items()])
        return f"# {context_title}\n\n**Error: Failed to synthesize the results.**\n\n**Detailed Sub-Task Results (Successful):**\n\n{fallback_results_text}"
    else:
        log_success(f"Synthesis complete for '{context_title}' ({len(result)} chars)")

    log_timing(f"Synthesis for '{context_title}'", time.time() - start_time)
    return result


def process_complex_block(content, title):
    """
    Manages the Plan -> Execute -> Synthesize pipeline for a single block
    within the 'complex' query path.
    """
    log_header(f"Processing Complex Block: {title}")
    block_start_time = time.time()

    if not content or not content.strip():
        log_warning(f"Skipping complex block '{title}' due to empty content.")
        return f"\n## {title}\n\n*Skipped: Content was empty.*\n"

    # Step: Plan Tasks
    log_step(f"Plan Task Decomposition for '{title}'")
    tasks = plan_tasks(content, title)

    if not tasks:
        log_error(f"Planning failed for '{title}'. Skipping further processing.")
        log_timing(f"Block '{title}' (failed at planning)", time.time() - block_start_time)
        return f"\n## {title}\n\n*Error: Failed to generate sub-tasks for this section.*\n"

    # Step: Execute Sub-tasks in Parallel
    log_step(f"Execute {len(tasks)} Sub-Tasks in Parallel for '{title}'")
    exec_start_time = time.time()
    sub_task_results = {}
    max_concurrent_workers = min(len(tasks), 5)

    with tqdm.tqdm(total=len(tasks), desc=f"Executing Tasks ({title[:20]}..)", unit="task", leave=False) as progress_bar:
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrent_workers) as executor:
            future_to_task = {executor.submit(execute_subtask, task, title, content): task for task in tasks}
            for future in concurrent.futures.as_completed(future_to_task):
                task_from_future = future_to_task[future]
                try:
                    subtask, result_text = future.result()
                    sub_task_results[subtask] = result_text
                except Exception as exc:
                    log_error(f'Sub-task "{task_from_future[:50]}..." generated exception: {exc}')
                    sub_task_results[task_from_future] = f"Error: Execution generated exception - {exc}"
                finally:
                    progress_bar.update(1)

    log_timing(f"Sub-task parallel execution for '{title}'", time.time() - exec_start_time)

    ordered_results = {task: sub_task_results.get(task, f"Error: Result missing for task '{task}'") for task in tasks}
    successful_tasks = sum(1 for result in ordered_results.values() if not result.startswith("Error:"))
    failed_tasks_count = len(tasks) - successful_tasks
    log_info(f"Execution summary for '{title}': {successful_tasks} succeeded, {failed_tasks_count} failed.")

    # Step: Synthesize Results
    log_step(f"Synthesize & Structure Results for '{title}'")
    synthesized_response = synthesize_results(ordered_results, title, content)

    log_timing(f"Total complex block processing for '{title}'", time.time() - block_start_time)
    # This synthesized response implicitly includes "Format Complex Response"
    return synthesized_response


# --- Main Orchestration Logic ---
def main():
    log_header("Multi-Agent Study Assistant (Flowchart Adapted)")
    print(f"{Style.BRIGHT}Enter your query (simple question, complex topic, 'Módulo' structure, or tool request).{Style.RESET_ALL}")
    print(f"{Style.BRIGHT}Type 'quit' to exit or '###' on a new line to finish input.{Style.RESET_ALL}")

    while True:
        # Advanced multi-line input interface for handling large pasted content
        print(f"\n{Fore.GREEN}{Style.BRIGHT}User Query:{Style.RESET_ALL}")
        print(f"{Back.BLACK}{Fore.GREEN}▼ BEGIN INPUT (type '###' on a new line to finish) {Style.RESET_ALL}")
        
        user_query_lines = []
        try:
            # Read until sentinel value ### is encountered
            while True:
                line = input()
                if line.strip() == "###":
                    break
                if line.strip().lower() == "quit" and not user_query_lines:
                    raise EOFError  # Simulate EOF to exit
                user_query_lines.append(line)
        except EOFError:
            log_info("\nEOF detected, exiting.")
            break
        
        user_query = "\n".join(user_query_lines).strip()
        print(f"{Back.BLACK}{Fore.GREEN}▲ END INPUT {Style.RESET_ALL}")
        
        if user_query.lower() == 'quit':
            log_info("Exiting application")
            break
        if not user_query:
            log_warning("Empty query received. Please try again.")
            continue
        
        # Process the query
        log_header("Processing Query")
        overall_start_time = time.time()
        final_response = ""  # Initialize final response
        
        # Step: Query Classification
        classification_result = classify_query(user_query)
        query_type = classification_result.get("query_type", "complex") # Default to complex on error

        # Decision Point: Query Type?
        if query_type == "simple":
            # Path: Simple Query
            final_response = handle_simple_query(user_query)
            # Includes "Format Simple Response" implicitly

        elif query_type == "tool":
            # Path: Tool Use
            tool_info = identify_tool_and_params(user_query)
            if tool_info and tool_info.get("tool_name"):
                tool_output = execute_tool(tool_info)
                final_response = process_tool_result(tool_output)
                # Includes "Format Tool Response" implicitly
            else:
                log_error("Tool identified, but specific tool or parameters could not be determined by placeholder logic. Treating as complex.")
                # Fallback to complex processing if tool identification fails
                query_type = "complex" # Re-route
                final_response = process_complex_block(user_query, "Overall Query Analysis (Tool Fallback)")


        # Handle Complex as default or fallback
        if query_type == "complex":
             # Path: Complex Agentic Workflow
            log_step("Complex Query Path Initiated")
            # Check for module structure within the complex query
            modules = extract_modules(user_query)

            if modules:
                # Process structured complex query (multiple modules)
                log_info(f"Processing complex query with {len(modules)} identified modules.")
                final_response_parts = []
                module_num = 0
                for module_title, module_content in modules.items():
                    module_num += 1
                    # Each block goes through Plan -> Execute -> Synthesize
                    module_result = process_complex_block(module_content, module_title)
                    final_response_parts.append(module_result)

                # Combine module results
                log_step("Prepare Final Response Package (Combine Modules)")
                query_title = user_query.split('\n')[0][:60].strip()
                final_response_header = f"# Study Guide: {query_title}...\n\n"
                final_response = final_response_header + "\n\n---\n\n".join(final_response_parts)
                log_success(f"Combined {len(final_response_parts)} module results.")
            else:
                # Process unstructured complex query (single block)
                log_info("Processing the entire query as a single complex block.")
                query_title = "Overall Query Analysis"
                final_response = process_complex_block(user_query, query_title)
                # Synthesizer should add title, but add fallback header if needed
                if not final_response.startswith("#") and not final_response.startswith("Error:"):
                     query_preview = user_query[:60].strip()
                     final_response = f"# Study Notes: {query_preview}...\n\n" + final_response

        # Final Step: Deliver Response to User
        log_step("Deliver Response to User")
        overall_end_time = time.time()
        total_time = overall_end_time - overall_start_time
        log_timing("Total query processing", total_time)

        log_header("Study Assistant Response")
        print(f"{Fore.CYAN}{Style.BRIGHT}Response ({len(final_response)} chars, generated in {total_time:.2f}s via '{query_type}' path):{Style.RESET_ALL}")
        print(final_response)
        print(f"\n{Back.GREEN}{Fore.BLACK} COMPLETED {Style.RESET_ALL} Ready for next query.")


if __name__ == "__main__":
    # Check for required packages
    missing_packages = []
    # ... (package checking code remains the same as original) ...
    try: import colorama
    except ImportError: missing_packages.append("colorama")
    try: import tqdm
    except ImportError: missing_packages.append("tqdm")
    try: import openai
    except ImportError: missing_packages.append("openai")
    try: import dotenv
    except ImportError: missing_packages.append("python-dotenv")

    if missing_packages:
        print("\n" + "!"*60)
        print(f"{Fore.RED}[ERROR] Required Python packages are missing:{Style.RESET_ALL}")
        for pkg in missing_packages: print(f"  - {pkg}")
        print("\nPlease install them using pip:")
        print(f"  {Fore.YELLOW}pip install {' '.join(missing_packages)}{Style.RESET_ALL}")
        print("!"*60 + "\n")
        sys.exit(1)

    print(f"\n{Back.MAGENTA}{Fore.WHITE}{Style.BRIGHT} Multi-Agent Study Assistant (Flowchart Adapted) {Style.RESET_ALL}")
    print(f"{Fore.CYAN}Query Classification -> [Simple | Tool | Complex (Plan->Execute->Synthesize)]{Style.RESET_ALL}\n")

    # Check for necessary API keys based on initialized clients
    keys_ok = True
    # Determine all providers actually used across different roles
    providers_used = {
        CLASSIFIER_PROVIDER, PLANNER_PROVIDER, WORKER_PROVIDER,
        SYNTHESIZER_PROVIDER, SIMPLE_QUERY_PROVIDER, TOOL_IDENTIFIER_PROVIDER
    }

    if "groq" in providers_used and groq_client is None:
        log_error("Groq provider is configured for one or more roles, but client failed to initialize (Check GROQ_API_KEY).")
        keys_ok = False
    if "grok" in providers_used and grok_xai_client is None:
        log_error("Grok (xAI) provider is configured for one or more roles, but client failed to initialize (Check XAI_API_KEY).")
        keys_ok = False
    # Add checks for other potential providers if introduced

    if keys_ok:
        log_success("API clients for configured providers initialized successfully (or keys were not needed/found).")
        log_info(f"Debug mode: {'Enabled' if DEBUG_MODE else 'Disabled'}")
        print(f"\n{Back.GREEN}{Fore.BLACK}{Style.BRIGHT} READY {Style.RESET_ALL}")
        main()
    else:
        log_error("Exiting due to missing API keys or client initialization errors for configured providers.")
        print(f"\n{Back.RED}{Fore.WHITE}{Style.BRIGHT} CONFIGURATION ERROR {Style.RESET_ALL}")
        sys.exit(1)

# --- END OF FILE code_adapted.py ---