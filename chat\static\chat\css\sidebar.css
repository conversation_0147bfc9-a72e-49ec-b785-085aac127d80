/* sidebar.css - Styles for the sidebar component */

/* Hover area for sidebar activation */
.hover-area {
    position: fixed;
    left: 0;
    top: 0;
    width: 80px;
    height: 100vh;
    z-index: 99;
    transition: var(--transition-theme);
    pointer-events: auto;
    background-color: transparent;
}

.hover-area::after {
    content: '';
    position: absolute;
    top: 50%;
    left: var(--spacing-sm);
    transform: translateY(-50%);
    width: 4px;
    height: 40px;
    background-color: rgba(var(--text-primary-rgb), 0.1);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-theme);
}

.hover-area:hover::after {
    background-color: rgba(var(--accent-primary-rgb), 0.3);
    height: 60px;
}

.light-theme .hover-area::after {
    background-color: rgba(var(--text-primary-rgb), 0.05);
}

.light-theme .hover-area:hover::after {
    background-color: rgba(var(--accent-primary-rgb), 0.3);
}

/* Main sidebar container */
.sidebar {
    position: fixed;
    left: -320px;
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    top: 50%;
    transform: translateY(-50%);
    width: 280px;
    height: 600px;
    display: flex;
    flex-direction: column;
    z-index: 1000;
    transition-property: left, opacity, transform, box-shadow;
    transition-duration: 0.8s;
    transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    transition-delay: 0.6s;
    scrollbar-width: none;
    scrollbar-color: var(--accent-primary);
    box-shadow: var(--card-shadow);
    pointer-events: auto;
    padding: var(--spacing-md);
    overflow: hidden; /* Changed from overflow-y: auto to hidden since content handles scrolling */
    opacity: 0.9;
}

/* Light theme sidebar */
.light-theme .sidebar {
    background: rgba(var(--bg-secondary-rgb), 0.85);
    border: 1px solid rgba(var(--accent-primary-rgb), 0.3);
    border-left: none;
    box-shadow: var(--card-shadow);
    color: var(--text-primary);
}

/* Sidebar visibility class */
.sidebar.visible {
    box-shadow: var(--card-shadow);
    transform: translateY(-50%) translateX(5px);
    transition-property: left, opacity, transform, box-shadow;
    transition-duration: 0.6s;
    transition-timing-function: cubic-bezier(0.34, 1.56, 0.64, 1);
    transition-delay: 0s;
    left: 0;
    opacity: 1;
}

/* Collapsing animation class */
.sidebar.collapsing {
    transition-property: left, opacity, transform, box-shadow;
    transition-duration: 0.8s;
    transition-timing-function: cubic-bezier(0.34, 0.15, 0.45, 0.91);
    transition-delay: 0s;
    opacity: 0.7;
    transform: translateY(-50%) translateX(-10px);
}

/* Sidebar header */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.sidebar-title {
    font-size: var(--font-size);
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.light-theme .sidebar-header {
    border-bottom: 1px solid var(--border-color);
}

.light-theme .sidebar-title {
    color: var(--text-primary);
}

/* Sidebar navigation */
.sidebar ul {
    list-style: none;
    padding: 0;
}

.sidebar ul li {
    margin-bottom: var(--spacing-sm);
}

.sidebar ul li a {
    text-decoration: none;
    color: var(--text-primary);
    display: flex;
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
}

.sidebar ul li a i {
    margin-right: var(--spacing-sm);
}

/* Sidebar sections */
.sidebar-section h4 {
    color: var(--text-dimmed);
    font-size: var(--font-size-sm);
    margin: var(--spacing-md) 0 var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Sidebar content - main scrollable area */
.sidebar-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

/* Sidebar footer - contains delete all button */
.sidebar-footer {
    margin-top: auto;
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
}

/* Light theme sidebar footer */
.light-theme .sidebar-footer {
    border-top: 1px solid var(--border-color);
}

/* Delete all button */
.delete-all-btn {
    width: 100%;
    padding: var(--spacing-sm);
    background-color: rgba(var(--danger-color-rgb), 0.1);
    border: 1px solid rgba(var(--danger-color-rgb), 0.3);
    border-radius: var(--border-radius);
    color: var(--danger-color);
    font-size: var(--font-size-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    transition: var(--transition-theme);
}

.delete-all-btn:hover {
    background-color: rgba(var(--danger-color-rgb), 0.2);
    border-color: rgba(var(--danger-color-rgb), 0.5);
    transform: translateY(-1px);
}

.delete-all-btn:active {
    transform: translateY(0);
}

.delete-all-btn i {
    font-size: 1rem;
}

.delete-all-btn span {
    font-weight: 500;
}

/* Light theme delete all button */
.light-theme .delete-all-btn {
    background-color: rgba(var(--danger-color-rgb), 0.05);
    border: 1px solid rgba(var(--danger-color-rgb), 0.2);
    color: var(--danger-color);
}

.light-theme .delete-all-btn:hover {
    background-color: rgba(var(--danger-color-rgb), 0.1);
    border-color: rgba(var(--danger-color-rgb), 0.3);
}

/* Chats container */
.chats-container {
    flex: 1;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--text-dimmed) transparent;
}

.chats-container::-webkit-scrollbar {
    width: 6px;
}

.chats-container::-webkit-scrollbar-thumb {
    background-color: var(--text-dimmed);
    border-radius: var(--border-radius-sm);
}

/* Chat instance */
.chat-instance {
    box-sizing: border-box;
    overflow-wrap: break-word;
    word-break: break-word;
    padding: var(--spacing-xs) var(--spacing-sm);
    cursor: pointer;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-xs);
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    transition: var(--transition-theme);
    color: var(--text-secondary);
    position: relative;
}

.chat-instance:hover {
    background-color: rgba(var(--accent-primary-rgb), 0.1);
    border-color: rgba(var(--accent-primary-rgb), 0.2);
}

/* Light theme chat instance */
.light-theme .chat-instance {
    color: var(--text-secondary);
}

.light-theme .chat-instance:hover {
    background-color: rgba(var(--accent-primary-rgb), 0.05);
    border-color: rgba(var(--accent-primary-rgb), 0.15);
}

.chat-instance-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.chat-info {
    flex: 1;
    overflow: hidden;
    min-width: 0;
    padding-right: var(--spacing-xs);
}

.chat-info p {
    margin: 0;
    white-space: nowrap;
    font-size: var(--font-size-xs);
    color: var(--text-dimmed);
    overflow: hidden;
    text-overflow: ellipsis;
}

.light-theme .chat-info p {
    color: var(--text-dimmed);
}

.chat-date {
    font-size: var(--font-size-xs);
    color: var(--text-dimmed);
    opacity: 0.8;
}

.light-theme .chat-date {
    color: var(--text-dimmed);
}

.chat-icon {
    margin-right: 8px;
    color: rgba(255, 255, 255, 0.6);
    width: 16px; /* Fixed width */
    display: flex;
    align-items: center;
    justify-content: center;
}

.light-theme .chat-icon {
    color: rgba(0, 0, 0, 0.6);
}

/* Chat actions container */
.chat-actions {
    display: flex;
    gap: 5px;
    margin-left: 5px; /* Ensure consistent spacing */
    width: 50px; /* Fixed width to prevent layout shifts */
    justify-content: flex-end;
}

/* Rename chat button */
.rename-chat-btn {
    opacity: 0;
    background: none;
    border: none;
    font-size: 0.8rem;
    color: var(--text-dimmed);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.3s ease;
}

.rename-chat-btn:hover {
    color: var(--accent-primary);
}

/* Delete chat button */
.delete-chat-btn {
    opacity: 0;
    background: none;
    border: none;
    font-size: 0.8rem;
    color: var(--text-dimmed);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.3s ease;
}

.delete-chat-btn:hover {
    color: var(--danger-color);
}

.chat-instance:hover .delete-chat-btn,
.chat-instance:hover .rename-chat-btn {
    opacity: 1;
}

/* Chat rename input */
.chat-rename-input {
    width: 100%;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--accent-primary);
    background-color: rgba(30, 30, 30, 0.7);
    color: var(--text-primary);
    font-size: 0.8rem;
    outline: none;
}

.light-theme .chat-rename-input {
    background-color: rgba(var(--bg-primary-rgb), 0.9);
    border: 1px solid var(--accent-primary);
}

.chat-rename-input:focus {
    border-color: var(--accent-secondary);
    box-shadow: 0 0 0 2px rgba(var(--accent-primary-rgb), 0.2);
}

/* Fixed styles for all screen sizes */
.chat-info p {
    max-width: 120px;
}

/* Custom confirmation dialog */
.confirmation-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--bg-primary-rgb), 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1100;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.light-theme .confirmation-dialog {
    background-color: rgba(var(--bg-primary-rgb), 0.4);
}

.confirmation-dialog.active {
    opacity: 1;
    visibility: visible;
}

.confirmation-content {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 400px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transform: translateY(20px);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), background-color var(--transition-theme), border-color var(--transition-theme);
    animation: confirmationSlideIn 0.3s ease forwards;
}

.light-theme .confirmation-content {
    background-color: var(--bg-secondary);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.confirmation-dialog.active .confirmation-content {
    transform: translateY(0);
}

.confirmation-header {
    padding: 20px 20px 0 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.confirmation-icon {
    color: var(--warning-color);
    font-size: 1.5rem;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(var(--warning-color-rgb), 0.1);
    border-radius: 50%;
    flex-shrink: 0;
}

.confirmation-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.confirmation-body {
    padding: 12px 20px 20px 20px;
}

.confirmation-body p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
}

.confirmation-actions {
    padding: 15px 20px 20px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.confirmation-btn {
    padding: 8px 16px;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    outline: none;
}

.confirmation-btn.cancel {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-dimmed);
}

.confirmation-btn.cancel:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--accent-primary);
}

.confirmation-btn.confirm {
    background-color: var(--danger-color);
    color: var(--text-white);
    border: 1px solid var(--danger-color);
}

.confirmation-btn.confirm:hover {
    background-color: rgba(var(--danger-color-rgb), 0.9);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(var(--danger-color-rgb), 0.3);
}

.confirmation-btn.confirm:active {
    transform: translateY(0);
}

/* Light theme specific styles */
.light-theme .confirmation-btn.cancel {
    background-color: transparent;
    border-color: var(--border-color);
    color: var(--text-dimmed);
}

.light-theme .confirmation-btn.cancel:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.light-theme .confirmation-btn.confirm {
    background-color: var(--danger-color);
    color: white;
}

.light-theme .confirmation-btn.confirm:hover {
    background-color: rgba(var(--danger-color-rgb), 0.9);
}

/* Error state styles */
.confirmation-btn.confirm.error {
    background-color: var(--danger-color);
    color: var(--text-white);
    border: 1px solid var(--danger-color);
}

.confirmation-btn.confirm.error:hover {
    background-color: rgba(var(--danger-color-rgb), 0.9);
}

/* Focus states for accessibility */
.confirmation-btn:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

.confirmation-btn.confirm:focus {
    outline-color: var(--danger-color);
}

.light-theme .confirmation-btn:focus {
    outline-color: var(--accent-primary);
}

.light-theme .confirmation-btn.confirm:focus {
    outline-color: var(--danger-color);
}

/* Animation keyframes */
@keyframes confirmationSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive design for confirmation dialog */
@media (max-width: 480px) {
    .confirmation-content {
        width: 95%;
        margin: 0 10px;
    }
    
    .confirmation-header {
        padding: 16px 16px 0 16px;
    }
    
    .confirmation-body {
        padding: 10px 16px 16px 16px;
    }
    
    .confirmation-actions {
        padding: 12px 16px 16px 16px;
        flex-direction: column;
        gap: 8px;
    }
    
    .confirmation-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Prevent body scroll when dialog is open */
body.confirmation-open {
    overflow: hidden;
}

/* Enhanced backdrop blur effect */
.confirmation-dialog::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.confirmation-content {
    position: relative;
    z-index: 1;
}

/* Improved animation timing */
.confirmation-dialog {
    animation-duration: 0.4s;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.confirmation-dialog.active .confirmation-content {
    animation: confirmationBounceIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* More sophisticated entrance animation */
@keyframes confirmationBounceIn {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.8);
    }
    60% {
        opacity: 1;
        transform: translateY(-5px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Button ripple effect */
.confirmation-btn {
    position: relative;
    overflow: hidden;
}

.confirmation-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.confirmation-btn:active::after {
    width: 100px;
    height: 100px;
}
