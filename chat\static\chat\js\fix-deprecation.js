/**
 * fix-deprecation.js - Fixes deprecation warnings in the console
 * 
 * This script addresses the DOMNodeInserted deprecation warning by
 * overriding the addEventListener method for these specific events.
 */

// Execute when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
  // Store the original addEventListener method
  const originalAddEventListener = Element.prototype.addEventListener;
  
  // Override the addEventListener method
  Element.prototype.addEventListener = function(type, listener, options) {
    // If the event type is one of the deprecated mutation events, use MutationObserver instead
    if (type === 'DOMNodeInserted' || type === 'DOMNodeRemoved' || type === 'DOMAttrModified') {
      console.log('Deprecated event listener intercepted:', type);
      
      // For select2 and similar libraries, we'll create a MutationObserver instead
      const element = this;
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if ((type === 'DOMNodeInserted' && mutation.type === 'childList' && mutation.addedNodes.length > 0) ||
              (type === 'DOMNodeRemoved' && mutation.type === 'childList' && mutation.removedNodes.length > 0) ||
              (type === 'DOMAttrModified' && mutation.type === 'attributes')) {
            // Create a synthetic event
            const event = new Event(type);
            // Add relevant properties
            if (type === 'DOMNodeInserted' && mutation.addedNodes.length > 0) {
              event.relatedNode = mutation.target;
              event.target = mutation.addedNodes[0];
            } else if (type === 'DOMNodeRemoved' && mutation.removedNodes.length > 0) {
              event.relatedNode = mutation.target;
              event.target = mutation.removedNodes[0];
            } else if (type === 'DOMAttrModified') {
              event.attrName = mutation.attributeName;
              event.prevValue = null; // We can't know the previous value
              event.newValue = element.getAttribute(mutation.attributeName);
            }
            // Call the original listener
            listener.call(element, event);
          }
        });
      });
      
      // Start observing
      observer.observe(element, {
        childList: type === 'DOMNodeInserted' || type === 'DOMNodeRemoved',
        attributes: type === 'DOMAttrModified',
        subtree: false
      });
      
      // Store the observer in a WeakMap to prevent memory leaks
      if (!window._mutationObservers) {
        window._mutationObservers = new WeakMap();
      }
      if (!window._mutationObservers.has(element)) {
        window._mutationObservers.set(element, {});
      }
      window._mutationObservers.get(element)[type] = observer;
      
      return;
    }
    
    // For all other event types, use the original method
    return originalAddEventListener.call(this, type, listener, options);
  };
  
  console.log('Fixed DOMNodeInserted deprecation warning');
});
