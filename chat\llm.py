"""Módulo principal para la integración con modelos de lenguaje.

Este módulo proporciona funciones para interactuar con diferentes proveedores de LLM
y gestionar la memoria de conversación.
"""
import logging
from typing import Dict, Union
from langchain.chains.conversation.memory import ConversationBufferWindowMemory

# Importar desde el nuevo sistema de proveedores
from .llm_providers.memory import serialize_memory, deserialize_memory

logger = logging.getLogger(__name__)

# Nota: Ya no usamos una variable global llm_provider
# En su lugar, usamos el servicio LLM para cada solicitud

def query_llm(user_input: str, memory_instance: ConversationBufferWindowMemory,
              request_type: str = "standard_query", image_path: str = None) -> Union[str, Dict]:
    """Consulta el modelo de lenguaje configurado.

    Args:
        user_input: Entrada del usuario
        memory_instance: Instancia de memoria de conversación
        request_type: Tipo de solicitud ("standard_query", "generate_test" o "multimodal_query")
        image_path: Ruta a la imagen para consultas multimodales

    Returns:
        str | dict: Respuesta del modelo como texto o JSON para exámenes
    """
    try:
        # Usar el servicio LLM para consultar el modelo
        from .services.llm_service import query_llm as service_query_llm
        from .llm_providers.context import LLMRequestContext

        # Crear un contexto con la configuración predeterminada
        context = LLMRequestContext()

        # Llamar al servicio
        return service_query_llm(context, user_input, memory_instance, request_type, image_path)
    except Exception as e:
        logger.error(f"Error al consultar el LLM: {e}")
        return {
            "response_type": "text",
            "data": "Error: No se pudo procesar tu solicitud."
        }

# Alias para mantener compatibilidad con código existente
query_grok = query_llm

# Exportar las funciones de memoria
__all__ = ['query_llm', 'query_grok', 'serialize_memory', 'deserialize_memory', 'llm_provider']