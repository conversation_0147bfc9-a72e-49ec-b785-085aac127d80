This is a Django-based AI chat application called "Chat Agent" (also referred to as "EduChat" in some templates) with the following key features:

LLM Integration:
Supports multiple AI providers: Grok (xAI) and Groq
Uses LangChain for conversation management
Implements a provider factory pattern for easy LLM switching
Technical Stack:
Backend: Django 5.1+
Database: MySQL
Frontend: Vanilla JavaScript with modular architecture
Styling: SCSS/CSS with a comprehensive component system
Dependencies managed via pip and npm
Key Features:
User authentication system
Chat history management
Code highlighting and terminal-style code blocks
Speech recognition capabilities
Responsive design with visual effects
Test/exam functionality
Architecture:
Modular design with integrated chat and authentication functionality
Clean separation between LLM providers
Comprehensive frontend module system
Environment-based configuration
The project appears to be an educational platform that provides AI-powered chat assistance to students, with particular attention to code display and formatting capabilities.
