/* exam.css - Styles for interactive exams */

/* Exam suggestion banner */
.exam-suggestion-banner {
  background: rgba(var(--accent-primary-rgb), 0.15);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  margin: 0.5rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid rgba(var(--accent-primary-rgb), 0.3);
}

.exam-suggestion-banner span {
  color: var(--text-primary);
  font-weight: 500;
}

.exam-suggestion-banner button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 0.5rem;
  color: var(--text-dimmed);
}

.exam-suggestion-banner button:hover {
  color: var(--danger-color);
}

/* Exam container */
.exam-container {
  background: rgba(var(--bg-tertiary-rgb), 0.5);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  margin: 1rem 0;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  width: 100%;
  min-width: 100%;
}

/* Make bot messages with exams use full width */
.bot-message .exam-container {
  width: 100%;
}

.chat-message.bot-message:has(.exam-container) {
  width: 100%;
  max-width: 100%;
}

.exam-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 600;
}

.exam-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

/* Questions */
.exam-question {
  margin: 1.5rem 0;
  padding: 1.2rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: rgba(var(--bg-secondary-rgb), 0.5);
  transition: all 0.3s ease;
}

.question-text {
  font-weight: 500;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

/* Options */
.exam-options {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.exam-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: var(--border-radius-sm);
  transition: background-color 0.2s ease;
}

.exam-option:hover {
  background: rgba(var(--bg-quaternary-rgb), 0.3);
}

.exam-option input[type="radio"] {
  margin: 0;
  cursor: pointer;
}

.exam-option label {
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 1rem;
  flex: 1;
}

/* Grade button */
.grade-exam-btn {
  margin-top: 1.5rem;
  padding: 0.7rem 1.5rem;
  background: var(--background-primary);
  color: var(--bg-primary);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.2s ease;
  min-width: 200px;
}

.grade-exam-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.grade-exam-btn:disabled {
  background: var(--button-dimmed);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  padding: 0.7rem 1rem;
  width: auto;
}

/* Exam button container */
.exam-button-container {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

/* Exam result container - holds both message and score */
.exam-result-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 1.5rem;
  padding: 1rem 1.25rem;
  background: rgba(var(--bg-quaternary-rgb), 0.3);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--accent-primary);
  transition: all 0.3s ease;
  box-shadow: var(--card-shadow);
}

/* Result message text */
.exam-result-message {
  flex: 1;
  color: var(--text-secondary);
  font-size: 1rem;
  font-style: italic;
  line-height: 1.4;
  margin: 0;
}

/* Score display */
.exam-score {
  display: inline-block;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--text-primary);
  padding: 0.6rem 1.2rem;
  border-radius: var(--border-radius);
  text-align: center;
  min-width: 80px;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

/* Results */
.correct-answer {
  border-color: var(--success-color);
  background: rgba(var(--success-color-rgb), 0.1);
}

.incorrect-answer {
  border-color: var(--danger-color);
  background: rgba(var(--danger-color-rgb), 0.1);
}

.unanswered {
  border-color: var(--warning-color);
  background: rgba(var(--warning-color-rgb), 0.1);
}

.correct-option {
  background: rgba(var(--success-color-rgb), 0.2);
  border-radius: var(--border-radius-sm);
}

.question-explanation {
  margin-top: 1rem;
  padding: 0.8rem;
  background: rgba(var(--bg-quaternary-rgb), 0.5);
  border-radius: var(--border-radius-sm);
  font-style: italic;
  color: var(--text-dimmed);
  border-left: 3px solid var(--accent-secondary);
}

.exam-result {
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(var(--bg-quaternary-rgb), 0.3);
  border-radius: var(--border-radius);
  text-align: center;
  border-left: 4px solid var(--accent-primary);
}

.exam-result p {
  color: var(--text-secondary);
  margin: 0;
  font-style: italic;
}

/* Exam Note */
.exam-note {
  margin: 1rem 0 1.5rem 0;
  padding: 1.2rem;
  background: rgba(var(--bg-tertiary-rgb), 0.4);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--accent-secondary);
}

.exam-note h4 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.exam-note p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.5;
}

/* Resources */
.exam-resources {
  margin-top: 1rem;
  padding: 0.8rem;
  background: rgba(var(--bg-quaternary-rgb), 0.3);
  border-radius: var(--border-radius-sm);
}

.exam-resources h5 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.exam-resources ul {
  list-style-type: none;
  padding-left: 0.5rem;
  margin: 0.5rem 0;
}

.exam-resources li {
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.2rem;
}

.exam-resources li:before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--accent-secondary);
}

.exam-resources a {
  color: var(--accent-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.exam-resources a:hover {
  color: var(--accent-primary);
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 768px) {
  .exam-container {
    padding: 1rem;
  }

  .exam-question {
    padding: 1rem;
  }

  .exam-title {
    font-size: 1.3rem;
  }

  .grade-exam-btn {
    width: 100%;
  }

  /* Stack result container vertically on mobile */
  .exam-result-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.75rem;
  }

  .exam-result-message {
    text-align: center;
    margin-bottom: 0.5rem;
  }

  .exam-score {
    font-size: 1.3rem;
    min-width: 70px;
  }
}
