# main.py
import sys
import time
import logging
import importlib
import re  # For regex operations

from colorama import init, Fore, Back, Style # For direct styling if needed

# Local Imports
import config # Import config first to load .env
from logging_config import setup_logging
from workflow_steps import (
    classify_query,
    handle_simple_query,
    handle_tool_query, # Handles tool ID, exec, process
    extract_modules,
    process_complex_block,
)
# tool_handler is used within workflow_steps now

# --- Package Check ---
def check_packages():
    """Checks for required packages."""
    required = ['colorama', 'tqdm', 'openai', 'dotenv']
    missing_packages = []
    for package_name in required:
        try:
            importlib.import_module(package_name)
        except ImportError:
            # Map import name to install name if different
            install_name = "python-dotenv" if package_name == "dotenv" else package_name
            missing_packages.append(install_name)

    if missing_packages:
        print("\n" + "!"*60)
        # Use basic print as logging might not be set up / colorama might be missing
        print(f"\033[91m[ERROR] Required Python packages are missing:\033[0m") # Red color
        for pkg in missing_packages:
            print(f"  - {pkg}")
        print("\nPlease install them using pip:")
        print(f"  \033[93mpip install {' '.join(missing_packages)}\033[0m") # Yellow color
        print("!"*60 + "\n")
        sys.exit(1)
    print("Required packages verified.")


# --- Helper Functions ---
def remove_unwanted_sections(text: str) -> str:
    """
    Removes "Overall Query Analysis" and "Conclusion" sections from the response.
    """
    # Remove "# Overall Query Analysis" or "## Overall Query Analysis" headers and their content
    # until the next header or end of text
    text = re.sub(r'(?i)#+ *Overall Query Analysis.*?(?=\n#|\Z)', '', text, flags=re.DOTALL)

    # Remove "# Conclusion" or "## Conclusion" headers and their content
    # until the next header or end of text
    text = re.sub(r'(?i)#+ *Conclusion.*?(?=\n#|\Z)', '', text, flags=re.DOTALL)

    # Clean up any excessive newlines that might have been created
    text = re.sub(r'\n{3,}', '\n\n', text)

    return text.strip()


# --- Multi-line Input ---
def get_multiline_input() -> str | None:
    """Handles multi-line user input until '###' or 'quit'."""
    print(f"\n{Fore.GREEN}{Style.BRIGHT}Your Query:{Style.RESET_ALL}")
    print(f"{Fore.GREEN}▼ Type your question below (end with '###' or type 'quit' to exit){Style.RESET_ALL}")

    lines = []
    while True:
        try:
            line = input()
            if line.strip().lower() == "###":
                break
            # Allow 'quit' on the first line to exit immediately
            if line.strip().lower() == "quit" and not lines:
                 return None # Signal to exit
            lines.append(line)
        except EOFError:
            logging.info("\nEOF detected, treating as end of input.")
            break # Treat EOF as end of input

    print(f"{Fore.GREEN}▲ Processing your query...{Style.RESET_ALL}")
    full_query = "\n".join(lines).strip()

    # Handle case where only 'quit' was entered after some lines
    if full_query.lower() == 'quit':
        return None

    return full_query


# --- Main Orchestration ---
def run_assistant():
    """Main loop for the study assistant."""
    log_header = lambda msg: logging.info(msg, extra={'is_header': True}) # Helper

    log_header("Study Assistant")
    logging.info(f"{Style.BRIGHT}Enter your query. Type 'quit' to exit or '###' to finish input.{Style.RESET_ALL}")

    while True:
        user_query = get_multiline_input()

        if user_query is None: # None signals quit
            logging.info("Exiting application.")
            break

        if not user_query:
            logging.warning("Empty query received. Please try again.")
            continue

        # Process the query
        log_header("Processing Query")
        overall_start_time = time.time()
        final_response = ""

        # 1. Query Classification
        classification_result = classify_query(user_query)
        query_type = classification_result.get("query_type", "complex") # Default on error
        if classification_result.get("error"):
            logging.warning(f"Classification issue: {classification_result['error']}. Defaulting to '{query_type}'.")

        logging.info(f"Query classified as '{query_type}'. Routing accordingly.")

        # 2. Decision Point & Execution
        try:
            if query_type == "simple":
                final_response = handle_simple_query(user_query)

            elif query_type == "tool":
                # Tool handling logic is now inside handle_tool_query
                final_response = handle_tool_query(user_query)
                # Check if fallback was indicated (optional, depends on handle_tool_query impl)
                # if final_response == "FALLBACK_TO_COMPLEX":
                #    logging.info("Tool identification failed, falling back to complex path.")
                #    query_type = "complex" # Re-route flag for complex processing below
                #    final_response = "" # Reset response
                # else:
                #    pass # Handled tool path successfully or returned error

            # Handle complex query type (either classified directly or via fallback)
            if query_type == "complex": # Check again in case of fallback
                logging.info("Complex Query Path Initiated...", extra={'is_step': True})
                # Check for structured modules first
                modules = extract_modules(user_query)

                if modules:
                    # Structured complex query (multiple modules)
                    logging.info(f"Processing {len(modules)} identified modules.")
                    final_response_parts = []
                    for i, (module_title, module_content) in enumerate(modules.items()):
                         log_header(f"Processing Module {i+1}/{len(modules)}: {module_title}")
                         # Each block goes through Plan -> Execute -> Synthesize via process_complex_block
                         module_result = process_complex_block(module_content, module_title)
                         final_response_parts.append(module_result)

                    # Combine module results
                    logging.info("Combining module results...", extra={'is_step': True})
                    query_title_preview = user_query.split('\n')[0][:60].strip()
                    final_response_header = f"# Study Guide: {query_title_preview}...\n\n" # Overall title
                    final_response = final_response_header + "\n\n---\n\n".join(final_response_parts)
                    logging.info(f"Combined {len(final_response_parts)} module results.", extra={'is_success': True})
                else:
                    # Unstructured complex query (single block)
                    logging.info("Processing the entire query as a single complex block.")
                    query_title = "Study Notes" # Changed from "Overall Query Analysis"
                    # Use process_complex_block for the entire query
                    final_response = process_complex_block(user_query, query_title)
                    # Ensure final response for single block has a title (synthesizer should add it, but fallback)
                    if not final_response.startswith("# ") and not final_response.strip().startswith("Error:"):
                         query_preview = user_query[:60].strip()
                         final_response = f"# Study Notes: {query_preview}...\n\n" + final_response


        except Exception as e:
             logging.error(f"An unexpected error occurred during query processing: {e}", exc_info=config.DEBUG_MODE)
             final_response = f"Error: An unexpected critical error occurred during processing.\nDetails: {e}"


        # 3. Deliver Response
        logging.info("Delivering response to user...", extra={'is_step': True})
        overall_end_time = time.time()
        total_time = overall_end_time - overall_start_time
        logging.info(f"[TIMING] Total query processing took {total_time:.2f}s (via '{query_type}' path)", extra={'is_timing': True})

        # Remove "Overall Query Analysis" and "Conclusion" sections from the response
        final_response = remove_unwanted_sections(final_response)

        log_header("Answer")
        print(f"{Fore.CYAN}{Style.BRIGHT}Response:{Style.RESET_ALL}")
        print(final_response) # Print the final assembled response
        print(f"\n{Fore.GREEN}Ready for your next question.{Style.RESET_ALL}")


# --- Entry Point ---
if __name__ == "__main__":
    # Initialize colorama for colored output across platforms
    init(autoreset=True)

    # Check dependencies first
    check_packages()

    # Setup logging using the configuration from config.py
    setup_logging(config.DEBUG_MODE)
    logging.info(f"Debug mode: {'Enabled' if config.DEBUG_MODE else 'Disabled'}")

    # Verify necessary clients are initialized based on config
    providers_needed = config.PROVIDERS_USED
    clients_ok = True
    for provider in providers_needed:
        if not config.check_client_availability(provider):
            logging.error(f"Client for required provider '{provider}' is not available. Check API key ({provider.upper()}_API_KEY) and initialization logs.")
            clients_ok = False

    if clients_ok:
        logging.info("API clients for configured providers appear available.", extra={'is_success': True})
        run_assistant() # Start the main application loop
    else:
        logging.critical("Exiting due to missing API keys or client initialization errors for configured providers.")
        print(f"\n{Back.RED}{Fore.WHITE}{Style.BRIGHT} CONFIGURATION ERROR - Check Logs {Style.RESET_ALL}")
        sys.exit(1)