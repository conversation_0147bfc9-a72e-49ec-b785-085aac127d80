"""
Proveedor de LLM para Google Gemini.
"""
from django.conf import settings
import logging
from typing import Dict, List, Optional, Any, Union
import google.generativeai as genai
from PIL import Image
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langchain_core.messages import HumanMessage, AIMessage

from .base import BaseLLMProvider, format_exam_query

logger = logging.getLogger(__name__)

class GeminiProvider(BaseLLMProvider):
    """Implementación del proveedor de LLM para Google Gemini."""

    # Modelos disponibles de Gemini
    AVAILABLE_MODELS = {
        # Gemini 2.x
        'gemini-2.5-pro-preview': {
            'id': 'models/gemini-2.5-pro-preview-03-25',
            'capabilities': ['text', 'code', 'reasoning', 'multimodal'],
            'max_tokens': 32768,
            'description': 'Modelo avanzado con razonamiento y comprensión multimodal'
        },
        'gemini-2.5-flash': {
            'id': 'models/gemini-2.5-flash-preview-05-20',
            'capabilities': ['text', 'code', 'multimodal'],
            'max_tokens': 16384,
            'description': 'Gemini 2.5 Flash - Modelo rápido y eficiente con capacidades multimodales'
        },

        'gemini-2.0-flash': {
            'id': 'models/gemini-2.0-flash',
            'capabilities': ['text', 'code', 'multimodal'],
            'max_tokens': 16384,
            'description': 'Modelo rápido con capacidades multimodales'
        },
        'gemini-2.0-flash-lite': {
            'id': 'models/gemini-2.0-flash-lite',
            'capabilities': ['text', 'code'],
            'max_tokens': 16384,
            'description': 'Versión ligera y eficiente de Gemini 2.0 Flash'
        },
        # Gemini 1.x
        'gemini-1.5-pro': {
            'id': 'models/gemini-1.5-pro',
            'capabilities': ['text', 'code', 'reasoning', 'multimodal'],
            'max_tokens': 32768,
            'description': 'Modelo potente con contexto extenso y razonamiento complejo'
        },
        'gemini-1.5-flash': {
            'id': 'models/gemini-1.5-flash',
            'capabilities': ['text', 'code', 'multimodal'],
            'max_tokens': 16384,
            'description': 'Modelo rápido con buen equilibrio entre velocidad y capacidad'
        }
    }

    def __init__(self):
        """Inicializa el proveedor de Gemini."""
        self.api_key = settings.GEMINI_API_KEY
        if not self.api_key:
            logger.warning("GEMINI_API_KEY no está configurada. Asegúrate de configurar esta variable de entorno.")

        # Registrar información para depuración
        logger.info(f"Usando clave API de Gemini: {self.api_key[:10]}...")

        try:
            # Configurar la API de Gemini
            genai.configure(api_key=self.api_key)

            # Intentar listar modelos para verificar que la API key funciona
            models = genai.list_models()
            logger.info(f"Modelos Gemini disponibles: {[m.name for m in models]}")
        except Exception as e:
            logger.error(f"Error al configurar Gemini API: {e}")
            logger.warning("Usando fallback a Grok debido a error en la API de Gemini")
            # No lanzar excepción para permitir fallback a otros proveedores

        # Obtener el modelo activo de la configuración
        self._model_name = settings.ACTIVE_GEMINI_MODEL

        # Registrar información para depuración
        logger.info(f"Inicializando GeminiProvider con modelo: {self._model_name}")
        logger.info(f"ACTIVE_GEMINI_MODEL en settings: {settings.ACTIVE_GEMINI_MODEL}")

        # Verificar que el modelo exista en la lista de modelos disponibles
        if self._model_name not in self.AVAILABLE_MODELS:
            logger.warning(f"Modelo Gemini '{self._model_name}' no reconocido. Usando gemini-1.5-pro como fallback.")
            self._model_name = 'gemini-1.5-pro'

        # Configuración del modelo
        self.temperature = 0.7
        self.max_tokens = self.AVAILABLE_MODELS[self._model_name]['max_tokens']

    def set_model(self, model_name: str) -> None:
        """
        Configura el modelo Gemini a utilizar.

        Args:
            model_name: Nombre del modelo Gemini a utilizar.
        """
        if model_name in self.AVAILABLE_MODELS:
            self._model_name = model_name
            self.max_tokens = self.AVAILABLE_MODELS[model_name]['max_tokens']
            logger.info(f"Modelo Gemini cambiado a: {model_name}")
        else:
            logger.warning(f"Modelo Gemini '{model_name}' no reconocido. Manteniendo {self._model_name}.")

    @property
    def model_name(self) -> str:
        """Nombre del modelo utilizado."""
        return self._model_name

    def query(self, user_input: str, memory_instance: ConversationBufferWindowMemory) -> str:
        """
        Consulta el modelo Gemini con la entrada del usuario y la memoria de conversación.

        Args:
            user_input: Texto de entrada del usuario.
            memory_instance: Instancia de memoria de conversación.

        Returns:
            Respuesta del modelo como texto.
        """
        try:
            # Verificar que la clave API esté configurada
            if not self.api_key:
                logger.error("GEMINI_API_KEY no está configurada. Configura esta variable de entorno.")
                return "Error de configuración: La clave API de Gemini no está configurada. Por favor, contacta al administrador del sistema."

            # Obtener el ID real del modelo
            model_id = self.AVAILABLE_MODELS[self._model_name]['id']
            logger.info(f"Consultando modelo Gemini: {model_id}")

            try:
                # Crear el modelo generativo
                model = genai.GenerativeModel(model_id)

                # Obtener mensajes de la memoria
                chat_history = memory_instance.chat_memory.messages

                # Iniciar una conversación de chat
                chat = model.start_chat()

                # Agregar mensajes de la memoria al historial de chat
                for message in chat_history:
                    if hasattr(message, 'type'):
                        if message.type == 'human':
                            chat.send_message(message.content)
                        # No enviamos los mensajes del asistente, ya que el modelo los generará
                    # Compatibilidad con LangChain 0.1.0+
                    elif hasattr(message, 'content'):
                        if isinstance(message, HumanMessage):
                            chat.send_message(message.content)
                        # No enviamos los mensajes del asistente, ya que el modelo los generará

                # Enviar el mensaje actual del usuario
                response = chat.send_message(user_input)

                # Extraer la respuesta
                result = response.text

                # Actualizar la memoria con la respuesta
                memory_instance.chat_memory.add_user_message(user_input)
                memory_instance.chat_memory.add_ai_message(result)

                return result

            except Exception as e:
                error_str = str(e)
                logger.error(f"Error al usar Gemini API: {error_str}")

                # Mensaje personalizado para errores comunes
                if "quota" in error_str.lower() or "429" in error_str:
                    error_msg = "Lo siento, se ha excedido la cuota de la API de Gemini. Este es un límite de la versión gratuita. Por favor, intenta con otro modelo o proveedor, o inténtalo de nuevo más tarde."
                elif "invalid" in error_str.lower() and "api key" in error_str.lower():
                    error_msg = "Error de configuración: La clave API de Gemini no es válida. Por favor, verifica tu clave API."
                else:
                    error_msg = f"Lo siento, hubo un problema al conectar con el modelo Gemini. Error: {error_str}"

                memory_instance.chat_memory.add_user_message(user_input)
                memory_instance.chat_memory.add_ai_message(error_msg)
                return error_msg

        except Exception as e:
            # Capturar todos los errores de la API
            logger.error(f"Error al consultar Gemini: {e}")
            return f"Error: No se pudo completar la solicitud al modelo de lenguaje. Detalles: {str(e)}"

    def query_with_image(self, user_input: str, image_path: str, memory_instance: ConversationBufferWindowMemory) -> str:
        """
        Consulta el modelo Gemini con texto e imagen.

        Args:
            user_input: Texto de entrada del usuario.
            image_path: Ruta a la imagen.
            memory_instance: Instancia de memoria de conversación.

        Returns:
            Respuesta del modelo como texto.
        """
        try:
            # Verificar que la clave API esté configurada
            if not self.api_key:
                logger.error("GEMINI_API_KEY no está configurada. Configura esta variable de entorno.")
                return "Error de configuración: La clave API de Gemini no está configurada. Por favor, contacta al administrador del sistema."

            # Obtener el ID real del modelo
            model_id = self.AVAILABLE_MODELS[self._model_name]['id']

            # Crear el modelo generativo
            model = genai.GenerativeModel(model_id)

            # Cargar la imagen
            image = Image.open(image_path)

            # Crear la consulta multimodal
            response = model.generate_content([user_input, image])

            # Extraer la respuesta
            result = response.text

            # Actualizar la memoria con la consulta y respuesta
            memory_instance.chat_memory.add_user_message(f"{user_input} [Imagen adjunta]")
            memory_instance.chat_memory.add_ai_message(result)

            return result

        except Exception as e:
            # Capturar todos los errores de la API
            logger.error(f"Error al consultar Gemini con imagen: {e}")
            return f"Error: No se pudo completar la solicitud al modelo de lenguaje con la imagen. Detalles: {str(e)}"

    def format_exam_query(self, user_input: str) -> str:
        """
        Formatea la entrada del usuario para la generación de exámenes.

        Args:
            user_input: Texto de entrada del usuario.

        Returns:
            Prompt formateado para la generación de exámenes.
        """
        return format_exam_query(user_input)

    def get_model_info(self) -> Dict[str, Any]:
        """
        Obtiene información sobre el modelo.

        Returns:
            Diccionario con información del modelo.
        """
        model_info = self.AVAILABLE_MODELS.get(self._model_name, {})
        return {
            "name": self._model_name,
            "provider": "Google Gemini",
            "max_tokens": model_info.get('max_tokens', 16384),
            "temperature": self.temperature,
            "capabilities": model_info.get('capabilities', []),
            "description": model_info.get('description', '')
        }

    @classmethod
    def get_available_models(cls) -> Dict[str, Dict[str, Any]]:
        """
        Obtiene la lista de modelos disponibles.

        Returns:
            Diccionario con información de los modelos disponibles.
        """
        return cls.AVAILABLE_MODELS
